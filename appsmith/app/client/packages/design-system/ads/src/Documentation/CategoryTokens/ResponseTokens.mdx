import { Meta } from "@storybook/blocks";
import { ColorBlock } from "../components/ColorBlock";
import { CodeBlock } from "../components/CodeBlock";
import { Toast } from "../../Toast";

<Meta title="ADS/Docs/Category Tokens/Response Tokens" name="Response Tokens" />

#### Response

Response components furnish users with feedback based on their interactions or actions within the application. Their purpose is to convey outcomes of user actions, system status updates, and error messages.

---

<table>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-error-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-error-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-info-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-info-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-success-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-success-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-warning-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-warning-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-error-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-error-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-info-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-info-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-success-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-success-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-warning-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-warning-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-response-surface-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-response-surface-default-border" />
    </td>
  </tr>
</table>

<Toast />
