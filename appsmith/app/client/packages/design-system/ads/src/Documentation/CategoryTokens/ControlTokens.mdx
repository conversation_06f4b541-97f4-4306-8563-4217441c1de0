import { Meta } from "@storybook/blocks";
import { ColorBlock } from "../components/ColorBlock";
import { CodeBlock } from "../components/CodeBlock";
import { Toast } from "../../Toast";

<Meta title="ADS/Docs/Category Tokens/Control Tokens" name="Control Tokens" />

#### Control

Control components provide tools for users to manage and manipulate the application or its content. They include input, select, Radio, checkbox, and other UI elements that allow users to manage and manipulate data.

---

<table>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-checked-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-checked-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-checked-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-checked-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-knob-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-knob-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-track-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-track-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-pill-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-pill-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-default-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-hover-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-hover-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-active-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-active-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-checked-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-checked-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-checked-hover-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-checked-hover-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-field-error-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-field-error-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-helper-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-helper-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-helper-error-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-helper-error-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-icon-checked-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-icon-checked-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-icon-error-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-icon-error-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-placeholder-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-placeholder-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-value-checked-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-value-checked-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-value-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-value-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-control-value-inactive-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-control-value-inactive-fg" />
    </td>
  </tr>
</table>

<Toast />
