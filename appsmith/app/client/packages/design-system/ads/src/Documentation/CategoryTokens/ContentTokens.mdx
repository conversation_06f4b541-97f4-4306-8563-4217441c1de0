import { Meta } from "@storybook/blocks";
import { ColorBlock } from "../components/ColorBlock";
import { CodeBlock } from "../components/CodeBlock";
import { Toast } from "../../Toast";

<Meta title="ADS/Docs/Category Tokens/Content Tokens" name="Content Tokens" />

#### Content

Content components serve as the primary vehicles for presenting essential information and visual content to users. They showcase data, textual content, images, and multimedia elements that constitute the core essence of the user experience. Instances include text, callouts, banner and various forms of visual and textual content.

---

<table>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-helper-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-helper-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-inactive-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-inactive-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-error-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-error-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-warning-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-warning-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-success-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-success-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-info-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-info-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-special-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-special-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-label-premium-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-label-premium-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-active-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-active-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-error-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-error-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-success-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-success-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-info-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-info-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-neutral-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-neutral-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-warning-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-warning-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-special-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-special-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-premium-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-premium-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-default-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-info-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-info-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-success-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-success-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-error-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-error-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-warning-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-warning-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-special-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-special-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-surface-premium-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-surface-premium-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-content-container-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-content-container-default-border" />
    </td>
  </tr>
</table>

<Toast />
