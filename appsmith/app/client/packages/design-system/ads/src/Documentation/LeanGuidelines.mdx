import { Meta } from "@storybook/blocks";

<Meta title="ADS/Docs/Lean guidelines" />

# Lean guidelines

Components are the lego blocks that you get to build your system. Use these to compose higher order components and compose your UI. All our categories can be broadly grouped into 4 categories. Think about what is the purpose of your design before you get to selecting a component.

## Displaying & organising information

- [Text](https://design-system.appsmith.com/?path=/docs/design-system-text--documentation)
- [Tab](https://design-system.appsmith.com/?path=/docs/design-system-tab--documentation)
- [Menu](https://design-system.appsmith.com/?path=/docs/design-system-menu--documentation)
- [Tooltip](https://design-system.appsmith.com/?path=/docs/design-system-tooltip--documentation)
- [Icon](https://design-system.appsmith.com/?path=/docs/design-system-icon--documentation)
- [Avatar and avatar group](https://design-system.appsmith.com/?path=/docs/design-system-avatar--documentation)
- [Divider](https://design-system.appsmith.com/?path=/docs/design-system-divider--documentation)
- [Callout](https://design-system.appsmith.com/?path=/docs/design-system-callout--documentation) (information kind)
- [Spinner](https://design-system.appsmith.com/?path=/docs/design-system-spinner--documentation)

## Taking user input

Uses the UI category tokens of “input controls” to construct the atomic components in this category. Tokens start with `ui.control`.

- [Input fields](https://design-system.appsmith.com/?path=/docs/design-system-input--documentation)
- [Switch](https://design-system.appsmith.com/?path=/docs/design-system-switch--documentation)
- [Checkbox](https://design-system.appsmith.com/?path=/docs/design-system-checkbox--documentation)
- [Segmented control](https://design-system.appsmith.com/?path=/docs/design-system-segmented-control--documentation)
- [Select](https://design-system.appsmith.com/?path=/docs/design-system-select--documentation)
- [Radio button](https://design-system.appsmith.com/?path=/docs/design-system-radio--documentation)
- [Toggle button](https://design-system.appsmith.com/?path=/docs/design-system-text--documentation)

## Doing an action

Uses the UI category tokens of “action” to construct the atomic components. Tokens start with `ui.action`.

- [Button and iconbutton](https://design-system.appsmith.com/?path=/docs/design-system-button--documentation)
- [Link](https://design-system.appsmith.com/?path=/docs/design-system-link--documentation)
- [Modal](https://design-system.appsmith.com/?path=/docs/design-system-modal--documentation)

## Displaying the system's response

Uses the UI category tokens of “response” to construct the atomic components. Tokens start with `ui.response`.

- [Callout](https://design-system.appsmith.com/?path=/docs/design-system-callout--documentation) (dismissable kinds)
- [Toast](https://design-system.appsmith.com/?path=/docs/design-system-toast--documentation)
