import { Meta } from "@storybook/blocks";
import { ColorBlock } from "../components/ColorBlock";
import { CodeBlock } from "../components/CodeBlock";
import { Toast } from "../../Toast";

<Meta title="ADS/Docs/Category Tokens/Action Tokens" name="Action Tokens" />

#### Action

They're designed to initiate specific events or requests that trigger changes in the application's state or data. Examples encompass buttons, links, and other elements that empower users to take actions.

---

<table>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-surface-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-surface-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-surface-active-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-surface-active-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-surface-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-surface-default-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-surface-hover-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-surface-hover-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-surface-active-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-surface-active-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-primary-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-primary-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-surface-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-surface-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-surface-active-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-surface-active-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-surface-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-surface-default-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-surface-hover-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-surface-hover-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-surface-active-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-surface-active-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-secondary-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-secondary-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-tertiary-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-tertiary-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-tertiary-surface-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-tertiary-surface-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-tertiary-surface-active-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-tertiary-surface-active-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-tertiary-surface-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-tertiary-surface-default-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-tertiary-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-tertiary-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-tertiary-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-tertiary-icon-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-error-surface-default-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-error-surface-default-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-error-surface-hover-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-error-surface-hover-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-error-surface-active-bg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-error-surface-active-bg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-error-surface-default-border" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-error-surface-default-border" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-error-label-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-error-label-default-fg" />
    </td>
  </tr>
  <tr>
    <td>
      <CodeBlock code="--ads-v2-colors-action-error-icon-default-fg" />
    </td>
    <td>
      <ColorBlock color="--ads-v2-colors-action-error-icon-default-fg" />
    </td>
  </tr>
</table>

<Toast />
