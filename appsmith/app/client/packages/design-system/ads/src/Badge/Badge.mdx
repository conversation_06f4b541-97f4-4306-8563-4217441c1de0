import { <PERSON><PERSON>, <PERSON>a } from "@storybook/blocks";

import * as BadgeStories from "./Badge.stories";

<Meta of={BadgeStories} />

# Badge

A Badge component is a small visual element used to display additional information, typically in the form of status, count, or notification.

<Canvas of={BadgeStories.BadgeStory} />

## Usage

Badge component often used to enhance the user's understanding of a piece of content or interface element.
