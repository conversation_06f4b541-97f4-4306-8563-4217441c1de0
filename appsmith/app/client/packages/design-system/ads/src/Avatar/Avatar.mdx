import { <PERSON><PERSON>, <PERSON>a } from "@storybook/blocks";

import * as AvatarStories from "./Avatar.stories";
import * as AvatarGroupStories from "./AvatarGroup.stories";

<Meta of={AvatarStories} />

# Avatar

Avatars are used in the interface to display a small thumbnail representation of individuals or names.

<Canvas of={AvatarStories.AvatarStory} />

In case there is an error loading the avatar image, the component will fallback to an alternative option in the following order:

- The first letter as supplied in the `firstLetter` prop
- The first letter of the label
- A generic avatar icon

## Kind

Image

<Canvas of={AvatarStories.AvatarStory} />

Initials

<Canvas of={AvatarStories.AvatarInitialStory} />

## Size

Small (24px)

<Canvas of={AvatarStories.AvatarSmallStory} />

Medium (32px)

<Canvas of={AvatarStories.AvatarMediumStory} />

# Avatar Group

An avatar group is a collection of avatars that are displayed stacked on top of each other.
Stacked groups are limited to five avatars and should only be used with medium or small avatars.

<Canvas of={AvatarGroupStories.AvatarGroupStory} />
