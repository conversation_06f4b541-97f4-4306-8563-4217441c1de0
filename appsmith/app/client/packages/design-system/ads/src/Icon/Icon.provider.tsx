/* eslint-disable no-console */
import React from "react";
import { importRemixIcon, importSvg } from "./loadables";

const AddMoreIcon = importRemixIcon(
  async () => import("remixicon-react/AddCircleLineIcon"),
);
const AddMoreFillIcon = importRemixIcon(
  async () => import("remixicon-react/AddCircleFillIcon"),
);
const ArrowGoForwardLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowGoForwardLineIcon"),
);
const ArrowGoBackLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowGoBackLineIcon"),
);
const ArrowLeftRightIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowLeftRightLineIcon"),
);
const ArrowDownLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowDownLineIcon"),
);
const BookIcon = importRemixIcon(
  async () => import("remixicon-react/BookOpenLineIcon"),
);
const BugLineIcon = importRemixIcon(
  async () => import("remixicon-react/BugLineIcon"),
);
const ChevronRight = importRemixIcon(
  async () => import("remixicon-react/ArrowRightSFillIcon"),
);
const CheckLineIcon = importRemixIcon(
  async () => import("remixicon-react/CheckLineIcon"),
);
const CloseLineIcon = importRemixIcon(
  async () => import("remixicon-react/CloseLineIcon"),
);
const CloseCircleIcon = importRemixIcon(
  async () => import("remixicon-react/CloseCircleFillIcon"),
);
const CloudOfflineIcon = importRemixIcon(
  async () => import("remixicon-react/CloudOffLineIcon"),
);
const CloudLineIcon = importRemixIcon(
  async () => import("remixicon-react/CloudLineIcon"),
);
const CommentContextMenu = importRemixIcon(
  async () => import("remixicon-react/More2FillIcon"),
);
const More2FillIcon = importRemixIcon(
  async () => import("remixicon-react/More2FillIcon"),
);
const CompassesLine = importRemixIcon(
  async () => import("remixicon-react/CompassesLineIcon"),
);
const ContextMenuIcon = importRemixIcon(
  async () => import("remixicon-react/MoreFillIcon"),
);
const CreateNewIcon = importRemixIcon(
  async () => import("remixicon-react/AddLineIcon"),
);
const Database2Line = importRemixIcon(
  async () => import("remixicon-react/Database2LineIcon"),
);
const DatasourceIcon = importRemixIcon(
  async () => import("remixicon-react/CloudFillIcon"),
);
const DeleteBin7 = importRemixIcon(
  async () => import("remixicon-react/DeleteBin7LineIcon"),
);
const DeviceLineIcon = importRemixIcon(
  async () => import("remixicon-react/DeviceLineIcon"),
);
const DiscordIcon = importRemixIcon(
  async () => import("remixicon-react/DiscordLineIcon"),
);
const DownArrow = importRemixIcon(
  async () => import("remixicon-react/ArrowDownSFillIcon"),
);
const Download = importRemixIcon(
  async () => import("remixicon-react/DownloadCloud2LineIcon"),
);
const DuplicateIcon = importRemixIcon(
  async () => import("remixicon-react/FileCopyLineIcon"),
);
const PencilFillIcon = importRemixIcon(
  async () => import("remixicon-react/PencilFillIcon"),
);
const EditLineIcon = importRemixIcon(
  async () => import("remixicon-react/EditLineIcon"),
);
const Edit2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Edit2LineIcon"),
);
const EditUnderlineIcon = importRemixIcon(
  async () => import("remixicon-react/EditLineIcon"),
);
const Emoji = importRemixIcon(
  async () => import("remixicon-react/EmotionLineIcon"),
);
const ExpandMore = importRemixIcon(
  async () => import("remixicon-react/ArrowDownSLineIcon"),
);
const DownArrowIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowDownSLineIcon"),
);
const ExpandLess = importRemixIcon(
  async () => import("remixicon-react/ArrowUpSLineIcon"),
);
const EyeOn = importRemixIcon(
  async () => import("remixicon-react/EyeLineIcon"),
);
const EyeOff = importRemixIcon(
  async () => import("remixicon-react/EyeOffLineIcon"),
);
const FileTransfer = importRemixIcon(
  async () => import("remixicon-react/FileTransferLineIcon"),
);
const FileLine = importRemixIcon(
  async () => import("remixicon-react/FileLineIcon"),
);
const Filter = importRemixIcon(
  async () => import("remixicon-react/Filter2FillIcon"),
);
const ForbidLineIcon = importRemixIcon(
  async () => import("remixicon-react/ForbidLineIcon"),
);
const GiftLineIcon = importRemixIcon(
  async () => import("remixicon-react/GiftLineIcon"),
);
const GitMerge = importRemixIcon(
  async () => import("remixicon-react/GitMergeLineIcon"),
);
const GitCommit = importRemixIcon(
  async () => import("remixicon-react/GitCommitLineIcon"),
);
const GitPullRequest = importRemixIcon(
  async () => import("remixicon-react/GitPullRequestLineIcon"),
);
const GitRepository = importRemixIcon(
  async () => import("remixicon-react/GitRepositoryLineIcon"),
);
const GlobalLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/globe-simple.svg"),
);
const GuideIcon = importRemixIcon(
  async () => import("remixicon-react/GuideFillIcon"),
);
const HistoryLineIcon = importRemixIcon(
  async () => import("remixicon-react/HistoryLineIcon"),
);
const QuestionMarkIcon = importRemixIcon(
  async () => import("remixicon-react/QuestionMarkIcon"),
);
const LightbulbFlashLine = importRemixIcon(
  async () => import("remixicon-react/LightbulbFlashLineIcon"),
);
const LinksLineIcon = importRemixIcon(
  async () => import("remixicon-react/LinksLineIcon"),
);
const LinkUnlinkIcon = importRemixIcon(
  async () => import("remixicon-react/LinkUnlinkIcon"),
);
const InfoIcon = importRemixIcon(
  async () => import("remixicon-react/InformationLineIcon"),
);
const KeyIcon = importRemixIcon(
  async () => import("remixicon-react/Key2LineIcon"),
);
const LeftArrowIcon2 = importRemixIcon(
  async () => import("remixicon-react/ArrowLeftSLineIcon"),
);
const Link2 = importRemixIcon(async () => import("remixicon-react/LinkIcon"));
const LeftArrowIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowLeftLineIcon"),
);
const NewsPaperLine = importRemixIcon(
  async () => import("remixicon-react/NewspaperLineIcon"),
);
const OvalCheck = importRemixIcon(
  async () => import("remixicon-react/CheckboxCircleLineIcon"),
);
const OvalCheckFill = importRemixIcon(
  async () => import("remixicon-react/CheckboxCircleFillIcon"),
);
const Pin3 = importRemixIcon(
  async () => import("remixicon-react/Pushpin2FillIcon"),
);
const PlayCircleLineIcon = importRemixIcon(
  async () => import("remixicon-react/PlayCircleLineIcon"),
);
const QueryIcon = importRemixIcon(
  async () => import("remixicon-react/CodeSSlashLineIcon"),
);
const SubtractLine = importRemixIcon(
  async () => import("remixicon-react/SubtractLineIcon"),
);
const RightArrowIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowRightLineIcon"),
);
const RightArrowIcon2 = importRemixIcon(
  async () => import("remixicon-react/ArrowRightSLineIcon"),
);
const RocketIcon = importRemixIcon(
  async () => import("remixicon-react/RocketLineIcon"),
);
const RobotIcon = importRemixIcon(
  async () => import("remixicon-react/RobotLineIcon"),
);
const Save2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Save2LineIcon"),
);
const SearchIcon = importRemixIcon(
  async () => import("remixicon-react/SearchLineIcon"),
);
const SortAscIcon = importRemixIcon(
  async () => import("remixicon-react/SortAscIcon"),
);
const SortDescIcon = importRemixIcon(
  async () => import("remixicon-react/SortDescIcon"),
);
const ShareBoxLineIcon = importRemixIcon(
  async () => import("remixicon-react/ShareBoxLineIcon"),
);
const ShareBoxFillIcon = importRemixIcon(
  async () => import("remixicon-react/ShareBoxFillIcon"),
);
const ShareForwardIcon = importRemixIcon(
  async () => import("remixicon-react/ShareForwardFillIcon"),
);
const Trash = importRemixIcon(
  async () => import("remixicon-react/DeleteBinLineIcon"),
);
const UpArrow = importRemixIcon(
  async () => import("remixicon-react/ArrowUpSFillIcon"),
);
const WarningIcon = importRemixIcon(
  async () => import("remixicon-react/ErrorWarningFillIcon"),
);
const WarningLineIcon = importRemixIcon(
  async () => import("remixicon-react/ErrorWarningLineIcon"),
);
const LoginIcon = importRemixIcon(
  async () => import("remixicon-react/LoginBoxLineIcon"),
);
const LogoutIcon = importRemixIcon(
  async () => import("remixicon-react/LogoutBoxRLineIcon"),
);
const ShareLineIcon = importRemixIcon(
  async () => import("remixicon-react/ShareLineIcon"),
);
const LoaderLineIcon = importRemixIcon(
  async () => import("remixicon-react/LoaderLineIcon"),
);
const WidgetIcon = importRemixIcon(
  async () => import("remixicon-react/FunctionLineIcon"),
);
const RefreshLineIcon = importRemixIcon(
  async () => import("remixicon-react/RefreshLineIcon"),
);
const GitBranchLineIcon = importRemixIcon(
  async () => import("remixicon-react/GitBranchLineIcon"),
);
const EditBoxLineIcon = importRemixIcon(
  async () => import("remixicon-react/EditBoxLineIcon"),
);
const StarLineIcon = importRemixIcon(
  async () => import("remixicon-react/StarLineIcon"),
);
const StarFillIcon = importRemixIcon(
  async () => import("remixicon-react/StarFillIcon"),
);
const Settings2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Settings2LineIcon"),
);
const DownloadIcon = importRemixIcon(
  async () => import("remixicon-react/DownloadLineIcon"),
);
const UploadCloud2LineIcon = importRemixIcon(
  async () => import("remixicon-react/UploadCloud2LineIcon"),
);
const DownloadLineIcon = importRemixIcon(
  async () => import("remixicon-react/DownloadLineIcon"),
);
const UploadLineIcon = importRemixIcon(
  async () => import("remixicon-react/UploadLineIcon"),
);
const FileListLineIcon = importRemixIcon(
  async () => import("remixicon-react/FileListLineIcon"),
);
const HamburgerIcon = importRemixIcon(
  async () => import("remixicon-react/MenuLineIcon"),
);
const MagicLineIcon = importRemixIcon(
  async () => import("remixicon-react/MagicLineIcon"),
);
const UserHeartLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserHeartLineIcon"),
);
const DvdLineIcon = importRemixIcon(
  async () => import("remixicon-react/DvdLineIcon"),
);
const GridLineIcon = importRemixIcon(
  async () => import("remixicon-react/GridLineIcon"),
);
const Group2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Group2LineIcon"),
);
const CodeViewIcon = importRemixIcon(
  async () => import("remixicon-react/CodeViewIcon"),
);
const GroupLineIcon = importRemixIcon(
  async () => import("remixicon-react/GroupLineIcon"),
);
const ArrowRightUpLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowRightUpLineIcon"),
);
const MailCheckLineIcon = importRemixIcon(
  async () => import("remixicon-react/MailCheckLineIcon"),
);
const UserFollowLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserFollowLineIcon"),
);
const AddBoxLineIcon = importRemixIcon(
  async () => import("remixicon-react/AddBoxLineIcon"),
);
const ArrowRightSFillIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowRightSFillIcon"),
);
const ArrowDownSFillIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowDownSFillIcon"),
);
const MailLineIcon = importRemixIcon(
  async () => import("remixicon-react/MailLineIcon"),
);
const LockPasswordLineIcon = importRemixIcon(
  async () => import("remixicon-react/LockPasswordLineIcon"),
);
const Timer2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Timer2LineIcon"),
);
const MapPin2LineIcon = importRemixIcon(
  async () => import("remixicon-react/MapPin2LineIcon"),
);
const User3LineIcon = importRemixIcon(
  async () => import("remixicon-react/User3LineIcon"),
);
const User2LineIcon = importRemixIcon(
  async () => import("remixicon-react/User2LineIcon"),
);
const Key2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Key2LineIcon"),
);
const FileList2LineIcon = importRemixIcon(
  async () => import("remixicon-react/FileList2LineIcon"),
);
const Lock2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Lock2LineIcon"),
);
const SearchEyeLineIcon = importRemixIcon(
  async () => import("remixicon-react/SearchEyeLineIcon"),
);
const AlertLineIcon = importRemixIcon(
  async () => import("remixicon-react/AlertLineIcon"),
);
const AlertFillIcon = importRemixIcon(
  async () => import("remixicon-react/AlertFillIcon"),
);
const InfoFillIcon = importRemixIcon(
  async () => import("remixicon-react/InformationFillIcon"),
);
const SettingsLineIcon = importRemixIcon(
  async () => import("remixicon-react/SettingsLineIcon"),
);
const LockUnlockLineIcon = importRemixIcon(
  async () => import("remixicon-react/LockUnlockLineIcon"),
);
const LockFillIcon = importRemixIcon(
  async () => import("remixicon-react/LockFillIcon"),
);
const PantoneLineIcon = importRemixIcon(
  async () => import("remixicon-react/PantoneLineIcon"),
);
const QuestionFillIcon = importRemixIcon(
  async () => import("remixicon-react/QuestionFillIcon"),
);
const QuestionLineIcon = importRemixIcon(
  async () => import("remixicon-react/QuestionLineIcon"),
);
const UserSharedLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserSharedLineIcon"),
);
const UserStarLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserStarLineIcon"),
);
const UserReceived2LineIcon = importRemixIcon(
  async () => import("remixicon-react/UserReceived2LineIcon"),
);
const UserAddLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserAddLineIcon"),
);
const UserUnfollowLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserUnfollowLineIcon"),
);
const UserSettingsLineIcon = importRemixIcon(
  async () => import("remixicon-react/UserSettingsLineIcon"),
);
const DeleteRowIcon = importRemixIcon(
  async () => import("remixicon-react/DeleteRowIcon"),
);
const ArrowUpLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowUpLineIcon"),
);
const MoneyDollarCircleLineIcon = importRemixIcon(
  async () => import("remixicon-react/MoneyDollarCircleLineIcon"),
);
const IncreaseV2Icon = importRemixIcon(
  async () => import("remixicon-react/AddLineIcon"),
);
const CopyIcon = importRemixIcon(
  async () => import("remixicon-react/FileCopyLineIcon"),
);
const QuestionIcon = importRemixIcon(
  async () => import("remixicon-react/QuestionLineIcon"),
);
const SettingsIcon = importRemixIcon(
  async () => import("remixicon-react/Settings5LineIcon"),
);
const EyeIcon = importRemixIcon(
  async () => import("remixicon-react/EyeLineIcon"),
);
const EyeOffIcon = importRemixIcon(
  async () => import("remixicon-react/EyeOffLineIcon"),
);
const CloseIcon = importRemixIcon(
  async () => import("remixicon-react/CloseLineIcon"),
);
const SubtractIcon = importRemixIcon(
  async () => import("remixicon-react/SubtractFillIcon"),
);
const ArrowLeftLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowLeftLineIcon"),
);
const ArrowRightLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowRightLineIcon"),
);
const CloseCircleLineIcon = importRemixIcon(
  async () => import("remixicon-react/CloseCircleLineIcon"),
);
const ArrowLeftSLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowLeftSLineIcon"),
);
const ArrowRightSLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowRightSLineIcon"),
);
const ArrowDownSLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowDownSLineIcon"),
);
const ArrowUpSLineIcon = importRemixIcon(
  async () => import("remixicon-react/ArrowUpSLineIcon"),
);
const AccountBoxLineIcon = importRemixIcon(
  async () => import("remixicon-react/AccountBoxLineIcon"),
);
const AccountCircleLineIcon = importRemixIcon(
  async () => import("remixicon-react/AccountCircleLineIcon"),
);
const AddLineIcon = importRemixIcon(
  async () => import("remixicon-react/AddLineIcon"),
);
const SearchLineIcon = importRemixIcon(
  async () => import("remixicon-react/SearchLineIcon"),
);
const DeleteBinLineIcon = importRemixIcon(
  async () => import("remixicon-react/DeleteBinLineIcon"),
);
const FileAddLineIcon = importRemixIcon(
  async () => import("remixicon-react/FileAddLineIcon"),
);
const Layout2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Layout2LineIcon"),
);
const PencilLineIcon = importRemixIcon(
  async () => import("remixicon-react/PencilLineIcon"),
);
const ServerLineIcon = importRemixIcon(
  async () => import("remixicon-react/ServerLineIcon"),
);
const LayoutColumnLineIcon = importRemixIcon(
  async () => import("remixicon-react/LayoutColumnLineIcon"),
);
const LayoutLeft2LineIcon = importRemixIcon(
  async () => import("remixicon-react/LayoutLeft2LineIcon"),
);
const PagesLineIcon = importRemixIcon(
  async () => import("remixicon-react/PagesLineIcon"),
);
const BoldIcon = importRemixIcon(
  async () => import("remixicon-react/BoldIcon"),
);
const ItalicIcon = importRemixIcon(
  async () => import("remixicon-react/ItalicIcon"),
);
const UnderLineIcon = importRemixIcon(
  async () => import("remixicon-react/UnderlineIcon"),
);
const SipLineIcon = importRemixIcon(
  async () => import("remixicon-react/SipLineIcon"),
);
const ForbidTwoLineIcon = importRemixIcon(
  async () => import("remixicon-react/Forbid2LineIcon"),
);
const Message2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Message2LineIcon"),
);
const FolderDownloadLineIcon = importRemixIcon(
  async () => import("remixicon-react/FolderDownloadLineIcon"),
);
const RestartLineIcon = importRemixIcon(
  async () => import("remixicon-react/RestartLineIcon"),
);
const FolderReduceLineIcon = importRemixIcon(
  async () => import("remixicon-react/FolderReduceLineIcon"),
);
const TimerFlashLineIcon = importRemixIcon(
  async () => import("remixicon-react/TimerFlashLineIcon"),
);
const TimerLineIcon = importRemixIcon(
  async () => import("remixicon-react/TimerLineIcon"),
);
const Map2LineIcon = importRemixIcon(
  async () => import("remixicon-react/Map2LineIcon"),
);
const MapPinUserLineIcon = importRemixIcon(
  async () => import("remixicon-react/MapPinUserLineIcon"),
);
const MapPin5LineIcon = importRemixIcon(
  async () => import("remixicon-react/MapPin5LineIcon"),
);
const ChatUploadLineIcon = importRemixIcon(
  async () => import("remixicon-react/ChatUploadLineIcon"),
);
const Home3LineIcon = importRemixIcon(
  async () => import("remixicon-react/Home3LineIcon"),
);
const FolderLineIcon = importRemixIcon(
  async () => import("remixicon-react/FolderLineIcon"),
);
const GoogleFillIcon = importRemixIcon(
  async () => import("remixicon-react/GoogleFillIcon"),
);
const GithubFillIcon = importRemixIcon(
  async () => import("remixicon-react/GithubFillIcon"),
);
const PlayLineIcon = importRemixIcon(
  async () => import("remixicon-react/PlayLineIcon"),
);
const ThumbUpLineIcon = importRemixIcon(
  async () => import("remixicon-react/ThumbUpLineIcon"),
);
const ThumbDownLineIcon = importRemixIcon(
  async () => import("remixicon-react/ThumbDownLineIcon"),
);
const MenuFoldLineIcon = importRemixIcon(
  async () => import("remixicon-react/MenuFoldLineIcon"),
);
const MenuUnfoldLineIcon = importRemixIcon(
  async () => import("remixicon-react/MenuUnfoldLineIcon"),
);
const Layout5LineIcon = importRemixIcon(
  async () => import("remixicon-react/Layout5LineIcon"),
);
const BillLineIcon = importRemixIcon(
  async () => import("remixicon-react/BillLineIcon"),
);
const FilePaper2LineIcon = importRemixIcon(
  async () => import("remixicon-react/FilePaper2LineIcon"),
);
const FileCopy2Line = importRemixIcon(
  async () => import("remixicon-react/FileCopy2LineIcon"),
);

const AppsLineIcon = importRemixIcon(
  async () => import("remixicon-react/AppsLineIcon"),
);

const ProtectedIcon = importRemixIcon(
  async () => import("remixicon-react/ShieldKeyholeLineIcon"),
);
const ShieldUserLineIcon = importRemixIcon(
  async () => import("remixicon-react/ShieldUserLineIcon"),
);
const EqualizerLineIcon = importRemixIcon(
  async () => import("remixicon-react/EqualizerLineIcon"),
);
const PriceTagIcon = importRemixIcon(
  async () => import("remixicon-react/PriceTag3LineIcon"),
);

const CornerDownLeftLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/corner-down-left-line.svg"),
);
const DragHandleIcon = importSvg(
  async () => import("../__assets__/icons/ads/app-icons/draghandler.svg"),
);
const BookLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/book-open-line.svg"),
);
const BugIcon = importSvg(
  async () => import("../__assets__/icons/ads/bug.svg"),
);
const CancelIcon = importSvg(
  async () => import("../__assets__/icons/ads/cancel.svg"),
);
const CrossIcon = importSvg(
  async () => import("../__assets__/icons/ads/cross.svg"),
);
const Fork2Icon = importSvg(
  async () => import("../__assets__/icons/ads/fork-2.svg"),
);
const OpenIcon = importSvg(
  async () => import("../__assets__/icons/ads/open.svg"),
);
const UserIcon = importSvg(
  async () => import("../__assets__/icons/ads/user.svg"),
);
const GeneralIcon = importSvg(
  async () => import("../__assets__/icons/ads/general.svg"),
);
const BillingIcon = importSvg(
  async () => import("../__assets__/icons/ads/billing.svg"),
);
const ErrorIcon = importSvg(
  async () => import("../__assets__/icons/ads/error.svg"),
);
const ShineIcon = importSvg(
  async () => import("../__assets__/icons/ads/shine.svg"),
);
const Shield = importSvg(
  async () => import("../__assets__/icons/ads/shield.svg"),
);
const SuccessIcon = importSvg(
  async () => import("../__assets__/icons/ads/success.svg"),
);
const WarningTriangleIcon = importSvg(
  async () => import("../__assets__/icons/ads/warning-triangle.svg"),
);
const ShareIcon2 = importSvg(
  async () => import("../__assets__/icons/ads/share-2.svg"),
);
const InviteUserIcon = importSvg(
  async () => import("../__assets__/icons/ads/invite-users.svg"),
);
const ManageIcon = importSvg(
  async () => import("../__assets__/icons/ads/manage.svg"),
);
const MaximizeIcon = importSvg(
  async () => import("../__assets__/icons/ads/maximize.svg"),
);
const MinimizeIcon = importSvg(
  async () => import("../__assets__/icons/ads/minimize.svg"),
);
const ArrowLeft = importSvg(
  async () => import("../__assets__/icons/ads/arrow-left.svg"),
);
const ChevronLeft = importSvg(
  async () => import("../__assets__/icons/ads/chevron_left.svg"),
);
const LinkIcon = importSvg(
  async () => import("../__assets__/icons/ads/link.svg"),
);
const NoResponseIcon = importSvg(
  async () => import("../__assets__/icons/ads/no-response.svg"),
);
const LightningIcon = importSvg(
  async () => import("../__assets__/icons/ads/lightning.svg"),
);
const TrendingFlat = importSvg(
  async () => import("../__assets__/icons/ads/trending-flat.svg"),
);
const PlayIcon = importSvg(
  async () => import("../__assets__/icons/ads/play.svg"),
);
const DesktopIcon = importSvg(
  async () => import("../__assets__/icons/ads/desktop.svg"),
);
const WandIcon = importSvg(
  async () => import("../__assets__/icons/ads/wand.svg"),
);
const MobileIcon = importSvg(
  async () => import("../__assets__/icons/ads/mobile.svg"),
);
const TabletIcon = importSvg(
  async () => import("../__assets__/icons/ads/tablet.svg"),
);
const TabletLandscapeIcon = importSvg(
  async () => import("../__assets__/icons/ads/tablet-landscape.svg"),
);
const FluidIcon = importSvg(
  async () => import("../__assets__/icons/ads/fluid.svg"),
);
const CardContextMenu = importSvg(
  async () => import("../__assets__/icons/ads/card-context-menu.svg"),
);
const GoogleColoredIcon = importSvg(
  async () => import("../__assets__/icons/ads/google.svg"),
);
const SendButton = importSvg(
  async () => import("../__assets__/icons/comments/send-button.svg"),
);
const Pin = importSvg(
  async () => import("../__assets__/icons/comments/pin.svg"),
);
const TrashOutline = importSvg(
  async () => import("../__assets__/icons/form/trash.svg"),
);
const ReadPin = importSvg(
  async () => import("../__assets__/icons/comments/read-pin.svg"),
);
const UnreadPin = importSvg(
  async () => import("../__assets__/icons/comments/unread-pin.svg"),
);
const Chat = importSvg(
  async () => import("../__assets__/icons/comments/chat.svg"),
);
const Unpin = importSvg(
  async () => import("../__assets__/icons/comments/unpinIcon.svg"),
);
const Reaction = importSvg(
  async () => import("../__assets__/icons/comments/reaction.svg"),
);
const Reaction2 = importSvg(
  async () => import("../__assets__/icons/comments/reaction-2.svg"),
);
const Upload = importSvg(
  async () => import("../__assets__/icons/ads/upload.svg"),
);
const ArrowForwardIcon = importSvg(
  async () => import("../__assets__/icons/control/arrow_forward.svg"),
);
const DoubleArrowRightIcon = importSvg(
  async () => import("../__assets__/icons/ads/double-arrow-right.svg"),
);
const DoubleArrowLeftIcon = importSvg(
  async () => import("../__assets__/icons/ads/double-arrow-left.svg"),
);
const CapSolidIcon = importSvg(
  async () => import("../__assets__/icons/control/cap_solid.svg"),
);
const CapDotIcon = importSvg(
  async () => import("../__assets__/icons/control/cap_dot.svg"),
);
const LineDottedIcon = importSvg(
  async () => import("../__assets__/icons/control/line_dotted.svg"),
);
const LineDashedIcon = importSvg(
  async () => import("../__assets__/icons/control/line_dashed.svg"),
);
const TableIcon = importSvg(
  async () => import("../__assets__/icons/ads/tables.svg"),
);
const ColumnIcon = importSvg(
  async () => import("../__assets__/icons/ads/column.svg"),
);
const GearIcon = importSvg(
  async () => import("../__assets__/icons/ads/gear.svg"),
);
const UserV2Icon = importSvg(
  async () => import("../__assets__/icons/ads/user-v2.svg"),
);
const SupportIcon = importSvg(
  async () => import("../__assets__/icons/ads/support.svg"),
);
const Snippet = importSvg(
  async () => import("../__assets__/icons/ads/snippet.svg"),
);
const WorkspaceIcon = importSvg(
  async () => import("../__assets__/icons/ads/workspaceIcon.svg"),
);
const SettingIcon = importSvg(
  async () => import("../__assets__/icons/control/settings.svg"),
);
const DropdownIcon = importSvg(
  async () => import("../__assets__/icons/ads/dropdown.svg"),
);
const ChatIcon = importSvg(
  async () => import("../__assets__/icons/ads/app-icons/chat.svg"),
);
const JsIcon = importSvg(async () => import("../__assets__/icons/ads/js.svg"));
const ExecuteIcon = importSvg(
  async () => import("../__assets__/icons/ads/execute.svg"),
);
const PackageIcon = importSvg(
  async () => import("../__assets__/icons/ads/package.svg"),
);
const ModuleIcon = importSvg(
  async () => import("../__assets__/icons/ads/module.svg"),
);
const CreateModuleIcon = importSvg(
  async () => import("../__assets__/icons/ads/create-module.svg"),
);
const WorkflowsIcon = importSvg(
  async () => import("../__assets__/icons/ads/workflows.svg"),
);
const WorkflowsMonochromeIcon = importSvg(
  async () => import("../__assets__/icons/ads/workflows-mono.svg"),
);
const DeleteIcon = importSvg(
  async () => import("../__assets__/icons/control/delete.svg"),
);
const MoveIcon = importSvg(
  async () => import("../__assets__/icons/control/move.svg"),
);
const EditIcon = importSvg(
  async () => import("../__assets__/icons/control/edit.svg"),
);
const ViewIcon = importSvg(
  async () => import("../__assets__/icons/control/view.svg"),
);
const MoreVerticalIcon = importSvg(
  async () => import("../__assets__/icons/control/more-vertical.svg"),
);
const OverflowMenuIcon = importSvg(
  async () => import("../__assets__/icons/menu/overflow-menu.svg"),
);
const JsToggleIcon = importSvg(
  async () => import("../__assets__/icons/control/js-toggle.svg"),
);
const IncreaseIcon = importSvg(
  async () => import("../__assets__/icons/control/increase.svg"),
);
const DecreaseIcon = importSvg(
  async () => import("../__assets__/icons/control/decrease.svg"),
);
const DraggableIcon = importSvg(
  async () => import("../__assets__/icons/control/draggable.svg"),
);
const AddCircleIcon = importSvg(
  async () => import("../__assets__/icons/control/add-circle.svg"),
);
const HelpIcon = importSvg(
  async () => import("../__assets__/icons/control/help.svg"),
);
const CollapseIcon = importSvg(
  async () => import("../__assets__/icons/control/collapse.svg"),
);
const PickMyLocationSelectedIcon = importSvg(
  async () => import("../__assets__/icons/control/pick-location-selected.svg"),
);
const RemoveIcon = importSvg(
  async () => import("../__assets__/icons/control/remove.svg"),
);
const DragIcon = importSvg(
  async () => import("../__assets__/icons/control/drag.svg"),
);
const SortIcon = importSvg(
  async () => import("../__assets__/icons/control/sort-icon.svg"),
);
const EditWhiteIcon = importSvg(
  async () => import("../__assets__/icons/control/edit-white.svg"),
);
const LaunchIcon = importSvg(
  async () => import("../__assets__/icons/control/launch.svg"),
);
const BackIcon = importSvg(
  async () => import("../__assets__/icons/control/back.svg"),
);
const DeleteColumnIcon = importSvg(
  async () => import("../__assets__/icons/control/delete-column.svg"),
);
const BoldFontIcon = importSvg(
  async () => import("../__assets__/icons/control/bold.svg"),
);
const UnderlineIcon = importSvg(
  async () => import("../__assets__/icons/control/underline.svg"),
);
const ItalicsFontIcon = importSvg(
  async () => import("../__assets__/icons/control/italics.svg"),
);
const LeftAlignIcon = importSvg(
  async () => import("../__assets__/icons/control/left-align.svg"),
);
const CenterAlignIcon = importSvg(
  async () => import("../__assets__/icons/control/center-align.svg"),
);
const RightAlignIcon = importSvg(
  async () => import("../__assets__/icons/control/right-align.svg"),
);
const VerticalAlignRight = importSvg(
  async () => import("../__assets__/icons/control/align_right.svg"),
);
const VerticalAlignLeft = importSvg(
  async () => import("../__assets__/icons/control/align_left.svg"),
);
const VerticalAlignBottom = importSvg(
  async () => import("../__assets__/icons/control/vertical_align_bottom.svg"),
);
const VerticalAlignCenter = importSvg(
  async () => import("../__assets__/icons/control/vertical_align_center.svg"),
);
const VerticalAlignTop = importSvg(
  async () => import("../__assets__/icons/control/vertical_align_top.svg"),
);
const Copy2Icon = importSvg(
  async () => import("../__assets__/icons/control/copy2.svg"),
);
const CutIcon = importSvg(
  async () => import("../__assets__/icons/control/cut.svg"),
);
const GroupIcon = importSvg(
  async () => import("../__assets__/icons/control/group.svg"),
);
const HeadingOneIcon = importSvg(
  async () => import("../__assets__/icons/control/heading_1.svg"),
);
const HeadingTwoIcon = importSvg(
  async () => import("../__assets__/icons/control/heading_2.svg"),
);
const HeadingThreeIcon = importSvg(
  async () => import("../__assets__/icons/control/heading_3.svg"),
);
const ParagraphIcon = importSvg(
  async () => import("../__assets__/icons/control/paragraph.svg"),
);
const ParagraphTwoIcon = importSvg(
  async () => import("../__assets__/icons/control/paragraph_2.svg"),
);
const BulletsIcon = importSvg(
  async () => import("../__assets__/icons/control/bullets.svg"),
);
const DividerCapRightIcon = importSvg(
  async () => import("../__assets__/icons/control/divider_cap_right.svg"),
);
const DividerCapLeftIcon = importSvg(
  async () => import("../__assets__/icons/control/divider_cap_left.svg"),
);
const DividerCapAllIcon = importSvg(
  async () => import("../__assets__/icons/control/divider_cap_all.svg"),
);
const AlignLeftIcon = importSvg(
  async () => import("../__assets__/icons/control/align_left.svg"),
);
const AlignRightIcon = importSvg(
  async () => import("../__assets__/icons/control/align_right.svg"),
);
const BorderRadiusSharpIcon = importSvg(
  async () => import("../__assets__/icons/control/border-radius-sharp.svg"),
);
const BorderRadiusRoundedIcon = importSvg(
  async () => import("../__assets__/icons/control/border-radius-rounded.svg"),
);
const BorderRadiusCircleIcon = importSvg(
  async () => import("../__assets__/icons/control/border-radius-circle.svg"),
);
const BoxShadowNoneIcon = importSvg(
  async () => import("../__assets__/icons/control/box-shadow-none.svg"),
);
const BoxShadowVariant1Icon = importSvg(
  async () => import("../__assets__/icons/control/box-shadow-variant1.svg"),
);
const BoxShadowVariant2Icon = importSvg(
  async () => import("../__assets__/icons/control/box-shadow-variant2.svg"),
);
const BoxShadowVariant3Icon = importSvg(
  async () => import("../__assets__/icons/control/box-shadow-variant3.svg"),
);
const BoxShadowVariant4Icon = importSvg(
  async () => import("../__assets__/icons/control/box-shadow-variant4.svg"),
);
const BoxShadowVariant5Icon = importSvg(
  async () => import("../__assets__/icons/control/box-shadow-variant5.svg"),
);
const SlashIcon = importSvg(
  async () => import("../__assets__/icons/form/slash-line.svg"),
);
const JsToggleV2 = importSvg(
  async () => import("../__assets__/icons/ads/js-toggle-v2.svg"),
);
const JsToggleV2Bold = importSvg(
  async () => import("../__assets__/icons/ads/js-toggle-v2-bold.svg"),
);
const Datasources2 = importSvg(
  async () => import("../__assets__/icons/menu/datasources-2.svg"),
);
const JSIconV2 = importSvg(
  async () => import("../__assets__/icons/menu/js-group.svg"),
);
const JSFile = importSvg(
  async () => import("../__assets__/icons/menu/js-file-icon.svg"),
);
const JSFunction = importSvg(
  async () => import("../__assets__/icons/menu/js-function.svg"),
);
const QueryMain = importSvg(
  async () => import("../__assets__/icons/menu/query-main.svg"),
);
const Binding = importSvg(
  async () => import("../__assets__/icons/menu/binding.svg"),
);
const AlignCenter = importSvg(
  async () => import("../__assets__/icons/ads/align-center.svg"),
);
const AlignLeft = importSvg(
  async () => import("../__assets__/icons/ads/align-left.svg"),
);
const AlignRight = importSvg(
  async () => import("../__assets__/icons/ads/align-right.svg"),
);
const ColumnFreeze = importSvg(
  async () => import("../__assets__/icons/ads/column-freeze.svg"),
);
const VerticalBottom = importSvg(
  async () => import("../__assets__/icons/ads/vertical-bottom.svg"),
);
const VerticalMiddle = importSvg(
  async () => import("../__assets__/icons/ads/vertical-middle.svg"),
);
const VerticalTop = importSvg(
  async () => import("../__assets__/icons/ads/vertical-top.svg"),
);
const ContractLeft = importSvg(
  async () => import("../__assets__/icons/ads/contract-left-line.svg"),
);
const ContractRight = importSvg(
  async () => import("../__assets__/icons/ads/contract-right-line.svg"),
);
const SkipRightLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/skip-right-line.svg"),
);
const SkipLeftLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/skip-left-line.svg"),
);
const WLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/w-line.svg"),
);
const HLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/h-line.svg"),
);
const ShowModalIcon = importSvg(
  async () => import("../__assets__/icons/ads/show-modal.svg"),
);
const JSYellowIcon = importSvg(
  async () => import("../__assets__/icons/ads/js-yellow.svg"),
);
const BindingIcon = importSvg(
  async () => import("../__assets__/icons/ads/binding-icon.svg"),
);
const Box3LineIcon = importSvg(
  async () => import("../__assets__/icons/ads/box-3-line.svg"),
);

const QueriesLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/queries.svg"),
);

const BracesLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/braces.svg"),
);

const DashboardLineIcon = importSvg(
  async () => import("../__assets__/icons/ads/dashboard-line.svg"),
);

const DatasourceConfigIcon = importSvg(
  async () => import("../__assets__/icons/ads/datasource-config.svg"),
);

// v3 icons
const JsSquareV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/js-square-v3-icon.svg"),
);
const QueriesV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/queries-v3-icon.svg"),
);
const WidgetsV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/widgets-v3-icon.svg"),
);
const DatasourceV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/datasource-v3-icon.svg"),
);
const EditorV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/editor-v3-icon.svg"),
);
const SettingsV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/settings-v3-icon.svg"),
);
const PackagesV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/packages-v3-icon.svg"),
);

const MinimizeV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/minimize-v3-icon.svg"),
);

const MaximizeV3Icon = importSvg(
  async () => import("../__assets__/icons/ads/maximize-v3-icon.svg"),
);

const ExternalLinkIcon = importRemixIcon(
  async () => import("remixicon-react/ExternalLinkLineIcon"),
);

const InputCursorMoveIcon = importSvg(
  async () => import("../__assets__/icons/ads/input-cursor-move.svg"),
);

const DebugIcon = importSvg(
  async () => import("../__assets__/icons/ads/debug.svg"),
);

const ClearIcon = importSvg(
  async () => import("../__assets__/icons/ads/clear.svg"),
);

const ContentTypeTable = importSvg(
  async () => import("../__assets__/icons/ads/content-type-table.svg"),
);

const ContentTypeJson = importSvg(
  async () => import("../__assets__/icons/ads/content-type-json.svg"),
);

const ContentTypeRaw = importSvg(
  async () => import("../__assets__/icons/ads/content-type-raw.svg"),
);

const CloudIconV2 = importSvg(
  async () => import("../__assets__/icons/ads/cloudy-line.svg"),
);

const NotionIcon = importSvg(
  async () => import("../__assets__/icons/ads/notion.svg"),
);

const ZendeskIcon = importSvg(
  async () => import("../__assets__/icons/ads/zendesk.svg"),
);

const GoogleDriveIcon = importSvg(
  async () => import("../__assets__/icons/ads/google-drive.svg"),
);

const SalesforceIcon = importSvg(
  async () => import("../__assets__/icons/ads/salesforce.svg"),
);

const StateInspectorIcon = importSvg(
  async () => import("../__assets__/icons/ads/state-inspector.svg"),
);

const BoxIcon = importSvg(
  async () => import("../__assets__/icons/ads/box.svg"),
);
const ConfluenceIcon = importSvg(
  async () => import("../__assets__/icons/ads/confluence.svg"),
);
const DropboxIcon = importSvg(
  async () => import("../__assets__/icons/ads/dropbox.svg"),
);
const FreshdeskIcon = importSvg(
  async () => import("../__assets__/icons/ads/freshdesk.svg"),
);
const IntercomIcon = importSvg(
  async () => import("../__assets__/icons/ads/intercom.svg"),
);
const OnedriveIcon = importSvg(
  async () => import("../__assets__/icons/ads/onedrive.svg"),
);
const SharepointIcon = importSvg(
  async () => import("../__assets__/icons/ads/sharepoint.svg"),
);

const MdFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/md-file.svg"),
);
const PdfFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/pdf-file.svg"),
);
const TxtFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/txt-file.svg"),
);

const CsvFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/csv-file.svg"),
);
const DocFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/doc-file.svg"),
);
const JsonFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/json-file.svg"),
);
const PptFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/ppt-file.svg"),
);
const RtfFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/rtf-file.svg"),
);
const TsvFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/tsv-file.svg"),
);
const XlsFileIcon = importSvg(
  async () => import("../__assets__/icons/ads/xls-file.svg"),
);
const Robot2LineIcon = importSvg(
  async () => import("../__assets__/icons/ads/robot-2-line.svg"),
);

const SparklingFilledIcon = importSvg(
  async () => import("../__assets__/icons/ads/sparkling-filled.svg"),
);

const AIChatIcon = importSvg(
  async () => import("../__assets__/icons/ads/ai-chat.svg"),
);

import PlayIconPNG from "../__assets__/icons/control/play-icon.png";

function PlayIconPNGWrapper() {
  return (
    <img
      alt="Datasource"
      src={PlayIconPNG}
      style={{ height: "30px", width: "30px" }}
    />
  );
}

const ICON_LOOKUP = {
  "account-box-line": AccountBoxLineIcon,
  "account-circle-line": AccountCircleLineIcon,
  "add-box-line": AddBoxLineIcon,
  "add-circle-control": AddCircleIcon,
  "add-line": AddLineIcon,
  "add-more": AddMoreIcon,
  "add-more-fill": AddMoreFillIcon,
  "ai-chat": AIChatIcon,
  "arrow-go-forward": ArrowGoForwardLineIcon,
  "alert-fill": AlertFillIcon,
  "alert-line": AlertLineIcon,
  "align-center": AlignCenter,
  "align-left": AlignLeft,
  "align-right": AlignRight,
  "apps-line": AppsLineIcon,
  "arrow-down-s-fill": ArrowDownSFillIcon,
  "arrow-down-s-line": ArrowDownSLineIcon,
  "arrow-forward": ArrowForwardIcon,
  "arrow-go-back": ArrowGoBackLineIcon,
  "arrow-left": ArrowLeft,
  "arrow-left-line": ArrowLeftLineIcon,
  "arrow-left-s-line": ArrowLeftSLineIcon,
  "arrow-right-line": ArrowRightLineIcon,
  "arrow-right-s-fill": ArrowRightSFillIcon,
  "arrow-right-s-line": ArrowRightSLineIcon,
  "arrow-right-up-line": ArrowRightUpLineIcon,
  "arrow-up-line": ArrowUpLineIcon,
  "arrow-up-s-line": ArrowUpSLineIcon,
  "back-control": BackIcon,
  "bill-line": BillLineIcon,
  "bind-data-control": TrendingFlat,
  "binding-new": BindingIcon,
  "bold-font": BoldFontIcon,
  "book-line": BookLineIcon,
  "border-radius-circle": BorderRadiusCircleIcon,
  "border-radius-rounded": BorderRadiusRoundedIcon,
  "border-radius-sharp": BorderRadiusSharpIcon,
  "box-3-line": Box3LineIcon,
  "box-shadow-none": BoxShadowNoneIcon,
  "box-shadow-variant1": BoxShadowVariant1Icon,
  "box-shadow-variant2": BoxShadowVariant2Icon,
  "box-shadow-variant3": BoxShadowVariant3Icon,
  "box-shadow-variant4": BoxShadowVariant4Icon,
  "box-shadow-variant5": BoxShadowVariant5Icon,
  "braces-line": BracesLineIcon,
  "bug-line": BugLineIcon,
  "cap-dot": CapDotIcon,
  "cap-solid": CapSolidIcon,
  "card-context-menu": CardContextMenu,
  "center-align": CenterAlignIcon,
  "chat-help": ChatIcon,
  "chat-upload-line": ChatUploadLineIcon,
  "check-line": CheckLineIcon,
  "chevron-left": ChevronLeft,
  "chevron-right": ChevronRight,
  clear: ClearIcon,
  "close-circle": CloseCircleIcon,
  "close-circle-control": CloseCircleIcon,
  "close-circle-line": CloseCircleLineIcon,
  "close-control": CloseIcon,
  "close-line": CloseIcon,
  "close-modal": CloseLineIcon,
  "close-x": CloseLineIcon,
  "cloud-off-line": CloudOfflineIcon,
  "cloud-v2": CloudIconV2,
  "collapse-control": CollapseIcon,
  "column-freeze": ColumnFreeze,
  "column-unfreeze": SubtractIcon,
  "comment-context-menu": CommentContextMenu,
  "compasses-line": CompassesLine,
  "content-type-table": ContentTypeTable,
  "content-type-json": ContentTypeJson,
  "content-type-raw": ContentTypeRaw,
  "context-menu": ContextMenuIcon,
  "contract-left-line": ContractLeft,
  "contract-right-line": ContractRight,
  "copy-control": CopyIcon,
  "copy2-control": Copy2Icon,
  "create-module": CreateModuleIcon,
  "cut-control": CutIcon,
  "dashboard-line": DashboardLineIcon,
  "database-2-line": Database2Line,
  "datasource-config": DatasourceConfigIcon,
  "datasource-v3": DatasourceV3Icon,
  "datasources-2": Datasources2,
  "decrease-control": DecreaseIcon,
  debug: DebugIcon,
  "delete-bin-line": DeleteBinLineIcon,
  "delete-blank": DeleteBin7,
  "delete-column": DeleteColumnIcon,
  "delete-control": DeleteIcon,
  "delete-row": DeleteRowIcon,
  "divider-cap-all": DividerCapAllIcon,
  "divider-cap-left": DividerCapLeftIcon,
  "divider-cap-right": DividerCapRightIcon,
  "double-arrow-left": DoubleArrowLeftIcon,
  "double-arrow-right": DoubleArrowRightIcon,
  "down-arrow": DownArrowIcon,
  "down-arrow-2": ArrowDownLineIcon,
  "download-line": DownloadLineIcon,
  "drag-control": DragIcon,
  "drag-handle": DragHandleIcon,
  "draggable-control": DraggableIcon,
  "edit-2-line": Edit2LineIcon,
  "edit-box-line": EditBoxLineIcon,
  "edit-control": EditIcon,
  "edit-line": EditLineIcon,
  "edit-underline": EditUnderlineIcon,
  "edit-white": EditWhiteIcon,
  "editor-v3": EditorV3Icon,
  "enter-line": CornerDownLeftLineIcon,
  "equalizer-line": EqualizerLineIcon,
  "expand-less": ExpandLess,
  "expand-more": ExpandMore,
  "external-link-line": ExternalLinkIcon,
  "eye-off": EyeOff,
  "eye-on": EyeOn,
  "file-add-line": FileAddLineIcon,
  "file-copy-2-line": FileCopy2Line,
  "file-line": FileLine,
  "file-list-2-line": FileList2LineIcon,
  "file-list-line": FileListLineIcon,
  "file-paper-2-line": FilePaper2LineIcon,
  "file-transfer": FileTransfer,
  "folder-download-line": FolderDownloadLineIcon,
  "folder-line": FolderLineIcon,
  "folder-reduce-line": FolderReduceLineIcon,
  "forbid-line": ForbidLineIcon,
  "fork-2": Fork2Icon,
  "gift-line": GiftLineIcon,
  "git-branch": GitBranchLineIcon,
  "git-commit": GitCommit,
  "git-pull-request": GitPullRequest,
  "git-repository": GitRepository,
  "github-fill": GithubFillIcon,
  "global-line": GlobalLineIcon,
  "google-colored": GoogleColoredIcon,
  "google-fill": GoogleFillIcon,
  "group-2-line": Group2LineIcon,
  "group-control": GroupIcon,
  "group-line": GroupLineIcon,
  "h-line": HLineIcon,
  "heading-one": HeadingOneIcon,
  "heading-three": HeadingThreeIcon,
  "heading-two": HeadingTwoIcon,
  "help-control": HelpIcon,
  "hide-column": EyeOffIcon,
  "home-3-line": Home3LineIcon,
  "icon-align-left": AlignLeftIcon,
  "icon-align-right": AlignRightIcon,
  "increase-control": IncreaseIcon,
  "increase-control-v2": IncreaseV2Icon,
  "info-fill": InfoFillIcon,
  "input-cursor-move": InputCursorMoveIcon,
  "invite-user": InviteUserIcon,
  "italics-font": ItalicsFontIcon,
  "js-file": JSFile,
  "js-function": JSFunction,
  "js-icon-v2": JSIconV2,
  "js-square-v3": JsSquareV3Icon,
  "js-toggle": JsToggleIcon,
  "js-toggle-v2": JsToggleV2,
  "js-toggle-v2-bold": JsToggleV2Bold,
  "js-yellow": JSYellowIcon,
  "key-2-line": Key2LineIcon,
  "launch-control": LaunchIcon,
  "layout-2-line": Layout2LineIcon,
  "layout-5-line": Layout5LineIcon,
  "layout-column-line": LayoutColumnLineIcon,
  "layout-left-2-line": LayoutLeft2LineIcon,
  "left-align": LeftAlignIcon,
  "left-arrow-2": LeftArrowIcon2,
  "lightbulb-flash-line": LightbulbFlashLine,
  "line-dashed": LineDashedIcon,
  "line-dotted": LineDottedIcon,
  "link-2": Link2,
  "link-unlink": LinkUnlinkIcon,
  "links-line": LinksLineIcon,
  "loader-line": LoaderLineIcon,
  "lock-2-line": Lock2LineIcon,
  "lock-fill": LockFillIcon,
  "lock-password-line": LockPasswordLineIcon,
  "lock-unlock-line": LockUnlockLineIcon,
  "magic-line": MagicLineIcon,
  "mail-check-line": MailCheckLineIcon,
  "mail-line": MailLineIcon,
  "map-2-line": Map2LineIcon,
  "map-pin-2-line": MapPin2LineIcon,
  "map-pin-5-line": MapPin5LineIcon,
  "map-pin-user-line": MapPinUserLineIcon,
  "maximize-v3": MaximizeV3Icon,
  "menu-fold": MenuFoldLineIcon,
  "menu-unfold": MenuUnfoldLineIcon,
  "message-2-line": Message2LineIcon,
  "minimize-v3": MinimizeV3Icon,
  "money-dollar-circle-line": MoneyDollarCircleLineIcon,
  "more-2-fill": More2FillIcon,
  "more-horizontal-control": OverflowMenuIcon,
  "more-vertical-control": MoreVerticalIcon,
  "move-control": MoveIcon,
  "news-paper": NewsPaperLine,
  "no-action": ForbidTwoLineIcon,
  "no-response": NoResponseIcon,
  "oval-check": OvalCheck,
  "oval-check-fill": OvalCheckFill,
  "packages-v3": PackagesV3Icon,
  "page-line": PagesLineIcon,
  "paragraph-two": ParagraphTwoIcon,
  "pencil-fill-icon": PencilFillIcon,
  "pencil-line": PencilLineIcon,
  "pick-my-location-selected-control": PickMyLocationSelectedIcon,
  "pin-3": Pin3,
  "play-circle-line": PlayCircleLineIcon,
  "play-line": PlayLineIcon,
  "play-video": PlayIconPNGWrapper,
  "queries-line": QueriesLineIcon,
  "queries-v3": QueriesV3Icon,
  "query-main": QueryMain,
  "question-fill": QuestionFillIcon,
  "question-line": QuestionLineIcon,
  "question-mark": QuestionMarkIcon,
  "reaction-2": Reaction2,
  "read-pin": ReadPin,
  "remove-control": RemoveIcon,
  "restart-line": RestartLineIcon,
  "right-align": RightAlignIcon,
  "right-arrow": RightArrowIcon,
  "right-arrow-2": RightArrowIcon2,
  "search-eye-line": SearchEyeLineIcon,
  "search-line": SearchLineIcon,
  "send-button": SendButton,
  "server-line": ServerLineIcon,
  "settings-2-line": Settings2LineIcon,
  "settings-control": SettingsIcon,
  "settings-line": SettingsLineIcon,
  "settings-v3": SettingsV3Icon,
  "share-2": ShareIcon2,
  "share-box": ShareBoxFillIcon,
  "share-box-line": ShareBoxLineIcon,
  "share-line": ShareLineIcon,
  "show-column": EyeIcon,
  "show-modal": ShowModalIcon,
  "sip-line": SipLineIcon,
  "skip-left-line": SkipLeftLineIcon,
  "skip-right-line": SkipRightLineIcon,
  "sort-asc": SortAscIcon,
  "sort-control": SortIcon,
  "sort-desc": SortDescIcon,
  "star-fill": StarFillIcon,
  "star-line": StarLineIcon,
  "subtract-line": SubtractLine,
  "swap-horizontal": ArrowLeftRightIcon,
  "text-bold": BoldIcon,
  "text-italic": ItalicIcon,
  "text-underline": UnderLineIcon,
  "thumb-down-line": ThumbDownLineIcon,
  "thumb-up-line": ThumbUpLineIcon,
  "timer-2-line": Timer2LineIcon,
  "timer-flash-line": TimerFlashLineIcon,
  "timer-line": TimerLineIcon,
  "trash-outline": TrashOutline,
  "trending-flat": TrendingFlat,
  "unread-pin": UnreadPin,
  "upload-cloud": UploadCloud2LineIcon,
  "upload-line": UploadLineIcon,
  "user-2": UserV2Icon,
  "user-2-line": User2LineIcon,
  "user-3-line": User3LineIcon,
  "user-add-line": UserAddLineIcon,
  "user-follow-line": UserFollowLineIcon,
  "user-heart-line": UserHeartLineIcon,
  "user-received-2-line": UserReceived2LineIcon,
  "user-settings-line": UserSettingsLineIcon,
  "user-shared-line": UserSharedLineIcon,
  "user-star-line": UserStarLineIcon,
  "user-unfollow-line": UserUnfollowLineIcon,
  "vertical-align-bottom": VerticalBottom,
  "vertical-align-middle": VerticalMiddle,
  "vertical-align-top": VerticalTop,
  "vertical-bottom": VerticalAlignBottom,
  "vertical-center": VerticalAlignCenter,
  "vertical-left": VerticalAlignLeft,
  "vertical-right": VerticalAlignRight,
  "vertical-top": VerticalAlignTop,
  "view-all": RightArrowIcon,
  "view-control": ViewIcon,
  "view-less": LeftArrowIcon,
  "w-line": WLineIcon,
  "warning-line": WarningLineIcon,
  "warning-triangle": WarningTriangleIcon,
  "widgets-v3": WidgetsV3Icon,
  "workflows-mono": WorkflowsMonochromeIcon,
  "protected-icon": ProtectedIcon,
  pricetag: PriceTagIcon,
  billing: BillingIcon,
  binding: Binding,
  book: BookIcon,
  bug: BugIcon,
  bullets: BulletsIcon,
  cancel: CancelIcon,
  chat: Chat,
  close: CloseIcon,
  cloud: CloudLineIcon,
  code: CodeViewIcon,
  column: ColumnIcon,
  cross: CrossIcon,
  danger: ErrorIcon,
  datasource: DatasourceIcon,
  delete: Trash,
  desktop: DesktopIcon,
  device: DeviceLineIcon,
  discord: DiscordIcon,
  downArrow: DownArrow,
  download2: DownloadIcon,
  download: Download,
  dropdown: DropdownIcon,
  duplicate: DuplicateIcon,
  edit: EditIcon,
  emoji: Emoji,
  enterprise: MagicLineIcon,
  error: ErrorIcon,
  execute: ExecuteIcon,
  filter: Filter,
  fluid: FluidIcon,
  fork: GitMerge,
  gear: GearIcon,
  general: GeneralIcon,
  grid: GridLineIcon,
  guide: GuideIcon,
  hamburger: HamburgerIcon,
  help: HelpIcon,
  history: HistoryLineIcon,
  info: InfoIcon,
  js: JsIcon,
  key: KeyIcon,
  lightning: LightningIcon,
  link: LinkIcon,
  loader: LoaderLineIcon,
  login: LoginIcon,
  logout: LogoutIcon,
  manage: ManageIcon,
  maximize: MaximizeIcon,
  member: UserHeartLineIcon,
  minimize: MinimizeIcon,
  minus: RemoveIcon,
  mobile: MobileIcon,
  module: ModuleIcon,
  open: OpenIcon,
  package: PackageIcon,
  pantone: PantoneLineIcon,
  paragraph: ParagraphIcon,
  pin: Pin,
  play: PlayIcon,
  plus: CreateNewIcon,
  query: QueryIcon,
  question: QuestionIcon,
  reaction: Reaction,
  refresh: RefreshLineIcon,
  rocket: RocketIcon,
  robot: RobotIcon,
  "robot-2": Robot2LineIcon,
  "sparkling-filled": SparklingFilledIcon,
  save: Save2LineIcon,
  search: SearchIcon,
  setting: SettingIcon,
  share: ShareForwardIcon,
  shield: Shield,
  "shield-user-line": ShieldUserLineIcon,
  shine: ShineIcon,
  slash: SlashIcon,
  snippet: Snippet,
  success: SuccessIcon,
  support: SupportIcon,
  tables: TableIcon,
  tablet: TabletIcon,
  tabletLandscape: TabletLandscapeIcon,
  trash: Trash,
  underline: UnderlineIcon,
  unpin: Unpin,
  upArrow: UpArrow,
  upgrade: DvdLineIcon,
  upload: Upload,
  user: UserIcon,
  wand: WandIcon,
  warning: WarningIcon,
  widget: WidgetIcon,
  workflows: WorkflowsIcon,
  workspace: WorkspaceIcon,
  notion: NotionIcon,
  "md-file": MdFileIcon,
  "pdf-file": PdfFileIcon,
  "txt-file": TxtFileIcon,
  "csv-file": CsvFileIcon,
  "doc-file": DocFileIcon,
  "json-file": JsonFileIcon,
  "ppt-file": PptFileIcon,
  "rtf-file": RtfFileIcon,
  "tsv-file": TsvFileIcon,
  "xls-file": XlsFileIcon,
  zendesk: ZendeskIcon,
  "google-drive": GoogleDriveIcon,
  salesforce: SalesforceIcon,
  "state-inspector": StateInspectorIcon,
  box: BoxIcon,
  confluence: ConfluenceIcon,
  dropbox: DropboxIcon,
  freshdesk: FreshdeskIcon,
  intercom: IntercomIcon,
  onedrive: OnedriveIcon,
  sharepoint: SharepointIcon,
};

export const IconCollection = Object.keys(ICON_LOOKUP);

export type IconNames = (typeof IconCollection)[number];

/*
  TODO:
  - fix path and colors for inverse icons
 */
export function IconProvider(props: {
  iconName: string;
  size?: string;
  color?: string;
}) {
  const { color, iconName, size } = props;

  const Icon = ICON_LOOKUP[iconName as keyof typeof ICON_LOOKUP]
    ? ICON_LOOKUP[iconName as keyof typeof ICON_LOOKUP]
    : null;

  if (!Icon) {
    console.error(
      iconName,
      " not found. If you haven't made a typo, the icon probably does not exit " +
        "in our database - check the lookup here: " +
        "https://github.com/appsmithorg/appsmith/blob/release/app/client/packages/design-system/ads/src/Icon/Icon.provider.tsx#L1078",
    );
  }

  return Icon && <Icon color={color} size={size} />;
}
