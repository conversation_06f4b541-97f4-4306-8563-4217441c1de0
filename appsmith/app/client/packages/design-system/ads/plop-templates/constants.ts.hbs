import { CLASS_NAME_PREFIX } from "__config__/constants";

export const {{capitalize name}}ClassName = `${CLASS_NAME_PREFIX}-name`;
export const {{capitalize name}}SubElementClassName = `${ {{capitalize name}}ClassName}__sub-element`;
export const {{capitalize name}}SubElementChildClassName = `${ {{capitalize name}}SubElementClassName}-child`;
{{! Path: packages/design-system/plop-templates/index.ts.hbs }}
