{"name": "@shared/ast", "private": true, "version": "1.0.0", "description": "", "main": "./index.ts", "scripts": {"test:unit": "jest -b --colors --no-cache --silent --coverage --collectCoverage=true --coverageDirectory='./' --coverageReporters='json-summary'", "test:jest": "jest --watch", "lint": "yarn g:lint", "prettier": "yarn g:prettier", "start": "rollup -c", "build": "rollup -c"}, "dependencies": {"@rollup/plugin-commonjs": "^22.0.0", "@types/escodegen": "^0.0.7", "@types/lodash": "^4.14.120", "acorn": "8.14.0", "acorn-walk": "8.3.4", "astravel": "^0.6.1", "astring": "^1.7.5", "escodegen": "^2.0.0", "klona": "^2.0.5", "lodash": "^4.17.21", "rollup": "^2.79.2", "rollup-plugin-generate-package-json": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-typescript2": "^0.32.0", "typescript": "^5.5.4", "unescape-js": "^1.1.4"}, "devDependencies": {"@types/jest": "29.0.3", "@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "jest": "29.0.3", "ts-jest": "29.1.0"}}