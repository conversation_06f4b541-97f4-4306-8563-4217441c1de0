{"compilerOptions": {"target": "ES6", "lib": ["DOM", "ES6", "DOM.Iterable", "ScriptHost", "ES2016.Array.Include", "es2020.string", "esnext"], "strict": true, "declaration": true, "declarationDir": "build", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "downlevelIteration": true, "experimentalDecorators": true, "importHelpers": true, "typeRoots": ["./typings", "./node_modules/@types"], "sourceMap": true, "baseUrl": "./src", "noFallthroughCasesInSwitch": true}, "include": ["./src/**/*", "index.d.ts", "index.ts"], "exclude": ["node_modules", "build"]}