server {
    listen 80;
    server_name dev.appsmith.com;

    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dev.appsmith.com;
    client_max_body_size 150m;

    ssl_certificate /etc/certificate/dev.appsmith.com.pem;
    ssl_certificate_key /etc/certificate/dev.appsmith.com-key.pem;

    # include /etc/letsencrypt/options-ssl-nginx.conf;
    # ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    gzip on;

    proxy_ssl_server_name on;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header Accept-Encoding "";

    sub_filter_once off;
    location / {
        proxy_pass __APPSMITH_CLIENT_PROXY_PASS__;
        sub_filter __APPSMITH_SENTRY_DSN__ '${APPSMITH_SENTRY_DSN}';
        sub_filter __APPSMITH_SMART_LOOK_ID__ '${APPSMITH_SMART_LOOK_ID}';
        sub_filter __APPSMITH_SEGMENT_KEY__ '${APPSMITH_SEGMENT_KEY}';
        sub_filter __APPSMITH_CLIENT_LOG_LEVEL__ '${APPSMITH_CLIENT_LOG_LEVEL}';
        sub_filter __APPSMITH_SENTRY_RELEASE__ '${APPSMITH_SENTRY_RELEASE}';
        sub_filter __APPSMITH_SENTRY_ENVIRONMENT__ '${APPSMITH_SENTRY_ENVIRONMENT}';
        sub_filter __APPSMITH_VERSION_ID__ '${APPSMITH_VERSION_ID}';
        sub_filter __APPSMITH_VERSION_RELEASE_DATE__ '${APPSMITH_VERSION_RELEASE_DATE}';
        sub_filter __APPSMITH_INTERCOM_APP_ID__ '${APPSMITH_INTERCOM_APP_ID}';
        sub_filter __APPSMITH_MAIL_ENABLED__ '${APPSMITH_MAIL_ENABLED}';
        sub_filter __APPSMITH_RECAPTCHA_SITE_KEY__ '${APPSMITH_RECAPTCHA_SITE_KEY}';
        sub_filter __APPSMITH_DISABLE_INTERCOM__ '${APPSMITH_DISABLE_INTERCOM}';
        sub_filter __APPSMITH_ZIPY_SDK_KEY__ '${APPSMITH_ZIPY_SDK_KEY}';
        sub_filter __APPSMITH_HIDE_WATERMARK__ '${APPSMITH_HIDE_WATERMARK}';
        sub_filter __APPSMITH_DISABLE_IFRAME_WIDGET_SANDBOX__ '${APPSMITH_DISABLE_IFRAME_WIDGET_SANDBOX}';

        sub_filter __APPSMITH_NEW_RELIC_OTLP_LICENSE_KEY__ '${APPSMITH_NEW_RELIC_OTLP_LICENSE_KEY}';
        sub_filter __APPSMITH_NEW_RELIC_OTEL_EXPORTER_OTLP_ENDPOINT__ '${APPSMITH_NEW_RELIC_OTEL_EXPORTER_OTLP_ENDPOINT}';
        sub_filter __APPSMITH_FRONTEND_TRACING_URL__ '${APPSMITH_FRONTEND_TRACING_URL}';
    }


    location /api {
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_pass __APPSMITH_SERVER_PROXY_PASS__;
    }

    location /oauth2 {
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_pass __APPSMITH_SERVER_PROXY_PASS__;
    }

    location /login {
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_pass __APPSMITH_SERVER_PROXY_PASS__;
    }
}
