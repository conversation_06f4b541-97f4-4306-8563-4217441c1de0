import React, { useCallback } from "react";
import { truncate } from "lodash";

import type { JSCollection } from "entities/JSCollection";
import {
  <PERSON><PERSON>,
  <PERSON>lex,
  <PERSON>u,
  <PERSON>uContent,
  <PERSON>uT<PERSON>ger,
  Toolt<PERSON>,
} from "@appsmith/ads";
import type { JSActionDropdownOption } from "../types";
import { RUN_BUTTON_DEFAULTS, testLocators } from "../constants";
import { createMessage, NO_JS_FUNCTION_TO_RUN } from "ee/constants/messages";
import { JSFunctionItem } from "./JSFunctionItem";
import { JS_FUNCTION_RUN_NAME_LENGTH } from "./constants";

interface Props {
  disabled: boolean;
  isLoading: boolean;
  jsCollection: JSCollection;
  onButtonClick: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  onSelect: (value: string | undefined) => void;
  options: JSActionDropdownOption[];
  selected: JSActionDropdownOption;
  showTooltip: boolean;
}

/**
 * JSFunctionRun component renders a button and a dropdown menu for running JS functions.
 * It conditionally renders the old or new version of the component based on a feature flag.
 *
 */
export const JSFunctionRun = (props: Props) => {
  const { onSelect } = props;

  // Callback function to handle function selection from the dropdown menu
  const onFunctionSelect = useCallback(
    (option: JSActionDropdownOption) => {
      if (onSelect) {
        onSelect(option.value);
      }
    },
    [onSelect],
  );

  return (
    <Flex gap="spaces-2">
      <Menu>
        <MenuTrigger>
          <Button
            data-testid="t--js-function-run"
            endIcon="arrow-down-s-line"
            isDisabled={props.disabled}
            kind="tertiary"
            size="sm"
            startIcon="js-function"
          >
            {truncate(props.selected.label, {
              length: JS_FUNCTION_RUN_NAME_LENGTH,
            })}
          </Button>
        </MenuTrigger>
        {!!props.options.length && (
          <MenuContent align="end" data-testid="t--js-functions-menu">
            {props.options.map((option) => (
              <JSFunctionItem
                key={option.label}
                onSelect={onFunctionSelect}
                option={option}
              />
            ))}
          </MenuContent>
        )}
      </Menu>

      <Tooltip
        content={createMessage(NO_JS_FUNCTION_TO_RUN, props.jsCollection.name)}
        isDisabled={!props.showTooltip}
        placement="topRight"
      >
        <Button
          className={testLocators.runJSAction}
          data-testid={testLocators.runJSActionTestID}
          isDisabled={props.disabled}
          isLoading={props.isLoading}
          onClick={props.onButtonClick}
          size="sm"
        >
          {RUN_BUTTON_DEFAULTS.CTA_TEXT}
        </Button>
      </Tooltip>
    </Flex>
  );
};
