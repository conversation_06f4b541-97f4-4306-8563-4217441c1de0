export const cards = [
  {
    key: "fvqmankv5n",
    type: "AUDIO_WIDGET",
    rows: 4,
    columns: 28,
    detachFromLayout: false,
    displayName: "Audio",
    icon: "/static/media/icon.07157764485879c0e109bcd2c97db502.svg",
    thumbnail: "/static/media/thumbnail.6be15396ae96265e42ee0b4f0ea47b84.svg",
    searchTags: ["mp3", "sound", "wave", "player"],
    tags: ["Media"],
    isDynamicHeight: false,
  },
  {
    key: "ghuh15z8of",
    type: "AUDIO_RECORDER_WIDGET",
    rows: 7,
    columns: 16,
    detachFromLayout: false,
    displayName: "Audio Recorder",
    icon: "/static/media/icon.5cc50f429e2b62c16ce55c3eedb03ee3.svg",
    thumbnail: "/static/media/thumbnail.4513fbf2818c6a93ab8300818615fcec.svg",
    searchTags: ["sound recorder", "voice recorder"],
    tags: ["External"],
    isDynamicHeight: false,
  },
  {
    key: "4qidp9t2f3",
    type: "BUTTON_WIDGET",
    rows: 4,
    columns: 16,
    detachFromLayout: false,
    displayName: "Button",
    icon: "/static/media/icon.cb7371e9a48acf9db0f2f3ea0a714255.svg",
    thumbnail: "/static/media/thumbnail.a348658e996feaad96cadc30d99374ff.svg",
    searchTags: ["click", "submit"],
    tags: ["Buttons"],
    isDynamicHeight: false,
  },
  {
    key: "sn7f66bkdl",
    type: "BUTTON_GROUP_WIDGET",
    rows: 4,
    columns: 24,
    detachFromLayout: false,
    displayName: "Button Group",
    icon: "/static/media/icon.ae1401b479d77b1908271bdfed6d12ba.svg",
    thumbnail: "/static/media/thumbnail.eaa22f923be763e8779a46432554f1ed.svg",
    searchTags: ["click", "submit"],
    tags: ["Buttons"],
    isDynamicHeight: false,
  },
  {
    key: "ybakb6m5hv",
    type: "CAMERA_WIDGET",
    rows: 33,
    columns: 25,
    detachFromLayout: false,
    displayName: "Camera",
    icon: "/static/media/icon.34eb88a83cfebde8a0a6b0e9383e35f6.svg",
    thumbnail: "/static/media/thumbnail.8cd95033bb6345b71ab6da5c06462725.svg",
    searchTags: ["photo", "video recorder"],
    tags: ["External"],
    isDynamicHeight: false,
  },
  {
    key: "7vsaskvw9p",
    type: "CATEGORY_SLIDER_WIDGET",
    rows: 8,
    columns: 40,
    detachFromLayout: false,
    displayName: "Category Slider",
    icon: "/static/media/icon.7615ff4a5f55a5c7a3eb377a653a028b.svg",
    thumbnail: "/static/media/thumbnail.06a850cb6ec6946f666e3f6cdce9f5da.svg",
    searchTags: ["range"],
    tags: ["Sliders"],
    isDynamicHeight: false,
  },
  {
    key: "fmfa4jwi62",
    type: "CHART_WIDGET",
    rows: 32,
    columns: 24,
    detachFromLayout: false,
    displayName: "Chart",
    icon: "/static/media/icon.07abfb45c3867c2e1b1d5d4818b7078c.svg",
    thumbnail: "/static/media/thumbnail.98fb19b2c4f99671c0888284cf939f3a.svg",
    searchTags: ["graph", "visuals", "visualisations"],
    tags: ["Display"],
    isDynamicHeight: false,
  },
  {
    key: "7ewf25yjl0",
    type: "CHECKBOX_WIDGET",
    rows: 4,
    columns: 12,
    detachFromLayout: false,
    displayName: "Checkbox",
    icon: "/static/media/icon.********************************.svg",
    thumbnail: "/static/media/thumbnail.aa4d27836e27bb6c4c45db14dee8f2b2.svg",
    searchTags: ["boolean"],
    tags: ["Toggles"],
    isDynamicHeight: true,
  },
  {
    key: "zqcq8kn28u",
    type: "CHECKBOX_GROUP_WIDGET",
    rows: 6,
    columns: 23,
    detachFromLayout: false,
    displayName: "Checkbox Group",
    icon: "/static/media/icon.********************************.svg",
    thumbnail: "/static/media/thumbnail.2dc0ec495bdfde499e9ce54c2dd91015.svg",
    tags: ["Toggles"],
    isDynamicHeight: true,
  },
  {
    key: "ub61i3g7jm",
    type: "CODE_SCANNER_WIDGET",
    rows: 33,
    columns: 25,
    detachFromLayout: false,
    displayName: "Code Scanner",
    icon: "/static/media/icon.9e807168159cda719250068e053610ea.svg",
    thumbnail: "/static/media/thumbnail.40d4041f63e94b1dd8bca195bf98b596.svg",
    searchTags: [
      "barcode scanner",
      "qr scanner",
      "code detector",
      "barcode reader",
    ],
    tags: ["External"],
    isDynamicHeight: false,
  },
  {
    key: "k0em99zxft",
    type: "CONTAINER_WIDGET",
    rows: 10,
    columns: 24,
    detachFromLayout: false,
    displayName: "Container",
    icon: "/static/media/icon.dcc052e58db911c4c8955c200f4bcf5c.svg",
    thumbnail: "/static/media/thumbnail.6cb355b93146b5347bbb048a568ed446.svg",
    searchTags: ["div", "parent", "group"],
    tags: ["Layout"],
    isDynamicHeight: true,
  },
  {
    key: "3z6ir71rj0",
    type: "CURRENCY_INPUT_WIDGET",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "Currency Input",
    icon: "/static/media/icon.5a8f2fe74616b133e5165fdd06237cd9.svg",
    thumbnail: "/static/media/thumbnail.b14e0307b1b312b9e9ca9580d118af39.svg",
    searchTags: ["amount", "total"],
    tags: ["Inputs"],
    isDynamicHeight: false,
  },
  {
    key: "1fxwqiv7ag",
    type: "CUSTOM_WIDGET",
    rows: 30,
    columns: 23,
    detachFromLayout: false,
    displayName: "Custom",
    icon: "/static/media/icon.ff37253a5ce2f5284c42ede26ce165f9.svg",
    thumbnail: "/static/media/thumbnail.a7d7cde44fad9a0d4d490def43560a78.svg",
    searchTags: ["external"],
    tags: ["Display"],
    isDynamicHeight: false,
    isSearchWildcard: true,
  },
  {
    key: "p400w08xjl",
    type: "DATE_PICKER_WIDGET2",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "DatePicker",
    icon: "/static/media/icon.42ea2261c1fd35ebb9e4549b5028de76.svg",
    thumbnail: "/static/media/thumbnail.6f2ddea04e74f31810326bef3bc239b8.svg",
    searchTags: ["calendar"],
    tags: ["Inputs"],
    isDynamicHeight: false,
  },
  {
    key: "a0qjo6hkwp",
    type: "DIVIDER_WIDGET",
    rows: 4,
    columns: 20,
    detachFromLayout: false,
    displayName: "Divider",
    icon: "/static/media/icon.2d8f0568561ab85a8d38356420942b62.svg",
    thumbnail: "/static/media/thumbnail.85250b0a5e6d690bf6499c965e769b14.svg",
    searchTags: ["line"],
    tags: ["Layout"],
    isDynamicHeight: false,
  },
  {
    key: "yw5zv5rhqx",
    type: "DOCUMENT_VIEWER_WIDGET",
    rows: 40,
    columns: 24,
    detachFromLayout: false,
    displayName: "Document Viewer",
    icon: "/static/media/icon.3e8abdb084ce856a43fb2ae1354bf17a.svg",
    thumbnail: "/static/media/thumbnail.90b68df6fa9cdb12189336cd5c4ed121.svg",
    searchTags: ["pdf"],
    tags: ["Media"],
    isDynamicHeight: false,
  },
  {
    key: "f5gapb6vtm",
    type: "FILE_PICKER_WIDGET_V2",
    rows: 4,
    columns: 16,
    detachFromLayout: false,
    displayName: "FilePicker",
    icon: "/static/media/icon.46436f7d7af97386342d9f9b55ed79a0.svg",
    thumbnail: "/static/media/thumbnail.164363adea04c44e54b1fd47657a579e.svg",
    searchTags: ["upload"],
    tags: ["Inputs"],
    isDynamicHeight: false,
  },
  {
    key: "ar8pmqni7i",
    type: "FORM_WIDGET",
    rows: 40,
    columns: 24,
    detachFromLayout: false,
    displayName: "Form",
    icon: "/static/media/icon.b5185d0532af5856eedbf89336c549ae.svg",
    thumbnail: "/static/media/thumbnail.3e92e6e159c53c6046da08eae3732a97.svg",
    searchTags: ["group"],
    tags: ["Layout"],
    isDynamicHeight: true,
  },
  {
    key: "6ygjl9r6lp",
    type: "ICON_BUTTON_WIDGET",
    rows: 4,
    columns: 4,
    detachFromLayout: false,
    displayName: "Icon button",
    icon: "/static/media/icon.bb5d55ae67fb1687dff78e66ad1faec0.svg",
    thumbnail: "/static/media/thumbnail.b02b764a52db45d332efa723178a50a4.svg",
    searchTags: ["click", "submit"],
    tags: ["Buttons"],
    isDynamicHeight: false,
  },
  {
    key: "d54ic1xljy",
    type: "IFRAME_WIDGET",
    rows: 32,
    columns: 24,
    detachFromLayout: false,
    displayName: "Iframe",
    icon: "/static/media/icon.99e0e6020fd949e060892f1c6a291b5a.svg",
    thumbnail: "/static/media/thumbnail.542dc4e979f061c70c19af83e2460c35.svg",
    searchTags: ["embed"],
    tags: ["Display"],
    isDynamicHeight: false,
  },
  {
    key: "04wk4u9w3n",
    type: "IMAGE_WIDGET",
    rows: 12,
    columns: 12,
    detachFromLayout: false,
    displayName: "Image",
    icon: "/static/media/icon.0ca0b0d56b58addbf45114ae5a79e88a.svg",
    thumbnail: "/static/media/thumbnail.74089bb21dfcb5890f6b106f93c3b9c8.svg",
    tags: ["Media"],
    isDynamicHeight: false,
  },
  {
    key: "uriawgeo0y",
    type: "INPUT_WIDGET_V2",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "Input",
    icon: "/static/media/icon.f4dd498ee72f01127e5bfe3a1a8ad671.svg",
    thumbnail: "/static/media/thumbnail.a4eac1a16753ea7fc54cac7731f9ea6a.svg",
    searchTags: ["form", "text input", "number", "textarea"],
    tags: ["Suggested", "Inputs"],
    isDynamicHeight: false,
  },
  {
    key: "fcghc38db4",
    type: "JSON_FORM_WIDGET",
    rows: 41,
    columns: 25,
    detachFromLayout: false,
    displayName: "JSON Form",
    icon: "/static/media/icon.7df6f087cceba86a9a5481b6c7f4871d.svg",
    thumbnail: "/static/media/thumbnail.1092bbfe332a9ea2da2aa7e3efd01db2.svg",
    tags: ["Suggested", "Layout"],
    isDynamicHeight: true,
  },
  {
    key: "s22yz0w5lh",
    type: "LIST_WIDGET_V2",
    rows: 40,
    columns: 24,
    detachFromLayout: false,
    displayName: "List",
    icon: "/static/media/icon.25a47fc4c639c22e8da41e27e998b550.svg",
    thumbnail: "/static/media/thumbnail.469b7e8d3a04de93014af9a7bc9ebe56.svg",
    tags: ["Suggested", "Display"],
    isDynamicHeight: false,
  },
  {
    key: "qo5c1p7v27",
    type: "MAP_WIDGET",
    rows: 40,
    columns: 24,
    detachFromLayout: false,
    displayName: "Map",
    icon: "/static/media/icon.90428b358e5d271b311873239e736e78.svg",
    thumbnail: "/static/media/thumbnail.a67ea5d45fc8fa84754d11a409af1f33.svg",
    tags: ["Content"],
    isDynamicHeight: false,
  },
  {
    key: "eay04glz1w",
    type: "MAP_CHART_WIDGET",
    rows: 32,
    columns: 24,
    detachFromLayout: false,
    displayName: "Map Chart",
    icon: "/static/media/icon.d4b5093e56d789ca6b4656cb925d4a7c.svg",
    thumbnail: "/static/media/thumbnail.ce80e3c5d01fe182e07c4e48df5bb6d8.svg",
    searchTags: ["graph", "visuals", "visualisations"],
    tags: ["Display"],
    isDynamicHeight: false,
  },
  {
    key: "5nn4hzisoi",
    type: "MENU_BUTTON_WIDGET",
    rows: 4,
    columns: 16,
    detachFromLayout: false,
    displayName: "Menu button",
    icon: "/static/media/icon.33a4057faa7212849fa6ff1f15f2da0f.svg",
    thumbnail: "/static/media/thumbnail.57aa4a1b6aaab3ade6ce3bc3a1177531.svg",
    tags: ["Buttons"],
    isDynamicHeight: false,
  },
  {
    key: "3c4n5pxan3",
    type: "MODAL_WIDGET",
    rows: 24,
    columns: 24,
    detachFromLayout: true,
    displayName: "Modal",
    icon: "/static/media/icon.4b2718a4b16cd370332555ef28633b8d.svg",
    thumbnail: "/static/media/thumbnail.c20e144182d9af35cde59e39d9afe632.svg",
    searchTags: ["dialog", "popup", "notification"],
    tags: ["Layout"],
    isDynamicHeight: true,
  },
  {
    key: "5aqptqpjk8",
    type: "MULTI_SELECT_TREE_WIDGET",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "Multi TreeSelect",
    icon: "/static/media/icon.8e0988176ef2c5bf3b641e36cf34d0e3.svg",
    thumbnail: "/static/media/thumbnail.d1dd4f1ce55fa3e93b8a4de404e481cc.svg",
    searchTags: ["dropdown", "multiselecttree"],
    tags: ["Select"],
    isDynamicHeight: false,
  },
  {
    key: "pl9it5l7wk",
    type: "MULTI_SELECT_WIDGET_V2",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "MultiSelect",
    icon: "/static/media/icon.a6466795dfb50a097cd9e9b3a05e61d3.svg",
    thumbnail: "/static/media/thumbnail.9770668f6ae1bf4d3b439b1d90948e94.svg",
    searchTags: ["dropdown", "tags"],
    tags: ["Select"],
    isDynamicHeight: false,
  },
  {
    key: "h9xx56nt2d",
    type: "NUMBER_SLIDER_WIDGET",
    rows: 8,
    columns: 40,
    detachFromLayout: false,
    displayName: "Number Slider",
    icon: "/static/media/icon.f1c0a9f4c08502584d745ce2fb2fbb23.svg",
    thumbnail: "/static/media/thumbnail.493d650547eaef7533c2c9deda058338.svg",
    searchTags: ["range"],
    tags: ["Sliders"],
    isDynamicHeight: false,
  },
  {
    key: "056j897tz6",
    type: "PHONE_INPUT_WIDGET",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "Phone Input",
    icon: "/static/media/icon.ac26f9e730b8da17abb61653b307ac56.svg",
    thumbnail: "/static/media/thumbnail.56a649533a64f5ed11640a74c51360fc.svg",
    searchTags: ["call"],
    tags: ["Inputs"],
    isDynamicHeight: false,
  },
  {
    key: "6yfazgjt4l",
    type: "PROGRESS_WIDGET",
    rows: 4,
    columns: 28,
    detachFromLayout: false,
    displayName: "Progress",
    icon: "/static/media/icon.4427604c19b1a86ffdf81f77957632bd.svg",
    thumbnail: "/static/media/thumbnail.b99132f26b7fae778b21ebedf9748338.svg",
    searchTags: ["percent"],
    tags: ["Content"],
    isDynamicHeight: false,
  },
  {
    key: "0m1lvxau07",
    type: "RADIO_GROUP_WIDGET",
    rows: 6,
    columns: 20,
    detachFromLayout: false,
    displayName: "Radio Group",
    icon: "/static/media/icon.40dce3f754d60606830f83a23d1ac328.svg",
    thumbnail: "/static/media/thumbnail.f1ed00d0b9588bed20e6359d9d03a790.svg",
    searchTags: ["choice"],
    tags: ["Toggles"],
    isDynamicHeight: true,
  },
  {
    key: "qa03f2s3sf",
    type: "RANGE_SLIDER_WIDGET",
    rows: 8,
    columns: 40,
    detachFromLayout: false,
    displayName: "Range Slider",
    icon: "/static/media/icon.e4ce18dbcf9dae28943ed44865692370.svg",
    thumbnail: "/static/media/thumbnail.dba83a44257e147ae015c36470b046a4.svg",
    tags: ["Sliders"],
    isDynamicHeight: false,
  },
  {
    key: "svb95k56go",
    type: "RATE_WIDGET",
    rows: 4,
    columns: 20,
    detachFromLayout: false,
    displayName: "Rating",
    icon: "/static/media/icon.256b2c690197b61f6e5a7fafd2d1f195.svg",
    thumbnail: "/static/media/thumbnail.49bc434e5d61fee59efddf43533e90cf.svg",
    searchTags: ["stars", "rate"],
    tags: ["Content"],
    isDynamicHeight: true,
  },
  {
    key: "6dl4lzbk58",
    type: "RICH_TEXT_EDITOR_WIDGET",
    rows: 20,
    columns: 24,
    detachFromLayout: false,
    displayName: "Rich Text Editor",
    icon: "/static/media/icon.06d230b800553d2866ed7f3ec0474e3e.svg",
    thumbnail: "/static/media/thumbnail.b19d2640527b6f3fd53fe51eb20774d0.svg",
    searchTags: ["input", "rte"],
    tags: ["Inputs"],
    isDynamicHeight: false,
  },
  {
    key: "dsycmkvolj",
    type: "SELECT_WIDGET",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "Select",
    icon: "/static/media/icon.a62f1265827a5af78966a733a05697db.svg",
    thumbnail: "/static/media/thumbnail.9aef24cb159e9c6e4d0f1d7ca4fb0c79.svg",
    searchTags: ["dropdown"],
    tags: ["Suggested", "Select"],
    isDynamicHeight: false,
  },
  {
    key: "ucirc4qnmo",
    type: "STATBOX_WIDGET",
    rows: 14,
    columns: 22,
    detachFromLayout: false,
    displayName: "Stats Box",
    icon: "/static/media/icon.********************************.svg",
    thumbnail: "/static/media/thumbnail.8acd8070c8c88f657e7fd6c650aecc0c.svg",
    searchTags: ["statbox"],
    tags: ["Display"],
    isDynamicHeight: true,
  },
  {
    key: "8v0riszj92",
    type: "SWITCH_WIDGET",
    rows: 4,
    columns: 12,
    detachFromLayout: false,
    displayName: "Switch",
    icon: "/static/media/icon.c0b4c50c8e9e6060b0525341b26d016d.svg",
    thumbnail: "/static/media/thumbnail.cd04c6d6a11266a806cebe39829607cb.svg",
    searchTags: ["boolean"],
    tags: ["Toggles"],
    isDynamicHeight: true,
  },
  {
    key: "c9sp3ik1t7",
    type: "SWITCH_GROUP_WIDGET",
    rows: 6,
    columns: 26,
    detachFromLayout: false,
    displayName: "Switch Group",
    icon: "/static/media/icon.ecfad328a17009700cca52006029f273.svg",
    thumbnail: "/static/media/thumbnail.a24508dfef3d45bdae750aa0d2e016c9.svg",
    tags: ["Toggles"],
    isDynamicHeight: true,
  },
  {
    key: "529hdxbkhk",
    type: "TABLE_WIDGET_V2",
    rows: 28,
    columns: 34,
    detachFromLayout: false,
    displayName: "Table",
    icon: "/static/media/icon.c4395b694ca4b5a84741345b057b6866.svg",
    thumbnail: "/static/media/thumbnail.d0492f06681daa69baf92b07d3829dfc.svg",
    tags: ["Suggested", "Display"],
    isDynamicHeight: false,
  },
  {
    key: "fjp41lkkbp",
    type: "TABS_WIDGET",
    rows: 15,
    columns: 24,
    detachFromLayout: false,
    displayName: "Tabs",
    icon: "/static/media/icon.02aab13974bb96ab5effaedb467a923c.svg",
    thumbnail: "/static/media/thumbnail.902d280515fd575657e8ccf99545112b.svg",
    tags: ["Layout"],
    isDynamicHeight: true,
  },
  {
    key: "rb8iim4fz3",
    type: "TEXT_WIDGET",
    rows: 4,
    columns: 16,
    detachFromLayout: false,
    displayName: "Text",
    icon: "/static/media/icon.a55b701a2ae86aa2c6718ecb7b4083f0.svg",
    thumbnail: "/static/media/thumbnail.0c129b82c9b3e4cd4920563b289659ab.svg",
    searchTags: ["typography", "paragraph", "label"],
    tags: ["Suggested", "Content"],
    isDynamicHeight: true,
  },
  {
    key: "z647lz4p3h",
    type: "SINGLE_SELECT_TREE_WIDGET",
    rows: 7,
    columns: 20,
    detachFromLayout: false,
    displayName: "TreeSelect",
    icon: "/static/media/icon.1d5dafb1bceb767ecba0541ed445bd6c.svg",
    thumbnail: "/static/media/thumbnail.bc59917a102b6adb6de852484370cc41.svg",
    searchTags: ["dropdown", "singleselecttree"],
    tags: ["Select"],
    isDynamicHeight: false,
  },
  {
    key: "drq5tww9hb",
    type: "VIDEO_WIDGET",
    rows: 28,
    columns: 24,
    detachFromLayout: false,
    displayName: "Video",
    icon: "/static/media/icon.ad88290937e03de43848bdb4443a4d1f.svg",
    thumbnail: "/static/media/thumbnail.92f170b89cd60e633eb4db597d822682.svg",
    searchTags: ["youtube"],
    tags: ["Media"],
    isDynamicHeight: false,
  },
  {
    rows: 63,
    columns: 31,
    type: "BUILDING_BLOCK",
    displayName: "Table Lookup",
    icon: "https://images.ctfassets.net/lpvian6u6i39/4S9uBuruavbeWqFiblPKb7/4c1c7b786f186cf3db6436755bf402da/table-lookup-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/4S9uBuruavbeWqFiblPKb7/4c1c7b786f186cf3db6436755bf402da/table-lookup-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 64,
    columns: 31,
    type: "BUILDING_BLOCK",
    displayName: "Table Edit",
    icon: "https://images.ctfassets.net/lpvian6u6i39/1XY63gljYfJ3F9F0bwuM3z/3d50c0391700bc06798662604b24d4c4/table-edit-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/1XY63gljYfJ3F9F0bwuM3z/3d50c0391700bc06798662604b24d4c4/table-edit-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 83,
    columns: 31,
    type: "BUILDING_BLOCK",
    displayName: "Table Drilldown",
    icon: "https://images.ctfassets.net/lpvian6u6i39/5jSDNvOTjM6h7y00lTpaxS/c8b0fad9369bcb5a8413861053edaaea/table-drilldown-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/5jSDNvOTjM6h7y00lTpaxS/c8b0fad9369bcb5a8413861053edaaea/table-drilldown-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 51,
    columns: 22,
    type: "BUILDING_BLOCK",
    displayName: "Chart Data",
    icon: "https://images.ctfassets.net/lpvian6u6i39/1GDbaMj7ArjsFpNG9sao20/bcff52627213915f5f82ffd63225b9a3/chart-data-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/1GDbaMj7ArjsFpNG9sao20/bcff52627213915f5f82ffd63225b9a3/chart-data-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 49,
    columns: 16,
    type: "BUILDING_BLOCK",
    displayName: "Upload File",
    icon: "https://images.ctfassets.net/lpvian6u6i39/3kE2ldi8W7LLFT4mV2zT4f/e8407d49bdab5149d914797758625cd2/upload-file-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/3kE2ldi8W7LLFT4mV2zT4f/e8407d49bdab5149d914797758625cd2/upload-file-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 65,
    columns: 24,
    type: "BUILDING_BLOCK",
    displayName: "Dynamic Form",
    icon: "https://images.ctfassets.net/lpvian6u6i39/7Kfrywfr5vyYOmuG2CF22u/fe63c6bff6fe0a1582c278ce8c8500a9/dynamic-form-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/7Kfrywfr5vyYOmuG2CF22u/fe63c6bff6fe0a1582c278ce8c8500a9/dynamic-form-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 49,
    columns: 31,
    type: "BUILDING_BLOCK",
    displayName: "Chart Drilldown",
    icon: "https://images.ctfassets.net/lpvian6u6i39/JThUl8yPN534g9xGwKXmt/98db3a5545fcfd424ca865a69231b5ab/chart-drilldown-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/JThUl8yPN534g9xGwKXmt/98db3a5545fcfd424ca865a69231b5ab/chart-drilldown-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
  {
    rows: 70,
    columns: 31,
    type: "BUILDING_BLOCK",
    displayName: "List Lookup",
    icon: "https://images.ctfassets.net/lpvian6u6i39/3YDzmq2HSycFREF7XSzp32/d09a5a7182b265ce6bf0ef7f75771ee9/list-lookup-building-block-thumbnail.svg",
    thumbnail:
      "https://images.ctfassets.net/lpvian6u6i39/3YDzmq2HSycFREF7XSzp32/d09a5a7182b265ce6bf0ef7f75771ee9/list-lookup-building-block-thumbnail.svg",
    tags: ["Building Blocks"],
  },
];

export const groupedCards = {
  "Building Blocks": [
    {
      rows: 63,
      columns: 31,
      type: "BUILDING_BLOCK",
      displayName: "Table Lookup",
      icon: "https://images.ctfassets.net/lpvian6u6i39/4S9uBuruavbeWqFiblPKb7/4c1c7b786f186cf3db6436755bf402da/table-lookup-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/4S9uBuruavbeWqFiblPKb7/4c1c7b786f186cf3db6436755bf402da/table-lookup-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 64,
      columns: 31,
      type: "BUILDING_BLOCK",
      displayName: "Table Edit",
      icon: "https://images.ctfassets.net/lpvian6u6i39/1XY63gljYfJ3F9F0bwuM3z/3d50c0391700bc06798662604b24d4c4/table-edit-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/1XY63gljYfJ3F9F0bwuM3z/3d50c0391700bc06798662604b24d4c4/table-edit-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 83,
      columns: 31,
      type: "BUILDING_BLOCK",
      displayName: "Table Drilldown",
      icon: "https://images.ctfassets.net/lpvian6u6i39/5jSDNvOTjM6h7y00lTpaxS/c8b0fad9369bcb5a8413861053edaaea/table-drilldown-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/5jSDNvOTjM6h7y00lTpaxS/c8b0fad9369bcb5a8413861053edaaea/table-drilldown-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 51,
      columns: 22,
      type: "BUILDING_BLOCK",
      displayName: "Chart Data",
      icon: "https://images.ctfassets.net/lpvian6u6i39/1GDbaMj7ArjsFpNG9sao20/bcff52627213915f5f82ffd63225b9a3/chart-data-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/1GDbaMj7ArjsFpNG9sao20/bcff52627213915f5f82ffd63225b9a3/chart-data-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 49,
      columns: 16,
      type: "BUILDING_BLOCK",
      displayName: "Upload File",
      icon: "https://images.ctfassets.net/lpvian6u6i39/3kE2ldi8W7LLFT4mV2zT4f/e8407d49bdab5149d914797758625cd2/upload-file-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/3kE2ldi8W7LLFT4mV2zT4f/e8407d49bdab5149d914797758625cd2/upload-file-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 65,
      columns: 24,
      type: "BUILDING_BLOCK",
      displayName: "Dynamic Form",
      icon: "https://images.ctfassets.net/lpvian6u6i39/7Kfrywfr5vyYOmuG2CF22u/fe63c6bff6fe0a1582c278ce8c8500a9/dynamic-form-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/7Kfrywfr5vyYOmuG2CF22u/fe63c6bff6fe0a1582c278ce8c8500a9/dynamic-form-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 49,
      columns: 31,
      type: "BUILDING_BLOCK",
      displayName: "Chart Drilldown",
      icon: "https://images.ctfassets.net/lpvian6u6i39/JThUl8yPN534g9xGwKXmt/98db3a5545fcfd424ca865a69231b5ab/chart-drilldown-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/JThUl8yPN534g9xGwKXmt/98db3a5545fcfd424ca865a69231b5ab/chart-drilldown-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
    {
      rows: 70,
      columns: 31,
      type: "BUILDING_BLOCK",
      displayName: "List Lookup",
      icon: "https://images.ctfassets.net/lpvian6u6i39/3YDzmq2HSycFREF7XSzp32/d09a5a7182b265ce6bf0ef7f75771ee9/list-lookup-building-block-thumbnail.svg",
      thumbnail:
        "https://images.ctfassets.net/lpvian6u6i39/3YDzmq2HSycFREF7XSzp32/d09a5a7182b265ce6bf0ef7f75771ee9/list-lookup-building-block-thumbnail.svg",
      tags: ["Building Blocks"],
    },
  ],
  Suggested: [
    {
      key: "uriawgeo0y",
      type: "INPUT_WIDGET_V2",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Input",
      icon: "/static/media/icon.f4dd498ee72f01127e5bfe3a1a8ad671.svg",
      thumbnail: "/static/media/thumbnail.a4eac1a16753ea7fc54cac7731f9ea6a.svg",
      searchTags: ["form", "text input", "number", "textarea"],
      tags: ["Suggested", "Inputs"],
      isDynamicHeight: false,
    },
    {
      key: "fcghc38db4",
      type: "JSON_FORM_WIDGET",
      rows: 41,
      columns: 25,
      detachFromLayout: false,
      displayName: "JSON Form",
      icon: "/static/media/icon.7df6f087cceba86a9a5481b6c7f4871d.svg",
      thumbnail: "/static/media/thumbnail.1092bbfe332a9ea2da2aa7e3efd01db2.svg",
      tags: ["Suggested", "Layout"],
      isDynamicHeight: true,
    },
    {
      key: "s22yz0w5lh",
      type: "LIST_WIDGET_V2",
      rows: 40,
      columns: 24,
      detachFromLayout: false,
      displayName: "List",
      icon: "/static/media/icon.25a47fc4c639c22e8da41e27e998b550.svg",
      thumbnail: "/static/media/thumbnail.469b7e8d3a04de93014af9a7bc9ebe56.svg",
      tags: ["Suggested", "Display"],
      isDynamicHeight: false,
    },
    {
      key: "dsycmkvolj",
      type: "SELECT_WIDGET",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Select",
      icon: "/static/media/icon.a62f1265827a5af78966a733a05697db.svg",
      thumbnail: "/static/media/thumbnail.9aef24cb159e9c6e4d0f1d7ca4fb0c79.svg",
      searchTags: ["dropdown"],
      tags: ["Suggested", "Select"],
      isDynamicHeight: false,
    },
    {
      key: "529hdxbkhk",
      type: "TABLE_WIDGET_V2",
      rows: 28,
      columns: 34,
      detachFromLayout: false,
      displayName: "Table",
      icon: "/static/media/icon.c4395b694ca4b5a84741345b057b6866.svg",
      thumbnail: "/static/media/thumbnail.d0492f06681daa69baf92b07d3829dfc.svg",
      tags: ["Suggested", "Display"],
      isDynamicHeight: false,
    },
    {
      key: "rb8iim4fz3",
      type: "TEXT_WIDGET",
      rows: 4,
      columns: 16,
      detachFromLayout: false,
      displayName: "Text",
      icon: "/static/media/icon.a55b701a2ae86aa2c6718ecb7b4083f0.svg",
      thumbnail: "/static/media/thumbnail.0c129b82c9b3e4cd4920563b289659ab.svg",
      searchTags: ["typography", "paragraph", "label"],
      tags: ["Suggested", "Content"],
      isDynamicHeight: true,
    },
  ],
  Inputs: [
    {
      key: "3z6ir71rj0",
      type: "CURRENCY_INPUT_WIDGET",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Currency Input",
      icon: "/static/media/icon.5a8f2fe74616b133e5165fdd06237cd9.svg",
      thumbnail: "/static/media/thumbnail.b14e0307b1b312b9e9ca9580d118af39.svg",
      searchTags: ["amount", "total"],
      tags: ["Inputs"],
      isDynamicHeight: false,
    },
    {
      key: "p400w08xjl",
      type: "DATE_PICKER_WIDGET2",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "DatePicker",
      icon: "/static/media/icon.42ea2261c1fd35ebb9e4549b5028de76.svg",
      thumbnail: "/static/media/thumbnail.6f2ddea04e74f31810326bef3bc239b8.svg",
      searchTags: ["calendar"],
      tags: ["Inputs"],
      isDynamicHeight: false,
    },
    {
      key: "f5gapb6vtm",
      type: "FILE_PICKER_WIDGET_V2",
      rows: 4,
      columns: 16,
      detachFromLayout: false,
      displayName: "FilePicker",
      icon: "/static/media/icon.46436f7d7af97386342d9f9b55ed79a0.svg",
      thumbnail: "/static/media/thumbnail.164363adea04c44e54b1fd47657a579e.svg",
      searchTags: ["upload"],
      tags: ["Inputs"],
      isDynamicHeight: false,
    },
    {
      key: "uriawgeo0y",
      type: "INPUT_WIDGET_V2",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Input",
      icon: "/static/media/icon.f4dd498ee72f01127e5bfe3a1a8ad671.svg",
      thumbnail: "/static/media/thumbnail.a4eac1a16753ea7fc54cac7731f9ea6a.svg",
      searchTags: ["form", "text input", "number", "textarea"],
      tags: ["Suggested", "Inputs"],
      isDynamicHeight: false,
    },
    {
      key: "056j897tz6",
      type: "PHONE_INPUT_WIDGET",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Phone Input",
      icon: "/static/media/icon.ac26f9e730b8da17abb61653b307ac56.svg",
      thumbnail: "/static/media/thumbnail.56a649533a64f5ed11640a74c51360fc.svg",
      searchTags: ["call"],
      tags: ["Inputs"],
      isDynamicHeight: false,
    },
    {
      key: "6dl4lzbk58",
      type: "RICH_TEXT_EDITOR_WIDGET",
      rows: 20,
      columns: 24,
      detachFromLayout: false,
      displayName: "Rich Text Editor",
      icon: "/static/media/icon.06d230b800553d2866ed7f3ec0474e3e.svg",
      thumbnail: "/static/media/thumbnail.b19d2640527b6f3fd53fe51eb20774d0.svg",
      searchTags: ["input", "rte"],
      tags: ["Inputs"],
      isDynamicHeight: false,
    },
  ],
  Buttons: [
    {
      key: "4qidp9t2f3",
      type: "BUTTON_WIDGET",
      rows: 4,
      columns: 16,
      detachFromLayout: false,
      displayName: "Button",
      icon: "/static/media/icon.cb7371e9a48acf9db0f2f3ea0a714255.svg",
      thumbnail: "/static/media/thumbnail.a348658e996feaad96cadc30d99374ff.svg",
      searchTags: ["click", "submit"],
      tags: ["Buttons"],
      isDynamicHeight: false,
    },
    {
      key: "sn7f66bkdl",
      type: "BUTTON_GROUP_WIDGET",
      rows: 4,
      columns: 24,
      detachFromLayout: false,
      displayName: "Button Group",
      icon: "/static/media/icon.ae1401b479d77b1908271bdfed6d12ba.svg",
      thumbnail: "/static/media/thumbnail.eaa22f923be763e8779a46432554f1ed.svg",
      searchTags: ["click", "submit"],
      tags: ["Buttons"],
      isDynamicHeight: false,
    },
    {
      key: "6ygjl9r6lp",
      type: "ICON_BUTTON_WIDGET",
      rows: 4,
      columns: 4,
      detachFromLayout: false,
      displayName: "Icon button",
      icon: "/static/media/icon.bb5d55ae67fb1687dff78e66ad1faec0.svg",
      thumbnail: "/static/media/thumbnail.b02b764a52db45d332efa723178a50a4.svg",
      searchTags: ["click", "submit"],
      tags: ["Buttons"],
      isDynamicHeight: false,
    },
    {
      key: "5nn4hzisoi",
      type: "MENU_BUTTON_WIDGET",
      rows: 4,
      columns: 16,
      detachFromLayout: false,
      displayName: "Menu button",
      icon: "/static/media/icon.33a4057faa7212849fa6ff1f15f2da0f.svg",
      thumbnail: "/static/media/thumbnail.57aa4a1b6aaab3ade6ce3bc3a1177531.svg",
      tags: ["Buttons"],
      isDynamicHeight: false,
    },
  ],
  Select: [
    {
      key: "5aqptqpjk8",
      type: "MULTI_SELECT_TREE_WIDGET",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Multi TreeSelect",
      icon: "/static/media/icon.8e0988176ef2c5bf3b641e36cf34d0e3.svg",
      thumbnail: "/static/media/thumbnail.d1dd4f1ce55fa3e93b8a4de404e481cc.svg",
      searchTags: ["dropdown", "multiselecttree"],
      tags: ["Select"],
      isDynamicHeight: false,
    },
    {
      key: "pl9it5l7wk",
      type: "MULTI_SELECT_WIDGET_V2",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "MultiSelect",
      icon: "/static/media/icon.a6466795dfb50a097cd9e9b3a05e61d3.svg",
      thumbnail: "/static/media/thumbnail.9770668f6ae1bf4d3b439b1d90948e94.svg",
      searchTags: ["dropdown", "tags"],
      tags: ["Select"],
      isDynamicHeight: false,
    },
    {
      key: "dsycmkvolj",
      type: "SELECT_WIDGET",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "Select",
      icon: "/static/media/icon.a62f1265827a5af78966a733a05697db.svg",
      thumbnail: "/static/media/thumbnail.9aef24cb159e9c6e4d0f1d7ca4fb0c79.svg",
      searchTags: ["dropdown"],
      tags: ["Suggested", "Select"],
      isDynamicHeight: false,
    },
    {
      key: "z647lz4p3h",
      type: "SINGLE_SELECT_TREE_WIDGET",
      rows: 7,
      columns: 20,
      detachFromLayout: false,
      displayName: "TreeSelect",
      icon: "/static/media/icon.1d5dafb1bceb767ecba0541ed445bd6c.svg",
      thumbnail: "/static/media/thumbnail.bc59917a102b6adb6de852484370cc41.svg",
      searchTags: ["dropdown", "singleselecttree"],
      tags: ["Select"],
      isDynamicHeight: false,
    },
  ],
  Display: [
    {
      key: "fmfa4jwi62",
      type: "CHART_WIDGET",
      rows: 32,
      columns: 24,
      detachFromLayout: false,
      displayName: "Chart",
      icon: "/static/media/icon.07abfb45c3867c2e1b1d5d4818b7078c.svg",
      thumbnail: "/static/media/thumbnail.98fb19b2c4f99671c0888284cf939f3a.svg",
      searchTags: ["graph", "visuals", "visualisations"],
      tags: ["Display"],
      isDynamicHeight: false,
    },
    {
      key: "1fxwqiv7ag",
      type: "CUSTOM_WIDGET",
      rows: 30,
      columns: 23,
      detachFromLayout: false,
      displayName: "Custom",
      icon: "/static/media/icon.ff37253a5ce2f5284c42ede26ce165f9.svg",
      thumbnail: "/static/media/thumbnail.a7d7cde44fad9a0d4d490def43560a78.svg",
      searchTags: ["external"],
      tags: ["Display"],
      isDynamicHeight: false,
      isSearchWildcard: true,
    },
    {
      key: "d54ic1xljy",
      type: "IFRAME_WIDGET",
      rows: 32,
      columns: 24,
      detachFromLayout: false,
      displayName: "Iframe",
      icon: "/static/media/icon.99e0e6020fd949e060892f1c6a291b5a.svg",
      thumbnail: "/static/media/thumbnail.542dc4e979f061c70c19af83e2460c35.svg",
      searchTags: ["embed"],
      tags: ["Display"],
      isDynamicHeight: false,
    },
    {
      key: "s22yz0w5lh",
      type: "LIST_WIDGET_V2",
      rows: 40,
      columns: 24,
      detachFromLayout: false,
      displayName: "List",
      icon: "/static/media/icon.25a47fc4c639c22e8da41e27e998b550.svg",
      thumbnail: "/static/media/thumbnail.469b7e8d3a04de93014af9a7bc9ebe56.svg",
      tags: ["Suggested", "Display"],
      isDynamicHeight: false,
    },
    {
      key: "eay04glz1w",
      type: "MAP_CHART_WIDGET",
      rows: 32,
      columns: 24,
      detachFromLayout: false,
      displayName: "Map Chart",
      icon: "/static/media/icon.d4b5093e56d789ca6b4656cb925d4a7c.svg",
      thumbnail: "/static/media/thumbnail.ce80e3c5d01fe182e07c4e48df5bb6d8.svg",
      searchTags: ["graph", "visuals", "visualisations"],
      tags: ["Display"],
      isDynamicHeight: false,
    },
    {
      key: "ucirc4qnmo",
      type: "STATBOX_WIDGET",
      rows: 14,
      columns: 22,
      detachFromLayout: false,
      displayName: "Stats Box",
      icon: "/static/media/icon.********************************.svg",
      thumbnail: "/static/media/thumbnail.8acd8070c8c88f657e7fd6c650aecc0c.svg",
      searchTags: ["statbox"],
      tags: ["Display"],
      isDynamicHeight: true,
    },
    {
      key: "529hdxbkhk",
      type: "TABLE_WIDGET_V2",
      rows: 28,
      columns: 34,
      detachFromLayout: false,
      displayName: "Table",
      icon: "/static/media/icon.c4395b694ca4b5a84741345b057b6866.svg",
      thumbnail: "/static/media/thumbnail.d0492f06681daa69baf92b07d3829dfc.svg",
      tags: ["Suggested", "Display"],
      isDynamicHeight: false,
    },
  ],
  Layout: [
    {
      key: "k0em99zxft",
      type: "CONTAINER_WIDGET",
      rows: 10,
      columns: 24,
      detachFromLayout: false,
      displayName: "Container",
      icon: "/static/media/icon.dcc052e58db911c4c8955c200f4bcf5c.svg",
      thumbnail: "/static/media/thumbnail.6cb355b93146b5347bbb048a568ed446.svg",
      searchTags: ["div", "parent", "group"],
      tags: ["Layout"],
      isDynamicHeight: true,
    },
    {
      key: "a0qjo6hkwp",
      type: "DIVIDER_WIDGET",
      rows: 4,
      columns: 20,
      detachFromLayout: false,
      displayName: "Divider",
      icon: "/static/media/icon.2d8f0568561ab85a8d38356420942b62.svg",
      thumbnail: "/static/media/thumbnail.85250b0a5e6d690bf6499c965e769b14.svg",
      searchTags: ["line"],
      tags: ["Layout"],
      isDynamicHeight: false,
    },
    {
      key: "ar8pmqni7i",
      type: "FORM_WIDGET",
      rows: 40,
      columns: 24,
      detachFromLayout: false,
      displayName: "Form",
      icon: "/static/media/icon.b5185d0532af5856eedbf89336c549ae.svg",
      thumbnail: "/static/media/thumbnail.3e92e6e159c53c6046da08eae3732a97.svg",
      searchTags: ["group"],
      tags: ["Layout"],
      isDynamicHeight: true,
    },
    {
      key: "fcghc38db4",
      type: "JSON_FORM_WIDGET",
      rows: 41,
      columns: 25,
      detachFromLayout: false,
      displayName: "JSON Form",
      icon: "/static/media/icon.7df6f087cceba86a9a5481b6c7f4871d.svg",
      thumbnail: "/static/media/thumbnail.1092bbfe332a9ea2da2aa7e3efd01db2.svg",
      tags: ["Suggested", "Layout"],
      isDynamicHeight: true,
    },
    {
      key: "3c4n5pxan3",
      type: "MODAL_WIDGET",
      rows: 24,
      columns: 24,
      detachFromLayout: true,
      displayName: "Modal",
      icon: "/static/media/icon.4b2718a4b16cd370332555ef28633b8d.svg",
      thumbnail: "/static/media/thumbnail.c20e144182d9af35cde59e39d9afe632.svg",
      searchTags: ["dialog", "popup", "notification"],
      tags: ["Layout"],
      isDynamicHeight: true,
    },
    {
      key: "fjp41lkkbp",
      type: "TABS_WIDGET",
      rows: 15,
      columns: 24,
      detachFromLayout: false,
      displayName: "Tabs",
      icon: "/static/media/icon.02aab13974bb96ab5effaedb467a923c.svg",
      thumbnail: "/static/media/thumbnail.902d280515fd575657e8ccf99545112b.svg",
      tags: ["Layout"],
      isDynamicHeight: true,
    },
  ],
  Media: [
    {
      key: "fvqmankv5n",
      type: "AUDIO_WIDGET",
      rows: 4,
      columns: 28,
      detachFromLayout: false,
      displayName: "Audio",
      icon: "/static/media/icon.07157764485879c0e109bcd2c97db502.svg",
      thumbnail: "/static/media/thumbnail.6be15396ae96265e42ee0b4f0ea47b84.svg",
      searchTags: ["mp3", "sound", "wave", "player"],
      tags: ["Media"],
      isDynamicHeight: false,
    },
    {
      key: "yw5zv5rhqx",
      type: "DOCUMENT_VIEWER_WIDGET",
      rows: 40,
      columns: 24,
      detachFromLayout: false,
      displayName: "Document Viewer",
      icon: "/static/media/icon.3e8abdb084ce856a43fb2ae1354bf17a.svg",
      thumbnail: "/static/media/thumbnail.90b68df6fa9cdb12189336cd5c4ed121.svg",
      searchTags: ["pdf"],
      tags: ["Media"],
      isDynamicHeight: false,
    },
    {
      key: "04wk4u9w3n",
      type: "IMAGE_WIDGET",
      rows: 12,
      columns: 12,
      detachFromLayout: false,
      displayName: "Image",
      icon: "/static/media/icon.0ca0b0d56b58addbf45114ae5a79e88a.svg",
      thumbnail: "/static/media/thumbnail.74089bb21dfcb5890f6b106f93c3b9c8.svg",
      tags: ["Media"],
      isDynamicHeight: false,
    },
    {
      key: "drq5tww9hb",
      type: "VIDEO_WIDGET",
      rows: 28,
      columns: 24,
      detachFromLayout: false,
      displayName: "Video",
      icon: "/static/media/icon.ad88290937e03de43848bdb4443a4d1f.svg",
      thumbnail: "/static/media/thumbnail.92f170b89cd60e633eb4db597d822682.svg",
      searchTags: ["youtube"],
      tags: ["Media"],
      isDynamicHeight: false,
    },
  ],
  Toggles: [
    {
      key: "7ewf25yjl0",
      type: "CHECKBOX_WIDGET",
      rows: 4,
      columns: 12,
      detachFromLayout: false,
      displayName: "Checkbox",
      icon: "/static/media/icon.********************************.svg",
      thumbnail: "/static/media/thumbnail.aa4d27836e27bb6c4c45db14dee8f2b2.svg",
      searchTags: ["boolean"],
      tags: ["Toggles"],
      isDynamicHeight: true,
    },
    {
      key: "zqcq8kn28u",
      type: "CHECKBOX_GROUP_WIDGET",
      rows: 6,
      columns: 23,
      detachFromLayout: false,
      displayName: "Checkbox Group",
      icon: "/static/media/icon.********************************.svg",
      thumbnail: "/static/media/thumbnail.2dc0ec495bdfde499e9ce54c2dd91015.svg",
      tags: ["Toggles"],
      isDynamicHeight: true,
    },
    {
      key: "0m1lvxau07",
      type: "RADIO_GROUP_WIDGET",
      rows: 6,
      columns: 20,
      detachFromLayout: false,
      displayName: "Radio Group",
      icon: "/static/media/icon.40dce3f754d60606830f83a23d1ac328.svg",
      thumbnail: "/static/media/thumbnail.f1ed00d0b9588bed20e6359d9d03a790.svg",
      searchTags: ["choice"],
      tags: ["Toggles"],
      isDynamicHeight: true,
    },
    {
      key: "8v0riszj92",
      type: "SWITCH_WIDGET",
      rows: 4,
      columns: 12,
      detachFromLayout: false,
      displayName: "Switch",
      icon: "/static/media/icon.c0b4c50c8e9e6060b0525341b26d016d.svg",
      thumbnail: "/static/media/thumbnail.cd04c6d6a11266a806cebe39829607cb.svg",
      searchTags: ["boolean"],
      tags: ["Toggles"],
      isDynamicHeight: true,
    },
    {
      key: "c9sp3ik1t7",
      type: "SWITCH_GROUP_WIDGET",
      rows: 6,
      columns: 26,
      detachFromLayout: false,
      displayName: "Switch Group",
      icon: "/static/media/icon.ecfad328a17009700cca52006029f273.svg",
      thumbnail: "/static/media/thumbnail.a24508dfef3d45bdae750aa0d2e016c9.svg",
      tags: ["Toggles"],
      isDynamicHeight: true,
    },
  ],
  Sliders: [
    {
      key: "7vsaskvw9p",
      type: "CATEGORY_SLIDER_WIDGET",
      rows: 8,
      columns: 40,
      detachFromLayout: false,
      displayName: "Category Slider",
      icon: "/static/media/icon.7615ff4a5f55a5c7a3eb377a653a028b.svg",
      thumbnail: "/static/media/thumbnail.06a850cb6ec6946f666e3f6cdce9f5da.svg",
      searchTags: ["range"],
      tags: ["Sliders"],
      isDynamicHeight: false,
    },
    {
      key: "h9xx56nt2d",
      type: "NUMBER_SLIDER_WIDGET",
      rows: 8,
      columns: 40,
      detachFromLayout: false,
      displayName: "Number Slider",
      icon: "/static/media/icon.f1c0a9f4c08502584d745ce2fb2fbb23.svg",
      thumbnail: "/static/media/thumbnail.493d650547eaef7533c2c9deda058338.svg",
      searchTags: ["range"],
      tags: ["Sliders"],
      isDynamicHeight: false,
    },
    {
      key: "qa03f2s3sf",
      type: "RANGE_SLIDER_WIDGET",
      rows: 8,
      columns: 40,
      detachFromLayout: false,
      displayName: "Range Slider",
      icon: "/static/media/icon.e4ce18dbcf9dae28943ed44865692370.svg",
      thumbnail: "/static/media/thumbnail.dba83a44257e147ae015c36470b046a4.svg",
      tags: ["Sliders"],
      isDynamicHeight: false,
    },
  ],
  Content: [
    {
      key: "qo5c1p7v27",
      type: "MAP_WIDGET",
      rows: 40,
      columns: 24,
      detachFromLayout: false,
      displayName: "Map",
      icon: "/static/media/icon.90428b358e5d271b311873239e736e78.svg",
      thumbnail: "/static/media/thumbnail.a67ea5d45fc8fa84754d11a409af1f33.svg",
      tags: ["Content"],
      isDynamicHeight: false,
    },
    {
      key: "6yfazgjt4l",
      type: "PROGRESS_WIDGET",
      rows: 4,
      columns: 28,
      detachFromLayout: false,
      displayName: "Progress",
      icon: "/static/media/icon.4427604c19b1a86ffdf81f77957632bd.svg",
      thumbnail: "/static/media/thumbnail.b99132f26b7fae778b21ebedf9748338.svg",
      searchTags: ["percent"],
      tags: ["Content"],
      isDynamicHeight: false,
    },
    {
      key: "svb95k56go",
      type: "RATE_WIDGET",
      rows: 4,
      columns: 20,
      detachFromLayout: false,
      displayName: "Rating",
      icon: "/static/media/icon.256b2c690197b61f6e5a7fafd2d1f195.svg",
      thumbnail: "/static/media/thumbnail.49bc434e5d61fee59efddf43533e90cf.svg",
      searchTags: ["stars", "rate"],
      tags: ["Content"],
      isDynamicHeight: true,
    },
    {
      key: "rb8iim4fz3",
      type: "TEXT_WIDGET",
      rows: 4,
      columns: 16,
      detachFromLayout: false,
      displayName: "Text",
      icon: "/static/media/icon.a55b701a2ae86aa2c6718ecb7b4083f0.svg",
      thumbnail: "/static/media/thumbnail.0c129b82c9b3e4cd4920563b289659ab.svg",
      searchTags: ["typography", "paragraph", "label"],
      tags: ["Suggested", "Content"],
      isDynamicHeight: true,
    },
  ],
  External: [
    {
      key: "ghuh15z8of",
      type: "AUDIO_RECORDER_WIDGET",
      rows: 7,
      columns: 16,
      detachFromLayout: false,
      displayName: "Audio Recorder",
      icon: "/static/media/icon.5cc50f429e2b62c16ce55c3eedb03ee3.svg",
      thumbnail: "/static/media/thumbnail.4513fbf2818c6a93ab8300818615fcec.svg",
      searchTags: ["sound recorder", "voice recorder"],
      tags: ["External"],
      isDynamicHeight: false,
    },
    {
      key: "ybakb6m5hv",
      type: "CAMERA_WIDGET",
      rows: 33,
      columns: 25,
      detachFromLayout: false,
      displayName: "Camera",
      icon: "/static/media/icon.34eb88a83cfebde8a0a6b0e9383e35f6.svg",
      thumbnail: "/static/media/thumbnail.8cd95033bb6345b71ab6da5c06462725.svg",
      searchTags: ["photo", "video recorder"],
      tags: ["External"],
      isDynamicHeight: false,
    },
    {
      key: "ub61i3g7jm",
      type: "CODE_SCANNER_WIDGET",
      rows: 33,
      columns: 25,
      detachFromLayout: false,
      displayName: "Code Scanner",
      icon: "/static/media/icon.9e807168159cda719250068e053610ea.svg",
      thumbnail: "/static/media/thumbnail.40d4041f63e94b1dd8bca195bf98b596.svg",
      searchTags: [
        "barcode scanner",
        "qr scanner",
        "code detector",
        "barcode reader",
      ],
      tags: ["External"],
      isDynamicHeight: false,
    },
  ],
};
