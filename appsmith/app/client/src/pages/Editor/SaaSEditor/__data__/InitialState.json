{"entities": {"actions": [{"isLoading": false, "config": {"id": "6194d8e1abb49e2fd00812b2", "workspaceId": "5e1c5b2014edee5e49efc06c", "pluginType": "SAAS", "pluginId": "6080f9266b8cfd602957ba72", "name": "Api1", "datasource": {"id": "6143187b6371c22cc093fdcf", "userPermissions": [], "pluginId": "6080f9266b8cfd602957ba72", "messages": [], "isValid": true, "new": false}, "pageId": "6194d8cdabb49e2fd00812b1", "actionConfiguration": {"timeoutInMillisecond": 10000, "paginationType": "NONE", "encodeParamsToggle": true, "pluginSpecifiedTemplates": [{"key": "method", "value": "GET"}, {"key": "sheetUrl", "value": "https://docs.google.com/spreadsheets/d/1HYjlH7T4ii_NZzCg6LvGG0hnqfNOu35XSl-OTo_4K7Y/edit#gid=1562690580"}, {"key": "range", "value": ""}, {"key": "spreadsheetName", "value": ""}, {"key": "tableHeaderIndex", "value": "1"}, {"key": "queryFormat", "value": "ROWS"}, {"key": "rowLimit", "value": "10"}, {"key": "sheetName", "value": "Sheet1"}, {"key": "rowOffset", "value": "0"}, {"key": "rowObject"}, {"key": "rowObjects"}, {"key": "rowIndex", "value": "0"}, {"key": "deleteFormat", "value": "SHEET"}, {"key": "smartSubstitution", "value": true}, {"value": [{}]}]}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "confirmBeforeExecute": false, "userPermissions": ["read:actions", "execute:actions", "manage:actions"], "validName": "Api1"}}], "datasources": "undefined", "pageList": "undefined", "jsExecutions": {}, "plugins": {"list": [], "loading": false, "formConfigs": {"5e687c18fb01e64e6a3f873f": [{"sectionName": "Connection", "children": [{"label": "Use mongo connection string URI key", "configProperty": "datasourceConfiguration.properties[0].key", "controlType": "INPUT_TEXT", "initialValue": "Use mongo connection string URI", "hidden": true}, {"label": "Use mongo connection string URI", "configProperty": "datasourceConfiguration.properties[0].value", "controlType": "DROP_DOWN", "initialValue": "No", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}]}, {"label": "Connection string URI key", "configProperty": "datasourceConfiguration.properties[1].key", "controlType": "INPUT_TEXT", "initialValue": "Connection string URI", "hidden": true}, {"label": "Connection string URI", "placeholderText": "mongodb+srv://<username>:<password>@test-db.swrsq.mongodb.net/myDatabase", "configProperty": "datasourceConfiguration.properties[1].value", "controlType": "INPUT_TEXT", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "NOT_EQUALS", "value": "Yes"}}, {"label": "Connection mode", "configProperty": "datasourceConfiguration.connection.mode", "controlType": "SEGMENTED_CONTROL", "initialValue": "READ_WRITE", "options": [{"label": "Read / Write", "value": "READ_WRITE"}, {"label": "Read only", "value": "READ_ONLY"}], "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}}, {"label": "Connection type", "configProperty": "datasourceConfiguration.connection.type", "initialValue": "DIRECT", "controlType": "DROP_DOWN", "options": [{"label": "Direct connection", "value": "DIRECT"}, {"label": "Replica set", "value": "REPLICA_SET"}], "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}}, {"children": [{"label": "Host address", "configProperty": "datasourceConfiguration.endpoints[*].host", "controlType": "KEYVALUE_ARRAY", "validationMessage": "Please enter a valid host", "validationRegex": "^((?![/:]).)*$", "placeholderText": "myapp.abcde.mongodb.net", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}}, {"label": "Port", "configProperty": "datasourceConfiguration.endpoints[*].port", "dataType": "NUMBER", "controlType": "KEYVALUE_ARRAY", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}}]}, {"label": "Default database name", "placeholderText": "(optional)", "configProperty": "datasourceConfiguration.connection.defaultDatabaseName", "controlType": "INPUT_TEXT", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}}]}, {"sectionName": "Authentication", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}, "children": [{"label": "Database name", "configProperty": "datasourceConfiguration.authentication.databaseName", "controlType": "INPUT_TEXT", "placeholderText": "Database name", "initialValue": "admin"}, {"label": "Authentication type", "configProperty": "datasourceConfiguration.authentication.authType", "controlType": "DROP_DOWN", "initialValue": "SCRAM_SHA_1", "options": [{"label": "SCRAM-SHA-1", "value": "SCRAM_SHA_1"}, {"label": "SCRAM-SHA-256", "value": "SCRAM_SHA_256"}, {"label": "MONGODB-CR", "value": "MONGODB_CR"}]}, {"children": [{"label": "Username", "configProperty": "datasourceConfiguration.authentication.username", "controlType": "INPUT_TEXT", "placeholderText": "Username"}, {"label": "Password", "configProperty": "datasourceConfiguration.authentication.password", "dataType": "PASSWORD", "controlType": "INPUT_TEXT", "placeholderText": "Password", "encrypted": true}]}]}, {"sectionName": "SSL (optional)", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "Yes"}, "children": [{"label": "SSL mode", "configProperty": "datasourceConfiguration.connection.ssl.authType", "controlType": "DROP_DOWN", "initialValue": "DEFAULT", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "DEFAULT"}, {"label": "Enabled", "value": "ENABLED"}, {"label": "Disabled", "value": "DISABLED"}]}]}], "5c9f512f96c1a50004819786": [{"sectionName": "Connection", "id": 1, "children": [{"label": "Connection mode", "configProperty": "datasourceConfiguration.connection.mode", "controlType": "SEGMENTED_CONTROL", "isRequired": true, "initialValue": "READ_WRITE", "options": [{"label": "Read / Write", "value": "READ_WRITE"}, {"label": "Read only", "value": "READ_ONLY"}]}, {"children": [{"label": "Host address", "configProperty": "datasourceConfiguration.endpoints[*].host", "controlType": "KEYVALUE_ARRAY", "validationMessage": "Please enter a valid host", "validationRegex": "^((?![/:]).)*$"}, {"label": "Port", "configProperty": "datasourceConfiguration.endpoints[*].port", "dataType": "NUMBER", "controlType": "KEYVALUE_ARRAY"}]}, {"label": "Database name", "configProperty": "datasourceConfiguration.authentication.databaseName", "controlType": "INPUT_TEXT", "placeholderText": "Database name", "initialValue": "admin"}]}, {"sectionName": "Authentication", "id": 2, "children": [{"children": [{"label": "Username", "configProperty": "datasourceConfiguration.authentication.username", "controlType": "INPUT_TEXT", "placeholderText": "Username"}, {"label": "Password", "configProperty": "datasourceConfiguration.authentication.password", "dataType": "PASSWORD", "controlType": "INPUT_TEXT", "placeholderText": "Password", "encrypted": true}]}]}, {"id": 3, "sectionName": "SSL (optional)", "children": [{"label": "SSL mode", "configProperty": "datasourceConfiguration.connection.ssl.authType", "controlType": "DROP_DOWN", "initialValue": "DEFAULT", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "DEFAULT"}, {"label": "Allow", "value": "ALLOW"}, {"label": "Prefer", "value": "PREFER"}, {"label": "Require", "value": "REQUIRE"}, {"label": "Disable", "value": "DISABLE"}]}]}], "5ca385dc81b37f0004b4db85": [{"sectionName": "General", "children": [{"label": "URL", "configProperty": "datasourceConfiguration.url", "controlType": "INPUT_TEXT", "isRequired": true, "placeholderText": "https://example.com"}, {"label": "Headers", "configProperty": "datasourceConfiguration.headers", "controlType": "KEYVALUE_ARRAY"}, {"label": "Send authentication Information key (do not edit)", "configProperty": "datasourceConfiguration.properties[0].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "isSendSessionEnabled"}, {"label": "Send Appsmith signature header (X-APPSMITH-SIGNATURE)", "configProperty": "datasourceConfiguration.properties[0].value", "controlType": "DROP_DOWN", "isRequired": true, "initialValue": "N", "options": [{"label": "Yes", "value": "Y"}, {"label": "No", "value": "N"}]}, {"label": "Session details signature key key (do not edit)", "configProperty": "datasourceConfiguration.properties[1].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "sessionSignatureKey"}, {"label": "Session details signature key", "configProperty": "datasourceConfiguration.properties[1].value", "controlType": "INPUT_TEXT", "hidden": {"path": "datasourceConfiguration.properties[0].value", "comparison": "EQUALS", "value": "N"}}, {"label": "Authentication type", "configProperty": "datasourceConfiguration.authentication.authenticationType", "controlType": "DROP_DOWN", "options": [{"label": "None", "value": "db<PERSON><PERSON>"}, {"label": "Basic", "value": "basic"}, {"label": "OAuth 2.0", "value": "oAuth2"}, {"label": "API key", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Bearer token", "value": "bearerToken"}]}, {"label": "Grant type", "configProperty": "datasourceConfiguration.authentication.grantType", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": true}, {"label": "Access token URL", "configProperty": "datasourceConfiguration.authentication.accessTokenUrl", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}, {"label": "Client Id", "configProperty": "datasourceConfiguration.authentication.clientId", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}, {"label": "Client secret", "configProperty": "datasourceConfiguration.authentication.clientSecret", "dataType": "PASSWORD", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}, {"label": "<PERSON>ope(s)", "configProperty": "datasourceConfiguration.authentication.scopeString", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}, {"label": "Header prefix", "configProperty": "datasourceConfiguration.authentication.headerPrefix", "controlType": "INPUT_TEXT", "placeholderText": "Bearer (default)", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}, {"label": "Add token to", "configProperty": "datasourceConfiguration.authentication.isTokenHeader", "controlType": "DROP_DOWN", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}, "options": [{"label": "Header", "value": true}, {"label": "Query parameters", "value": false}]}, {"label": "Audience(s)", "configProperty": "datasourceConfiguration.authentication.audience", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}, {"label": "Resource(s)", "configProperty": "datasourceConfiguration.authentication.resource", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": {"path": "datasourceConfiguration.authentication.authenticationType", "comparison": "NOT_EQUALS", "value": "oAuth2"}}]}], "5fbbc39ad1f71d6666c32e4b": [{"sectionName": "Details", "id": 1, "children": [{"label": "Database URL", "configProperty": "datasourceConfiguration.url", "controlType": "INPUT_TEXT", "isRequired": true, "placeholderText": "https://your-project-id.firebaseio.com"}, {"label": "Project Id", "configProperty": "datasourceConfiguration.authentication.username", "controlType": "INPUT_TEXT", "isRequired": true, "initialValue": ""}, {"label": "Service account credentials", "configProperty": "datasourceConfiguration.authentication.password", "controlType": "INPUT_TEXT", "dataType": "PASSWORD", "isRequired": true, "initialValue": ""}]}], "6080f9266b8cfd602957ba72": [{"sectionName": "General", "children": [{"label": "Authentication type", "configProperty": "datasourceConfiguration.authentication.authenticationType", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": true, "initialValue": "oAuth2"}, {"label": "Grant type", "configProperty": "datasourceConfiguration.authentication.grantType", "controlType": "INPUT_TEXT", "isRequired": false, "hidden": true, "initialValue": "authorization_code"}, {"label": "<PERSON><PERSON>", "configProperty": "datasourceConfiguration.authentication.scopeString", "controlType": "DROP_DOWN", "isRequired": true, "options": [{"label": "Read only", "value": "https://www.googleapis.com/auth/spreadsheets.readonly,https://www.googleapis.com/auth/drive.readonly"}, {"label": "Read and write", "value": "https://www.googleapis.com/auth/spreadsheets,https://www.googleapis.com/auth/drive"}], "initialValue": "https://www.googleapis.com/auth/spreadsheets,https://www.googleapis.com/auth/drive"}]}], "6023b4a070eb652de19476d3": [{"sectionName": "Details", "id": 1, "children": [{"label": "S3 service provider key", "configProperty": "datasourceConfiguration.properties[1].key", "controlType": "INPUT_TEXT", "initialValue": "s3Provider", "hidden": true}, {"label": "S3 service provider", "configProperty": "datasourceConfiguration.properties[1].value", "controlType": "DROP_DOWN", "isRequired": true, "initialValue": "amazon-s3", "options": [{"label": "Amazon S3", "value": "amazon-s3"}, {"label": "Upcloud", "value": "upcloud"}, {"label": "Digital Ocean spaces", "value": "digital-ocean-spaces"}, {"label": "<PERSON><PERSON>", "value": "wasabi"}, {"label": "DreamObjects", "value": "dream-objects"}, {"label": "Other", "value": "other"}]}, {"label": "Access key", "configProperty": "datasourceConfiguration.authentication.username", "controlType": "INPUT_TEXT", "initialValue": ""}, {"label": "Secret key", "configProperty": "datasourceConfiguration.authentication.password", "controlType": "INPUT_TEXT", "dataType": "PASSWORD", "initialValue": "", "encrypted": true}, {"label": "Endpoint URL", "configProperty": "datasourceConfiguration.endpoints[0].host", "controlType": "INPUT_TEXT", "initialValue": "", "placeholderText": "user-storage.de-fra1.upcloudobjects.com", "hidden": {"path": "datasourceConfiguration.properties[1].value", "comparison": "EQUALS", "value": "amazon-s3"}}, {"label": "Custom endpoint URL key", "configProperty": "datasourceConfiguration.properties[2].key", "controlType": "INPUT_TEXT", "initialValue": "customRegion", "hidden": true}, {"label": "Region", "configProperty": "datasourceConfiguration.properties[2].value", "controlType": "INPUT_TEXT", "initialValue": "", "placeholderText": "de-fra1", "hidden": {"path": "datasourceConfiguration.properties[1].value", "comparison": "NOT_EQUALS", "value": "other"}}]}], "5f9169920c6d936f469f4c8a": [{"sectionName": "Connection", "id": 1, "children": [{"children": [{"label": "Host address", "configProperty": "datasourceConfiguration.endpoints[*].host", "controlType": "KEYVALUE_ARRAY", "validationMessage": "Please enter a valid host", "validationRegex": "^((?![/:]).)*$"}, {"label": "Port", "configProperty": "datasourceConfiguration.endpoints[*].port", "dataType": "NUMBER", "controlType": "KEYVALUE_ARRAY"}]}, {"label": "Database number", "configProperty": "datasourceConfiguration.authentication.databaseName", "controlType": "INPUT_TEXT", "dataType": "NUMBER", "placeholderText": "0"}]}, {"sectionName": "Authentication", "id": 2, "children": [{"children": [{"label": "Username", "configProperty": "datasourceConfiguration.authentication.username", "controlType": "INPUT_TEXT", "placeholderText": "Username"}, {"label": "Password", "configProperty": "datasourceConfiguration.authentication.password", "dataType": "PASSWORD", "controlType": "INPUT_TEXT", "placeholderText": "Password", "encrypted": true}]}]}], "6138c786168857325f78ef3e": []}, "editorConfigs": {"5e687c18fb01e64e6a3f873f": [{"options": [{"label": "Find document(s)", "value": "FIND"}, {"label": "Insert document(s)", "value": "INSERT"}, {"label": "Update document(s)", "value": "UPDATE"}, {"label": "Delete document(s)", "value": "DELETE"}, {"label": "Count", "value": "COUNT"}, {"label": "Distinct", "value": "DISTINCT"}, {"label": "Aggregate", "value": "AGGREGATE"}, {"label": "Raw", "value": "RAW"}], "label": "Command", "description": "Choose method you would like to use to query the database", "configProperty": "actionConfiguration.formData.command", "controlType": "DROP_DOWN", "initialValue": "FIND"}, {"controlType": "SECTION", "label": "", "_comment": "This section holds all the templates", "children": [{"identifier": "FIND", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'FIND'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Query", "configProperty": "actionConfiguration.formData.find.query", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{rating : {$gte : 9}}"}, {"label": "Sort", "configProperty": "actionConfiguration.formData.find.sort", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{name : 1}"}, {"label": "Projection", "configProperty": "actionConfiguration.formData.find.projection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{name : 1}"}, {"label": "Limit", "configProperty": "actionConfiguration.formData.find.limit", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "10"}, {"label": "<PERSON><PERSON>", "configProperty": "actionConfiguration.formData.find.skip", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "0"}]}]}, {"identifier": "INSERT", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'INSERT'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Documents", "configProperty": "actionConfiguration.formData.insert.documents", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "[ { _id: 1, user: \"abc123\", status: \"A\" } ]"}]}]}, {"identifier": "UPDATE", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'UPDATE'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Query", "configProperty": "actionConfiguration.formData.updateMany.query", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{rating : {$gte : 9}}"}, {"label": "Update", "configProperty": "actionConfiguration.formData.updateMany.update", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{ $inc: { score: 1 } }"}, {"label": "Limit", "configProperty": "actionConfiguration.formData.updateMany.limit", "controlType": "DROP_DOWN", "initialValue": "SINGLE", "options": [{"label": "Single document", "value": "SINGLE"}, {"label": "All matching documents", "value": "ALL"}]}]}]}, {"identifier": "DELETE", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'DELETE'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Query", "configProperty": "actionConfiguration.formData.delete.query", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{rating : {$gte : 9}}"}, {"label": "Limit", "configProperty": "actionConfiguration.formData.delete.limit", "controlType": "DROP_DOWN", "initialValue": "SINGLE", "options": [{"label": "Single document", "value": "SINGLE"}, {"label": "All matching documents", "value": "ALL"}]}]}]}, {"identifier": "COUNT", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'COUNT'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Query", "configProperty": "actionConfiguration.formData.count.query", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{rating : {$gte : 9}}"}]}]}, {"identifier": "DISTINCT", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'DISTINCT'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Query", "configProperty": "actionConfiguration.formData.distinct.query", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "{rating : {$gte : 9}}"}, {"label": "Key", "configProperty": "actionConfiguration.formData.distinct.key", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "name"}]}]}, {"identifier": "AGGREGATE", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'AGGREGATE'}}"}, "children": [{"controlType": "SECTION", "label": "Select collection to query", "children": [{"label": "Collection", "configProperty": "actionConfiguration.formData.collection", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE"}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Array of pipelines", "configProperty": "actionConfiguration.formData.aggregate.arrayPipelines", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "inputType": "JSON", "evaluationSubstitutionType": "TEMPLATE", "placeholderText": "[{ $project: { tags: 1 } }, { $unwind: \"$tags\" }, { $group: { _id: \"$tags\", count: { $sum : 1 } } }  ]"}]}]}, {"identifier": "RAW", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'RAW'}}"}, "children": [{"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "", "propertyName": "rawWithSmartSubstitute", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "SMART_SUBSTITUTE", "conditionals": {"show": "{{actionConfiguration.formData.command === 'RAW' && actionConfiguration.formData.smartSubstitution === true}}"}}, {"label": "", "configProperty": "actionConfiguration.body", "propertyName": "rawWithTemplateSubstitute", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "TEMPLATE", "conditionals": {"show": "{{actionConfiguration.formData.command === 'RAW' && actionConfiguration.formData.smartSubstitution === false}}"}}]}]}]}], "5c9f512f96c1a50004819786": [{"sectionName": "", "id": 1, "children": [{"label": "", "internalLabel": "Query", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "PARAMETER", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": false}}, {"label": "", "internalLabel": "Query", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "TEMPLATE", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": true}}, {"label": "Use prepared statements", "tooltipText": "Prepared statements prevent SQL injections on your queries but do not support dynamic bindings outside values in your SQL", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].value", "controlType": "SWITCH", "initialValue": true}]}], "5ca385dc81b37f0004b4db85": [{"sectionName": "", "id": 1, "children": [{"label": "Path", "configProperty": "actionConfiguration.path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT"}, {"label": "Body", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "SMART_SUBSTITUTE", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": false}}, {"label": "Body", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": true}}, {"label": "Query parameters", "configProperty": "actionConfiguration.queryParameters", "controlType": "ARRAY_FIELD", "schema": [{"label": "Key", "key": "key", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "Key"}, {"label": "Value", "key": "value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "Value"}]}, {"label": "Headers", "configProperty": "actionConfiguration.headers", "controlType": "ARRAY_FIELD", "schema": [{"label": "Key", "key": "key", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "Key"}, {"label": "Value", "key": "value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "Value"}]}, {"label": "Form data", "configProperty": "actionConfiguration.bodyFormData", "controlType": "ARRAY_FIELD", "schema": [{"label": "Key", "key": "key", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "Key"}, {"label": "Value", "key": "value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "Value"}]}]}], "5fbbc39ad1f71d6666c32e4b": [{"sectionName": "", "id": 1, "children": [{"label": "Method key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "method"}, {"label": "Method", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].value", "controlType": "DROP_DOWN", "isRequired": true, "initialValue": "GET_DOCUMENT", "options": [{"label": "Get single document", "value": "GET_DOCUMENT"}, {"label": "Get documents in collection", "value": "GET_COLLECTION"}, {"label": "Set document", "value": "SET_DOCUMENT"}, {"label": "Create document", "value": "CREATE_DOCUMENT"}, {"label": "Add document to collection", "value": "ADD_TO_COLLECTION"}, {"label": "Update document", "value": "UPDATE_DOCUMENT"}, {"label": "Delete document", "value": "DELETE_DOCUMENT"}]}, {"label": "Collection/Document path", "configProperty": "actionConfiguration.path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "isRequired": true, "initialValue": ""}, {"label": "Timestamp value path key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[8].key", "controlType": "INPUT_TEXT", "initialValue": "timestampValuePath", "hidden": true}, {"label": "Timestamp value path (use dot(.) notation to reference nested key e.g. [\"key1.key2\"])", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[8].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": "", "placeholderText": "[\"checkinLog.timestampKey\", \"auditLog.timestampKey\"]", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "IN", "value": ["GET_DOCUMENT", "GET_COLLECTION", "DELETE_DOCUMENT"]}}, {"label": "Delete key value pair path key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[9].key", "controlType": "INPUT_TEXT", "initialValue": "deleteKeyValuePairPath", "hidden": true}, {"label": "Delete key value pair path (use dot(.) notation to reference nested key e.g. [\"key1.key2\"])", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[9].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": "", "placeholderText": "[\"userKey.nestedNamekey\", \"cityKey.nestedPincodeKey\"]", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "IN", "value": ["GET_DOCUMENT", "GET_COLLECTION", "DELETE_DOCUMENT", "CREATE_DOCUMENT", "ADD_TO_COLLECTION", "SET_DOCUMENT"]}}, {"label": "Order by key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[1].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "orderBy"}, {"label": "Order by (JSON array of field names to order by)", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[1].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET_COLLECTION"}, "placeholderText": "[\"ascendingField\", \"-descendingField\", \"nestedObj.field\"]", "initialValue": ""}, {"label": "Start after key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[6].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "limit"}, {"label": "Start after", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[6].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET_COLLECTION"}, "initialValue": ""}, {"label": "End before key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[7].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "limit"}, {"label": "End before", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[7].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET_COLLECTION"}, "initialValue": ""}, {"label": "Limit documents key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[2].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "limit"}, {"label": "Limit documents", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[2].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET_COLLECTION"}, "initialValue": "10"}, {"label": "Where conditions key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[3].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "whereConditionTuples"}, {"label": "Where conditions", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[3].value", "controlType": "ARRAY_FIELD", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET_COLLECTION"}, "schema": [{"label": "Path", "key": "path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "key1/nestedKey2"}, {"label": "Operator", "key": "operator", "controlType": "DROP_DOWN", "initialValue": "EQ", "options": [{"label": "<", "value": "LT"}, {"label": "<=", "value": "LTE"}, {"label": "==", "value": "EQ"}, {"label": ">=", "value": "GTE"}, {"label": ">", "value": "GT"}, {"label": "array-contains", "value": "ARRAY_CONTAINS"}, {"label": "in", "value": "IN"}, {"label": "array-contains-any", "value": "ARRAY_CONTAINS_ANY"}]}, {"label": "Value", "key": "value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "value"}]}, {"label": "Body", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "IN", "value": ["GET_DOCUMENT", "GET_COLLECTION", "DELETE_DOCUMENT"]}}]}], "6080f9266b8cfd602957ba72": [{"sectionName": "", "id": 1, "children": [{"label": "Method key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "method"}, {"label": "Method", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].value", "controlType": "DROP_DOWN", "isRequired": true, "initialValue": "GET", "options": [{"label": "Fetch sheet rows", "value": "GET"}, {"label": "Insert sheet row", "value": "APPEND"}, {"label": "Update sheet row", "value": "UPDATE"}, {"label": "Delete row", "value": "DELETE_ROW"}, {"label": "List sheets", "value": "LIST"}, {"label": "Fetch sheet", "value": "INFO"}, {"label": "Create new spreadsheet", "value": "CREATE"}, {"label": "Delete sheet", "value": "DELETE"}, {"label": "Bulk insert rows", "value": "BULK_APPEND"}, {"label": "Bulk update rows", "value": "BULK_UPDATE"}]}, {"label": "Spreadsheet URL key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[1].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "sheetUrl"}, {"label": "Spreadsheet URL", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[1].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "IN", "value": ["CREATE", "LIST"]}, "placeholderText": "https://docs.google.com/spreadsheets/d/xyz/edit#gid=0", "initialValue": ""}, {"label": "Spreadsheet name key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[3].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "spreadsheetName"}, {"label": "Spreadsheet name", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[3].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["CREATE"]}, "initialValue": ""}, {"label": "Delete format key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[12].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "deleteFormat"}, {"label": "Select entity", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[12].value", "controlType": "DROP_DOWN", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "DELETE"}, "initialValue": "SHEET", "options": [{"label": "Single sheet", "value": "SHEET"}, {"label": "Entire spreadsheet", "value": "SPREADSHEET"}]}, {"label": "Sheet name key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[7].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "sheetName"}, {"label": "Sheet name", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[7].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["GET", "UPDATE", "BULK_UPDATE", "DELETE_ROW", "DELETE", "APPEND", "BULK_APPEND"]}, {"conditionType": "AND", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": "DELETE"}, {"path": "actionConfiguration.pluginSpecifiedTemplates[12].value", "comparison": "EQUALS", "value": "SPREADSHEET"}]}]}, "initialValue": "Sheet1"}, {"label": "Table header index key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[4].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "tableHeaderIndex"}, {"label": "Table heading row index", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[4].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["GET", "UPDATE", "BULK_UPDATE", "DELETE_ROW", "APPEND", "BULK_APPEND"]}, "initialValue": "1"}, {"label": "Query format key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[5].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "queryFormat"}, {"label": "Query format", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[5].value", "controlType": "DROP_DOWN", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["GET"]}, "initialValue": "ROWS", "options": [{"label": "Query rows", "value": "ROWS"}, {"label": "Query range", "value": "RANGE"}]}, {"label": "Range key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[2].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "range"}, {"label": "Cell range", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[2].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET"}, {"conditionType": "AND", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": "GET"}, {"path": "actionConfiguration.pluginSpecifiedTemplates[5].value", "comparison": "EQUALS", "value": "ROWS"}]}]}, "initialValue": "", "placeholderText": "A2:B"}, {"label": "Row offset key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[8].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "rowOffset"}, {"label": "Row offset", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[8].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["GET"]}, {"conditionType": "AND", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": "GET"}, {"path": "actionConfiguration.pluginSpecifiedTemplates[5].value", "comparison": "EQUALS", "value": "RANGE"}]}]}, "initialValue": "0", "placeholderText": "0"}, {"label": "Row index key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[11].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "rowIndex"}, {"label": "Row Index", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[11].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["DELETE_ROW"]}, "initialValue": "0", "placeholderText": "0"}, {"label": "Row limit key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[6].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "rowLimit"}, {"label": "Row limit", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[6].value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET"}, {"conditionType": "AND", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "EQUALS", "value": "GET"}, {"path": "actionConfiguration.pluginSpecifiedTemplates[5].value", "comparison": "EQUALS", "value": "RANGE"}]}]}, "initialValue": "10", "placeholderText": "10"}, {"label": "Where key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[14].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "where"}, {"label": "Where conditions", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[14].value", "controlType": "ARRAY_FIELD", "hidden": {"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_EQUALS", "value": "GET"}, "initialValue": [], "schema": [{"label": "Column name", "key": "path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "name"}, {"label": "Operator", "key": "operator", "controlType": "DROP_DOWN", "initialValue": "EQ", "options": [{"label": "<", "value": "LT"}, {"label": "<=", "value": "LTE"}, {"label": "==", "value": "EQ"}, {"label": ">=", "value": "GTE"}, {"label": ">", "value": "GT"}, {"label": "in", "value": "IN"}, {"label": "not in", "value": "NOT_IN"}]}, {"label": "Value", "key": "value", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "placeholderText": "<PERSON>"}]}, {"label": "Row object key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[9].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "rowObject"}, {"label": "Row object", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[9].value", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "SMART_SUBSTITUTE", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["APPEND", "UPDATE"]}, {"path": "actionConfiguration.pluginSpecifiedTemplates[13].value", "comparison": "EQUALS", "value": false}]}, "placeholderText": "{{\n  {\n    ...Table1.selectedRow, \n    columnName: Input1.text\n  }\n}}"}, {"label": "Row object", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[9].value", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "SMART_SUBSTITUTE", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["APPEND", "UPDATE"]}, {"path": "actionConfiguration.pluginSpecifiedTemplates[13].value", "comparison": "EQUALS", "value": true}]}, "placeholderText": "{{\n  {\n    ...Table1.selectedRow, \n    columnName: Input1.text\n  }\n}}"}, {"label": "Row objects key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[10].key", "controlType": "INPUT_TEXT", "hidden": true, "initialValue": "rowObjects"}, {"label": "Row objects", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[10].value", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "SMART_SUBSTITUTE", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["CREATE", "BULK_APPEND", "BULK_UPDATE"]}, {"path": "actionConfiguration.pluginSpecifiedTemplates[13].value", "comparison": "EQUALS", "value": false}]}, "placeholderText": "{{\n  Table1.selectedRows.map((row) => {\n    return { ...row, columnName: Input1.text }\n  })\n}}"}, {"label": "Row objects", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[10].value", "controlType": "QUERY_DYNAMIC_TEXT", "evaluationSubstitutionType": "SMART_SUBSTITUTE", "hidden": {"conditionType": "OR", "conditions": [{"path": "actionConfiguration.pluginSpecifiedTemplates[0].value", "comparison": "NOT_IN", "value": ["CREATE", "BULK_APPEND", "BULK_UPDATE"]}, {"path": "actionConfiguration.pluginSpecifiedTemplates[13].value", "comparison": "EQUALS", "value": true}]}, "placeholderText": "{{\n  Table1.selectedRows.map((row) => {\n    return { ...row, columnName: Input1.text }\n  })\n}}"}, {"label": "Smart JSON substitution key", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[13].key", "hidden": true, "controlType": "INPUT_TEXT", "initialValue": "smartSubstitution"}]}], "6023b4a070eb652de19476d3": [{"options": [{"label": "List files in bucket", "value": "LIST"}, {"label": "Create a new file", "value": "UPLOAD_FILE_FROM_BODY"}, {"label": "Read file", "value": "READ_FILE"}, {"label": "Delete file", "value": "DELETE_FILE"}], "label": "Command", "description": "Choose method you would like to use", "configProperty": "actionConfiguration.formData.command", "controlType": "DROP_DOWN", "initialValue": "LIST"}, {"controlType": "SECTION", "label": "", "_comment": "This section holds all the templates", "children": [{"identifier": "LIST", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'LIST'}}"}, "children": [{"controlType": "SECTION", "label": "Select bucket to query", "children": [{"label": "Bucket name", "configProperty": "actionConfiguration.formData.bucket", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "isRequired": true, "initialValue": ""}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "Prefix", "configProperty": "actionConfiguration.formData.list.prefix", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": ""}, {"label": "Where", "configProperty": "actionConfiguration.formData.list.where", "nestedLevels": 3, "controlType": "WHERE_CLAUSE", "logicalTypes": [{"label": "AND", "value": "AND"}, {"label": "OR", "value": "OR"}], "comparisonTypes": [{"label": "==", "value": "EQ"}, {"label": "!=", "value": "NOT_EQ"}, {"label": "in", "value": "IN"}, {"label": "not in", "value": "NOT_IN"}]}]}, {"controlType": "SECTION", "label": "Options", "children": [{"label": "Generate signed URL", "configProperty": "actionConfiguration.formData.list.signedUrl", "controlType": "DROP_DOWN", "initialValue": "NO", "options": [{"label": "Yes", "value": "YES"}, {"label": "No", "value": "NO"}]}, {"label": "Expiry duration of signed URL (minutes)", "configProperty": "actionConfiguration.formData.list.expiry", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": "5", "conditionals": {"show": "{{actionConfiguration.formData.list.signedUrl === 'YES'}}"}}, {"label": "Generate unsigned URL", "configProperty": "actionConfiguration.formData.list.unSignedUrl", "controlType": "DROP_DOWN", "initialValue": "YES", "options": [{"label": "Yes", "value": "YES"}, {"label": "No", "value": "NO"}]}]}]}, {"identifier": "UPLOAD_FILE_FROM_BODY", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'UPLOAD_FILE_FROM_BODY'}}"}, "children": [{"controlType": "SECTION", "label": "Select bucket to query", "children": [{"label": "Bucket name", "configProperty": "actionConfiguration.formData.bucket", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "isRequired": true, "initialValue": ""}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "File path", "configProperty": "actionConfiguration.path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": ""}, {"label": "File data type", "configProperty": "actionConfiguration.formData.create.dataType", "controlType": "DROP_DOWN", "initialValue": "YES", "options": [{"label": "Base64", "value": "YES"}, {"label": "Text / Binary", "value": "NO"}]}, {"label": "Expiry duration of signed URL (minutes)", "configProperty": "actionConfiguration.formData.create.expiry", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": "5"}, {"label": "Content", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_TEXT", "initialValue": "", "placeHolderText": "{{ FilePicker1.files[0] }}"}]}]}, {"identifier": "READ_FILE", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'READ_FILE'}}"}, "children": [{"controlType": "SECTION", "label": "Select bucket to query", "children": [{"label": "Bucket name", "configProperty": "actionConfiguration.formData.bucket", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "isRequired": true, "initialValue": ""}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "File path", "configProperty": "actionConfiguration.path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": ""}, {"label": "File data type", "configProperty": "actionConfiguration.formData.read.dataType", "controlType": "DROP_DOWN", "initialValue": "YES", "options": [{"label": "Base64", "value": "YES"}, {"label": "Text / Binary", "value": "NO"}]}, {"label": "Expiry duration of signed URL (minutes)", "configProperty": "actionConfiguration.formData.read.expiry", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": "5"}, {"label": "Base64 encode file - yes/no", "configProperty": "actionConfiguration.formData.read.usingBase64Encoding", "controlType": "DROP_DOWN", "initialValue": "YES", "options": [{"label": "Yes", "value": "YES"}, {"label": "No", "value": "NO"}]}]}]}, {"identifier": "DELETE_FILE", "controlType": "SECTION", "conditionals": {"show": "{{actionConfiguration.formData.command === 'DELETE_FILE'}}"}, "children": [{"controlType": "SECTION", "label": "Select bucket to query", "children": [{"label": "Bucket name", "configProperty": "actionConfiguration.formData.bucket", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "evaluationSubstitutionType": "TEMPLATE", "isRequired": true, "initialValue": ""}]}, {"controlType": "SECTION", "label": "Query", "description": "Optional", "children": [{"label": "File path", "configProperty": "actionConfiguration.path", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": ""}, {"label": "Expiry duration of signed URL (minutes)", "configProperty": "actionConfiguration.formData.delete.expiry", "controlType": "QUERY_DYNAMIC_INPUT_TEXT", "initialValue": "5"}]}]}]}], "5f9169920c6d936f469f4c8a": [{"sectionName": "", "id": 1, "children": [{"label": "", "internalLabel": "Query", "configProperty": "actionConfiguration.body", "controlType": "QUERY_DYNAMIC_TEXT"}]}], "6138c786168857325f78ef3e": []}, "settingConfigs": {"5e687c18fb01e64e6a3f873f": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this query", "configProperty": "confirmBeforeExecute", "controlType": "SWITCH", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Smart BSON substitution", "tooltipText": "Turning on this property fixes the BSON substitution of bindings in the Mongo BSON document by adding/removing quotes intelligently and reduces developer errors", "configProperty": "actionConfiguration.formData.smartSubstitution", "controlType": "SWITCH", "initialValue": true}, {"label": "Query timeout (in milliseconds)", "info": "Maximum time after which the query will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "INPUT_TEXT", "dataType": "NUMBER"}]}], "5c9f512f96c1a50004819786": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this query", "configProperty": "confirmBeforeExecute", "controlType": "SWITCH", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Use prepared statements", "tooltipText": "Prepared statements prevent SQL injections on your queries but do not support dynamic bindings outside values in your SQL", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].value", "controlType": "SWITCH", "initialValue": true}, {"label": "Query timeout (in milliseconds)", "info": "Maximum time after which the query will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "INPUT_TEXT", "dataType": "NUMBER"}]}], "5ca385dc81b37f0004b4db85": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this API", "configProperty": "confirmBeforeExecute", "controlType": "CHECKBOX", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Encode query params", "configProperty": "actionConfiguration.encodeParamsToggle", "controlType": "CHECKBOX", "tooltipText": "Encode query params for all APIs. Also encode form body when Content-Type header is set to x-www-form-encoded"}, {"label": "Smart JSON substitution", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[0].value", "controlType": "CHECKBOX", "tooltipText": "Turning on this property fixes the JSON substitution of bindings in API body by adding/removing quotes intelligently and reduces developer errors", "initialValue": true}, {"label": "API timeout (in milliseconds)", "info": "Maximum time after which the API will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "NUMBER_INPUT", "dataType": "number"}]}], "5fbbc39ad1f71d6666c32e4b": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this query", "configProperty": "confirmBeforeExecute", "controlType": "SWITCH", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Query timeout (in milliseconds)", "info": "Maximum time after which the query will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "INPUT_TEXT", "dataType": "NUMBER"}]}], "6080f9266b8cfd602957ba72": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this query", "configProperty": "confirmBeforeExecute", "controlType": "SWITCH", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Smart JSON substitution", "tooltipText": "Turning on this property fixes the JSON substitution of bindings in the Row objects by adding/removing quotes intelligently and reduces developer errors", "configProperty": "actionConfiguration.pluginSpecifiedTemplates[13].value", "controlType": "SWITCH", "initialValue": true}, {"label": "Query timeout (in milliseconds)", "info": "Maximum time after which the query will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "INPUT_TEXT", "dataType": "NUMBER"}]}], "6023b4a070eb652de19476d3": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this query", "configProperty": "confirmBeforeExecute", "controlType": "SWITCH", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Smart JSON substitution", "tooltipText": "Turning on this property fixes the JSON substitution of bindings in the Content field by adding/removing quotes intelligently and reduces developer errors", "configProperty": "actionConfiguration.formData.smartSubstitution", "controlType": "SWITCH", "initialValue": true}, {"label": "Query timeout (in milliseconds)", "info": "Maximum time after which the query will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "INPUT_TEXT", "dataType": "NUMBER"}]}], "5f9169920c6d936f469f4c8a": [{"sectionName": "", "id": 1, "children": [{"label": "Run behavior", "configProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controlType": "DROP_DOWN", "initialValue": "MANUAL", "options": [{"label": "Automatic", "subText": "Query runs on page load or when a variable it depends on changes", "value": "AUTOMATIC"}, {"label": "On page load", "subText": "Query runs when the page loads or when manually triggered", "value": "ON_PAGE_LOAD"}, {"label": "Manual", "subText": "Query only runs when called in an event or JS with .run()", "value": "MANUAL"}]}, {"label": "Request confirmation before running this query", "configProperty": "confirmBeforeExecute", "controlType": "SWITCH", "tooltipText": "Ask confirmation from the user each time before refreshing data"}, {"label": "Query timeout (in milliseconds)", "info": "Maximum time after which the query will return", "configProperty": "actionConfiguration.timeoutInMillisecond", "controlType": "INPUT_TEXT", "dataType": "NUMBER"}]}], "6138c786168857325f78ef3e": []}, "dependencies": "undefined", "fetchingSinglePluginForm": {}}, "meta": {}, "app": "undefined", "jsActions": []}}