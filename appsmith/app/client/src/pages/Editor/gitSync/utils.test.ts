import {
  changeInfoSinceLastCommit,
  getIsStartingWithRemoteBranches,
  isLocalBranch,
  isRemoteBranch,
  isValidGitRemoteUrl,
  removeSpecialChars,
} from "./utils";
import { ApplicationVersion } from "ee/actions/applicationActions";

const validUrls = [
  "**************:user/project.git",
  "git://a@b:c/d.git",
  "git@***************:user/project.git",
  "ssh://<EMAIL>:port/path/to/repo.git",
  "ssh://<EMAIL>/path/to/repo.git",
  "ssh://host.xz:port/path/to/repo.git",
  "ssh://host.xz/path/to/repo.git",
  "ssh://<EMAIL>/path/to/repo.git",
  "ssh://host.xz/path/to/repo.git",
  "ssh://<EMAIL>/~user/path/to/repo.git",
  "ssh://host.xz/~user/path/to/repo.git",
  "ssh://<EMAIL>/~/path/to/repo.git",
  "ssh://host.xz/~/path/to/repo.git",
  "*****************.com:v3/something/other/thing",
  "*****************.com:v3/something/other/thing.git",
  "*****************.com:v3/something/other/(thing).git",
  "*****************.com:v3/(((something)/(other)/(thing).git",
  "************:org__v3/(((something)/(other)/(thing).git",
  "************************:org__org/repoName.git",
  "git@gitlab__abcd.test.org:org__org/repoName.git",
  "*****************.com:v3/something/with%20space%20(some)/geo-mantis",
  "*****************.com:v3/something/with%20space%20some/geo-mantis",
  "<EMAIL>:path/to/repo.git",
  "<EMAIL>:org_name/repository_name.git",
];

const invalidUrls = [
  "*****************.(com):v3/(((something)/(other)/(thing).git",
  "*****************.com:v3/something/other/thing/",
  "gitclonegit://a@b:c/d.git",
  "https://github.com/user/project.git",
  "http://github.com/user/project.git",
  "https://***************/user/project.git",
  "http://***************/user/project.git",
  "ssh://<EMAIL>:port/path/to/repo.git/",
  "ssh://<EMAIL>/path/to/repo.git/",
  "ssh://host.xz:port/path/to/repo.git/",
  "ssh://host.xz/path/to/repo.git/",
  "ssh://<EMAIL>/path/to/repo.git/",
  "ssh://host.xz/path/to/repo.git/",
  "ssh://<EMAIL>/~user/path/to/repo.git/",
  "ssh://host.xz/~user/path/to/repo.git/",
  "git://host.xz/path/to/repo.git/",
  "git://host.xz/~user/path/to/repo.git/",
  "http://host.xz/path/to/repo.git/",
  "https://host.xz/path/to/repo.git/",
  "/path/to/repo.git/",
  "path/to/repo.git/",
  "~/path/to/repo.git",
  "file:///path/to/repo.git/",
  "file://~/path/to/repo.git/",
  "<EMAIL>:/path/to/repo.git/",
  "host.xz:/path/to/repo.git/",
  "<EMAIL>:~user/path/to/repo.git/",
  "host.xz:~user/path/to/repo.git/",
  "host.xz:path/to/repo.git",
  "rsync://host.xz/path/to/repo.git/",
];

describe("gitSync utils", () => {
  describe("getIsStartingWithRemoteBranches", function () {
    it("returns true when only remote starts with origin/", () => {
      const actual = getIsStartingWithRemoteBranches(
        "whatever",
        "origin/whateverelse",
      );
      const expected = true;

      expect(actual).toEqual(expected);
    });

    it("returns false if param:local starts with origin/", () => {
      const actual = getIsStartingWithRemoteBranches(
        "origin/a",
        "origin/whateverelse",
      );
      const expected = false;

      expect(actual).toEqual(expected);
    });

    it("returns empty string if param:local is empty string", () => {
      const actual = getIsStartingWithRemoteBranches("a", "");
      const expected = "";

      expect(actual).toEqual(expected);
    });

    it("returns empty string if param:remote is empty string", () => {
      const actual = getIsStartingWithRemoteBranches("", "");
      const expected = "";

      expect(actual).toEqual(expected);
    });
  });

  describe("isValidGitRemoteUrl returns true for valid urls", () => {
    validUrls.forEach((validUrl: string) => {
      it(`${validUrl} is a valid git remote URL`, () => {
        const actual = isValidGitRemoteUrl(validUrl);
        const expected = true;

        expect(actual).toEqual(expected);
      });
    });
  });

  describe("isValidGitRemoteUrl returns false for invalid urls", () => {
    invalidUrls.forEach((invalidUrl: string) => {
      it(`${invalidUrl} is a valid git remote URL`, () => {
        const actual = isValidGitRemoteUrl(invalidUrl);
        const expected = false;

        expect(actual).toEqual(expected);
      });
    });
  });

  describe("isRemoteBranch", () => {
    it("returns true for branches that start with origin/", () => {
      const branches = ["origin/", "origin/_", "origin/a", "origin/origin"];
      const actual = branches.every(isRemoteBranch);
      const expected = true;

      expect(actual).toEqual(expected);
    });

    it("returns false for branches that don't start with origin/", () => {
      const branches = [
        "origin",
        "original/",
        "oriign/_",
        "main/",
        "upstream/origin",
        "develop/",
        "release/",
        "master/",
      ];
      const actual = branches.every(isRemoteBranch);
      const expected = false;

      expect(actual).toEqual(expected);
    });
  });

  describe("isLocalBranch", () => {
    it("returns false for branches that start with origin/", () => {
      const branches = ["origin/", "origin/_", "origin/a", "origin/origin"];
      const actual = branches.every(isLocalBranch);
      const expected = false;

      expect(actual).toEqual(expected);
    });

    it("returns true for branches that don't start with origin/", () => {
      const branches = [
        "origin",
        "original/",
        "oriign/_",
        "main/",
        "upstream/origin",
        "develop/",
        "release/",
        "master/",
      ];
      const actual = branches.every(isLocalBranch);
      const expected = true;

      expect(actual).toEqual(expected);
    });
  });

  describe("removeSpecialCharacters", () => {
    it("replaces special characters except / and - with _", () => {
      const inputs = [
        "abc_def",
        "abc-def",
        "abc*def",
        "abc/def",
        "abc&def",
        "abc%def",
        "abc#def",
        "abc@def",
        "abc!def",
        "abc,def",
        "abc<def",
        "abc>def",
        "abc?def",
        "abc.def",
        "abc;def",
        "abc(def",
      ];

      const expected = [
        "abc_def",
        "abc-def",
        "abc_def",
        "abc/def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
        "abc_def",
      ];

      inputs.forEach((input, index) => {
        const result = removeSpecialChars(input);

        expect(result).toStrictEqual(expected[index]);
      });
    });
  });
  describe("changeInfoSinceLastCommit", () => {
    it("returns default data", () => {
      const applicationData = {
        appIsExample: false,
        applicationVersion: ApplicationVersion.DEFAULT,
        defaultPageId: "",
        defaultBasePageId: "",
        slug: "",
        id: "",
        baseId: "",
        isAutoUpdate: false,
        isManualUpdate: false,
        name: "",
        workspaceId: "",
        pages: [],
      };
      const actual = changeInfoSinceLastCommit(applicationData);
      const expected = {
        changeReasonText: "Changes since last deployment",
        isAutoUpdate: false,
        isManualUpdate: false,
      };

      expect(actual).toEqual(expected);
    });
    it("returns migration change only data", () => {
      const applicationData = {
        appIsExample: false,
        applicationVersion: ApplicationVersion.DEFAULT,
        defaultPageId: "",
        defaultBasePageId: "",
        id: "",
        baseId: "",
        slug: "",
        isAutoUpdate: true,
        isManualUpdate: false,
        name: "",
        workspaceId: "",
        pages: [],
      };
      const actual = changeInfoSinceLastCommit(applicationData);
      const expected = {
        changeReasonText: "Changes since last deployment",
        isAutoUpdate: true,
        isManualUpdate: false,
      };

      expect(actual).toEqual(expected);
    });
    it("returns migration and user change data", () => {
      const applicationData = {
        appIsExample: false,
        applicationVersion: ApplicationVersion.DEFAULT,
        defaultPageId: "",
        defaultBasePageId: "",
        id: "",
        baseId: "",
        slug: "",
        isAutoUpdate: true,
        isManualUpdate: true,
        name: "",
        workspaceId: "",
        pages: [],
      };
      const actual = changeInfoSinceLastCommit(applicationData);
      const expected = {
        changeReasonText: "Changes since last deployment",
        isAutoUpdate: true,
        isManualUpdate: true,
      };

      expect(actual).toEqual(expected);
    });
    it("returns user changes only data", () => {
      const applicationData = {
        appIsExample: false,
        applicationVersion: ApplicationVersion.DEFAULT,
        defaultPageId: "",
        defaultBasePageId: "",
        id: "",
        baseId: "",
        slug: "",
        isAutoUpdate: false,
        isManualUpdate: true,
        name: "",
        workspaceId: "",
        pages: [],
      };
      const actual = changeInfoSinceLastCommit(applicationData);
      const expected = {
        changeReasonText: "Changes since last deployment",
        isAutoUpdate: false,
        isManualUpdate: true,
      };

      expect(actual).toEqual(expected);
    });
  });
});
