import type { Template } from "api/TemplatesApi";

// Unit Tests
export const MOCK_BUILDING_BLOCK_TITLE = "Test Building Block";
export const MOCK_BUILDING_BLOCK_DESCRIPTION =
  "Description of the test building block";
export const MOCK_BUILDING_BLOCK_ID = "mockId";

export const unitTestMockBuildingBlock: Template = {
  id: MOCK_BUILDING_BLOCK_ID,
  userPermissions: ["read", "write"],
  title: MOCK_BUILDING_BLOCK_TITLE,
  description: MOCK_BUILDING_BLOCK_DESCRIPTION,
  appUrl: "https://mockapp.com",
  gifUrl: "https://mockapp.com/mock.gif",
  screenshotUrls: [
    "https://mockapp.com/screenshot1.jpg",
    "https://mockapp.com/screenshot2.jpg",
  ],
  widgets: [],
  functions: ["Function1", "Function2"],
  useCases: ["UseCase1", "UseCase2"],
  datasources: ["Datasource1", "Datasource2"],
  pages: [],
  allowPageImport: true,
};

export const unitTestMockTemplate = {
  id: "6222224900c64549b31b9467",
  userPermissions: [],
  title: "Fund Raising CRM",
  description:
    "This Fundraising CRM, allows for secure and direct communication between a company, and their investors, allowing users to maintain track of their communications.",
  appUrl:
    "https://app.appsmith.com/applications/61dbc9d66bd5757f166cc898/pages/6204a671552a5f63958772aa/b?embed=true",
  appDataUrl:
    "https://s3.us-east-2.amazonaws.com/template.appsmith.com/FundRaisingCRM_Enabled.json",
  gifUrl: "",
  sortPriority: "1001",
  screenshotUrls: [
    "https://assets.appsmith.com/templates/screenshots/FundRaisingCRM.png",
  ],
  widgets: [
    "BUTTON_WIDGET",
    "CONTAINER_WIDGET",
    "FILE_PICKER_WIDGET_V2",
    "FORM_WIDGET",
    "ICON_BUTTON_WIDGET",
    "INPUT_WIDGET_V2",
    "LIST_WIDGET_V2",
    "MAP_WIDGET",
    "MODAL_WIDGET",
    "RATE_WIDGET",
    "RICH_TEXT_EDITOR_WIDGET",
    "TEXT_WIDGET",
  ],
  functions: ["Operations", "Communications", "All"],
  useCases: ["Finance", "Information Technology (IT)"],
  datasources: ["amazons3-plugin", "google-sheets-plugin"],
  pages: [
    {
      id: "6204a671552a5f63958772aa",
      baseId: "6204a671552a5f63958772aa",
      name: "Investors",
      slug: "investors",
      isDefault: true,
      isHidden: false,
    },
  ],
  minVersion: "v1.6.11-SNAPSHOT",
  minVersionPadded: "000010000600011",
  downloadCount: 0,
  active: true,
  allowPageImport: true,
  isCommunityTemplate: false,
  new: false,
};

export const unitTestMockTemplateAllFilters = {
  userPermissions: [],
  sortPriority: "1000",
  widgets: [
    "Modal",
    "CHECKBOX_WIDGET",
    "Table",
    "CONTAINER_WIDGET",
    "FORM_WIDGET",
    "MULTI_SELECT_WIDGET_V2",
    "LIST_WIDGET",
    "TABLE_WIDGET_V2",
    "INPUT_WIDGET_V2",
    "MENU_BUTTON_WIDGET",
    "RATE_WIDGET",
    "CHART_WIDGET",
    "Tabs",
    "BUTTON_WIDGET",
    "Button",
    "MODAL_WIDGET",
    "Select",
    "SWITCH_WIDGET",
    "DOCUMENT_VIEWER_WIDGET",
    "RICH_TEXT_EDITOR_WIDGET",
    "JSON_FORM_WIDGET",
    "Image",
    "TABLE_WIDGET",
    "IMAGE_WIDGET",
    "SELECT_WIDGET",
    "CURRENCY_INPUT_WIDGET",
    "TABS_WIDGET",
    "PHONE_INPUT_WIDGET",
    "DATE_PICKER_WIDGET2",
    "TEXT_WIDGET",
    "List",
    "LIST_WIDGET_V2",
    "STATBOX_WIDGET",
    "MAP_WIDGET",
    "Container",
    "BUTTON_GROUP_WIDGET",
    "Form",
    "Icon Button",
    "Text",
    "ICON_BUTTON_WIDGET",
    "Input",
  ],
  functions: [
    "Start-up",
    "Operations",
    "Customer Support",
    "Information Technology (IT)",
    "Other",
    "Building Blocks",
    "Services",
    "Technology",
    "Human Resources",
    "Building Block",
    "Communications",
    "Customer management",
    "Sales",
    "Consumer goods",
    "All",
    "E-Commerce",
    "Marketing",
  ],
  useCases: [
    "Productivity",
    "Public Relations (PR)",
    "Remote work",
    "Human Resources",
    "Human Resources (HR)",
    "Communications",
    "Admin",
    "Software Development",
    "Project Management",
    "Remote Work",
    "Marketing",
    "Personal",
    "Support",
    "Finance",
    "Sales",
  ],
  datasources: [
    "twilio",
    "61531b1b9370f312a0394f16",
    "Google Sheets",
    "smtp-plugin",
    "mongo-plugin",
    "postgres-plugin",
    "restapi-plugin",
  ],
  new: true,
};
