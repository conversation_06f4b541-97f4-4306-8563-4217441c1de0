import {
  FlexVerticalAlignment,
  ResponsiveBehavior,
} from "layoutSystems/common/utils/constants";
import type { CopiedWidgetGroup } from "sagas/WidgetOperationUtils";

export const leftMostWidget = {
  needsErrorInfo: false,
  boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
  mobileBottomRow: 116,
  widgetName: "con_main",
  borderColor: "#E0DEDE",
  isCanvas: true,
  displayName: "Container",
  iconSVG:
    "https://appcdn.appsmith.com/static/media/icon.dcc052e58db911c4c8955c200f4bcf5c.svg",
  searchTags: ["div", "parent", "group"],
  topRow: 0,
  bottomRow: 64,
  parentRowSpace: 10,
  type: "CONTAINER_WIDGET",
  hideCard: false,
  shouldScrollContents: true,
  mobileRightColumn: 37,
  animateLoading: true,
  parentColumnSpace: 28.734375,
  leftColumn: 15,
  dynamicBindingPathList: [
    {
      key: "borderRadius",
    },
    {
      key: "boxShadow",
    },
  ],
  children: ["za7wfwo8ko"],
  borderWidth: "1",
  flexVerticalAlignment: "stretch",
  key: "lpm5j92mpg",
  backgroundColor: "#FFFFFF",
  isDeprecated: false,
  rightColumn: 46,
  thumbnailSVG:
    "https://appcdn.appsmith.com/static/media/thumbnail.6cb355b93146b5347bbb048a568ed446.svg",
  dynamicHeight: "AUTO_HEIGHT",
  widgetId: "ppci5prygm",
  containerStyle: "card",
  onCanvasUI: {
    selectionBGCSSVar: "--on-canvas-ui-widget-selection",
    focusBGCSSVar: "--on-canvas-ui-widget-focus",
    selectionColorCSSVar: "--on-canvas-ui-widget-focus",
    focusColorCSSVar: "--on-canvas-ui-widget-selection",
    disableParentSelection: false,
  },
  minWidth: 450,
  isVisible: true,
  version: 1,
  parentId: "0",
  tags: ["Layout"],
  renderMode: "CANVAS",
  isLoading: false,
  mobileTopRow: 106,
  responsiveBehavior: "fill",
  originalTopRow: 0,
  borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
  mobileLeftColumn: 13,
  maxDynamicHeight: 9000,
  originalBottomRow: 64,
  minDynamicHeight: 10,
};
export const topMostWidget = {
  needsErrorInfo: false,
  boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
  mobileBottomRow: 116,
  widgetName: "con_main",
  borderColor: "#E0DEDE",
  isCanvas: true,
  displayName: "Container",
  iconSVG:
    "https://appcdn.appsmith.com/static/media/icon.dcc052e58db911c4c8955c200f4bcf5c.svg",
  searchTags: ["div", "parent", "group"],
  topRow: 0,
  bottomRow: 64,
  parentRowSpace: 10,
  type: "CONTAINER_WIDGET",
  hideCard: false,
  shouldScrollContents: true,
  mobileRightColumn: 37,
  animateLoading: true,
  parentColumnSpace: 28.734375,
  leftColumn: 15,
  dynamicBindingPathList: [
    {
      key: "borderRadius",
    },
    {
      key: "boxShadow",
    },
  ],
  children: ["za7wfwo8ko"],
  borderWidth: "1",
  flexVerticalAlignment: "stretch",
  key: "lpm5j92mpg",
  backgroundColor: "#FFFFFF",
  isDeprecated: false,
  rightColumn: 46,
  thumbnailSVG:
    "https://appcdn.appsmith.com/static/media/thumbnail.6cb355b93146b5347bbb048a568ed446.svg",
  dynamicHeight: "AUTO_HEIGHT",
  widgetId: "ppci5prygm",
  containerStyle: "card",
  onCanvasUI: {
    selectionBGCSSVar: "--on-canvas-ui-widget-selection",
    focusBGCSSVar: "--on-canvas-ui-widget-focus",
    selectionColorCSSVar: "--on-canvas-ui-widget-focus",
    focusColorCSSVar: "--on-canvas-ui-widget-selection",
    disableParentSelection: false,
  },
  minWidth: 450,
  isVisible: true,
  version: 1,
  parentId: "0",
  tags: ["Layout"],
  renderMode: "CANVAS",
  isLoading: false,
  mobileTopRow: 106,
  responsiveBehavior: "fill",
  originalTopRow: 0,
  borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
  mobileLeftColumn: 13,
  maxDynamicHeight: 9000,
  originalBottomRow: 64,
  minDynamicHeight: 10,
};
export const copiedWidgets: CopiedWidgetGroup[] = [
  {
    list: [
      {
        needsErrorInfo: false,
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        mobileBottomRow: 116,
        widgetName: "con_main",
        borderColor: "#E0DEDE",
        isCanvas: true,
        displayName: "Container",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.dcc052e58db911c4c8955c200f4bcf5c.svg",
        searchTags: ["div", "parent", "group"],
        topRow: 0,
        bottomRow: 64,
        parentRowSpace: 10,
        type: "CONTAINER_WIDGET",
        hideCard: false,
        shouldScrollContents: true,
        mobileRightColumn: 37,
        animateLoading: true,
        parentColumnSpace: 28.734375,
        leftColumn: 15,
        dynamicBindingPathList: [
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
        ],
        children: ["za7wfwo8ko"],
        borderWidth: "1",
        flexVerticalAlignment: FlexVerticalAlignment.Stretch,
        key: "lpm5j92mpg",
        backgroundColor: "#FFFFFF",
        isDeprecated: false,
        rightColumn: 46,
        thumbnailSVG:
          "https://appcdn.appsmith.com/static/media/thumbnail.6cb355b93146b5347bbb048a568ed446.svg",
        dynamicHeight: "AUTO_HEIGHT",
        widgetId: "ppci5prygm",
        containerStyle: "card",
        onCanvasUI: {
          selectionBGCSSVar: "--on-canvas-ui-widget-selection",
          focusBGCSSVar: "--on-canvas-ui-widget-focus",
          selectionColorCSSVar: "--on-canvas-ui-widget-focus",
          focusColorCSSVar: "--on-canvas-ui-widget-selection",
          disableParentSelection: false,
        },
        minWidth: 450,
        isVisible: true,
        version: 1,
        parentId: "0",
        tags: ["Layout"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 106,
        responsiveBehavior: ResponsiveBehavior.Fill,
        originalTopRow: 0,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 13,
        maxDynamicHeight: 9000,
        originalBottomRow: 64,
        minDynamicHeight: 10,
      },
      {
        needsErrorInfo: false,
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        mobileBottomRow: 100,
        widgetName: "Canvas5",
        displayName: "Canvas",
        topRow: 0,
        bottomRow: 640,
        parentRowSpace: 1,
        type: "CANVAS_WIDGET",
        canExtend: false,
        hideCard: true,
        minHeight: 100,
        mobileRightColumn: 689.625,
        parentColumnSpace: 1,
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
        ],
        children: [
          "wrggux789b",
          "k26uf25ngy",
          "mks70rbc2g",
          "dfcdc9r5im",
          "9ceqs732u7",
        ],
        key: "602xo8i3fp",
        isDeprecated: false,
        rightColumn: 689.625,
        detachFromLayout: true,
        dynamicHeight: "AUTO_HEIGHT",
        widgetId: "za7wfwo8ko",
        onCanvasUI: {
          selectionBGCSSVar: "--on-canvas-ui-widget-selection",
          focusBGCSSVar: "--on-canvas-ui-widget-focus",
          selectionColorCSSVar: "--on-canvas-ui-widget-focus",
          focusColorCSSVar: "--on-canvas-ui-widget-selection",
          disableParentSelection: true,
        },
        containerStyle: "none",
        minWidth: 450,
        isVisible: true,
        version: 1,
        parentId: "ppci5prygm",
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 0,
        maxDynamicHeight: 9000,
        minDynamicHeight: 4,
        flexLayers: [],
      },
      {
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        requiresFlatWidgetChildren: true,
        isCanvas: true,
        templateHeight: 160,
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.5c9511142b3624c7491c5442e8ccd0ef.svg",
        topRow: 12,
        pageSize: 4,
        type: "LIST_WIDGET_V2",
        itemSpacing: 8,
        animateLoading: true,
        dynamicBindingPathList: [
          {
            key: "currentItemsView",
          },
          {
            key: "selectedItemView",
          },
          {
            key: "triggeredItemView",
          },
          {
            key: "primaryKeys",
          },
          {
            key: "accentColor",
          },
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
          {
            key: "listData",
          },
        ],
        leftColumn: 0,
        enhancements: true,
        children: ["srju46rihz"],
        flexVerticalAlignment: FlexVerticalAlignment.Stretch,
        itemBackgroundColor: "#FFFFFF",
        accentColor: "{{appsmith.theme.colors.primaryColor}}",
        isVisible: true,
        tags: ["Suggested", "Display"],
        hasMetaWidgets: true,
        isLoading: false,
        mainCanvasId: "srju46rihz",
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        originalBottomRow: 100,
        additionalStaticProps: [
          "level",
          "levelData",
          "prefixMetaWidgetId",
          "metaWidgetId",
        ],
        mobileBottomRow: 60,
        currentItemsView: "{{[]}}",
        triggeredItemView: "{{{}}}",
        widgetName: "lst_supportQueries",
        listData: "{{functions.getFilteredTickets.data}}",
        displayName: "List",
        bottomRow: 62,
        parentRowSpace: 10,
        hideCard: false,
        mobileRightColumn: 25,
        mainContainerId: "dlbfscdcsw",
        primaryKeys:
          '{{lst_supportQueries.listData.map((currentItem, currentIndex) => currentItem["id"] )}}',
        parentColumnSpace: 13.24609375,
        dynamicTriggerPathList: [],
        gridType: "vertical",
        key: "qwjyje9c94",
        backgroundColor: "transparent",
        isDeprecated: false,
        rightColumn: 64,
        widgetId: "9ceqs732u7",
        minWidth: 450,
        parentId: "za7wfwo8ko",
        renderMode: "CANVAS",
        mobileTopRow: 20,
        responsiveBehavior: ResponsiveBehavior.Fill,
        originalTopRow: 12,
        mobileLeftColumn: 1,
        selectedItemView: "{{{}}}",
        version: 1,
      },
      {
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        mobileBottomRow: 400,
        widgetName: "Canvas2",
        displayName: "Canvas",
        topRow: 0,
        bottomRow: 500,
        parentRowSpace: 1,
        type: "CANVAS_WIDGET",
        canExtend: false,
        hideCard: true,
        dropDisabled: true,
        openParentPropertyPane: true,
        minHeight: 400,
        mobileRightColumn: 317.90625,
        noPad: true,
        parentColumnSpace: 1,
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
        ],
        children: ["dlbfscdcsw"],
        key: "cssw4tpdxw",
        isDeprecated: false,
        rightColumn: 317.90625,
        detachFromLayout: true,
        dynamicHeight: "AUTO_HEIGHT",
        widgetId: "srju46rihz",
        containerStyle: "none",
        minWidth: 450,
        isVisible: true,
        version: 1,
        parentId: "9ceqs732u7",
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 0,
        maxDynamicHeight: 9000,
        minDynamicHeight: 4,
        flexLayers: [],
      },
      {
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        mobileBottomRow: 12,
        widgetName: "con_supportTicket",
        borderColor: "#E0DEDE",
        disallowCopy: true,
        isCanvas: true,
        displayName: "Container",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.daebf68875b6c8e909e9e8ac8bee0c02.svg",
        searchTags: ["div", "parent", "group"],
        topRow: 0,
        bottomRow: 10,
        dragDisabled: true,
        type: "CONTAINER_WIDGET",
        hideCard: false,
        shouldScrollContents: false,
        isDeletable: false,
        mobileRightColumn: 64,
        animateLoading: true,
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
        ],
        children: ["fy9k9pu256"],
        borderWidth: "1",
        positioning: "fixed",
        flexVerticalAlignment: FlexVerticalAlignment.Stretch,
        key: "26ujlbvp2s",
        backgroundColor: "white",
        isDeprecated: false,
        rightColumn: 64,
        dynamicHeight: "FIXED",
        widgetId: "dlbfscdcsw",
        containerStyle: "card",
        minWidth: 450,
        isVisible: true,
        version: 1,
        isListItemContainer: true,
        parentId: "srju46rihz",
        tags: ["Layout"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        noContainerOffset: true,
        disabledWidgetFeatures: ["dynamicHeight"],
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 0,
        maxDynamicHeight: 9000,
        minDynamicHeight: 10,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        widgetName: "Canvas3",
        displayName: "Canvas",
        topRow: 0,
        bottomRow: 100,
        parentRowSpace: 1,
        type: "CANVAS_WIDGET",
        canExtend: false,
        hideCard: true,
        useAutoLayout: false,
        parentColumnSpace: 1,
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
        ],
        children: [
          "oeu7013kx2",
          "zhwuvr8gny",
          "eeg88q6qo0",
          "u26bhtsugr",
          "cdo8lfmyq2",
          "8x2cgauju9",
          "qqyhz7kz6a",
          "4fpv7yqjnd",
        ],
        key: "cssw4tpdxw",
        isDeprecated: false,
        detachFromLayout: true,
        dynamicHeight: "AUTO_HEIGHT",
        widgetId: "fy9k9pu256",
        containerStyle: "none",
        minWidth: 450,
        isVisible: true,
        version: 1,
        parentId: "dlbfscdcsw",
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 0,
        maxDynamicHeight: 9000,
        minDynamicHeight: 4,
        flexLayers: [],
        rightColumn: 100,
      },
      {
        mobileBottomRow: 8,
        widgetName: "txt_status",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 2,
        bottomRow: 6,
        parentRowSpace: 10,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 39,
        animateLoading: true,
        overflow: "NONE",
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        parentColumnSpace: 12.3946533203125,
        dynamicTriggerPathList: [],
        leftColumn: 53,
        dynamicBindingPathList: [
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
          {
            key: "text",
          },
        ],
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "{{currentItem.status}}",
        key: "912s07712t",
        isDeprecated: false,
        rightColumn: 62,
        textAlign: "CENTER",
        dynamicHeight: "FIXED",
        widgetId: "4fpv7yqjnd",
        minWidth: 450,
        isVisible: true,
        fontStyle: "BOLD",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 4,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 23,
        maxDynamicHeight: 9000,
        fontSize: "1rem",
        minDynamicHeight: 4,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 10,
        widgetName: "con_ticketPriority",
        borderColor: "#e0dede",
        isCanvas: true,
        dynamicPropertyPathList: [
          {
            key: "backgroundColor",
          },
        ],
        displayName: "Container",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.daebf68875b6c8e909e9e8ac8bee0c02.svg",
        searchTags: ["div", "parent", "group"],
        topRow: 0,
        bottomRow: 8,
        parentRowSpace: 10,
        type: "CONTAINER_WIDGET",
        hideCard: false,
        shouldScrollContents: false,
        mobileRightColumn: 64,
        animateLoading: true,
        parentColumnSpace: 12.3946533203125,
        dynamicTriggerPathList: [],
        leftColumn: 62,
        dynamicBindingPathList: [
          {
            key: "backgroundColor",
          },
        ],
        children: ["zseauuyzdw"],
        borderWidth: "1",
        flexVerticalAlignment: FlexVerticalAlignment.Stretch,
        key: "dnvr72p9zg",
        backgroundColor:
          "{{currentItem.priority === 'high' ? '#b91c1c' : 'white'}}",
        isDeprecated: false,
        rightColumn: 64,
        dynamicHeight: "FIXED",
        widgetId: "qqyhz7kz6a",
        containerStyle: "card",
        minWidth: 450,
        isVisible: true,
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Layout"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "0.375rem",
        mobileLeftColumn: 41,
        maxDynamicHeight: 9000,
        minDynamicHeight: 10,
      },
      {
        boxShadow: "{{appsmith.theme.boxShadow.appBoxShadow}}",
        mobileBottomRow: 100,
        widgetName: "Canvas4",
        displayName: "Canvas",
        topRow: 0,
        bottomRow: 80,
        parentRowSpace: 1,
        type: "CANVAS_WIDGET",
        canExtend: false,
        hideCard: true,
        minHeight: 100,
        mobileRightColumn: 285.0770263671875,
        parentColumnSpace: 1,
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "borderRadius",
          },
          {
            key: "boxShadow",
          },
        ],
        children: [],
        key: "79rgo5t4z8",
        isDeprecated: false,
        rightColumn: 285.0770263671875,
        detachFromLayout: true,
        dynamicHeight: "AUTO_HEIGHT",
        widgetId: "zseauuyzdw",
        containerStyle: "none",
        minWidth: 450,
        isVisible: true,
        version: 1,
        parentId: "qqyhz7kz6a",
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 0,
        maxDynamicHeight: 9000,
        minDynamicHeight: 4,
        flexLayers: [],
      },
      {
        boxShadow: "none",
        mobileBottomRow: 8,
        widgetName: "txt_description",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 0,
        bottomRow: 4,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 24,
        animateLoading: true,
        overflow: "NONE",
        dynamicTriggerPathList: [],
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        dynamicBindingPathList: [
          {
            key: "text",
          },
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
          {
            key: "text",
          },
        ],
        leftColumn: 0,
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "{{currentItem.description}}",
        key: "av6oo17qn4",
        isDeprecated: false,
        rightColumn: 26,
        textAlign: "LEFT",
        dynamicHeight: "FIXED",
        widgetId: "8x2cgauju9",
        minWidth: 450,
        isVisible: true,
        fontStyle: "BOLD",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 4,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 16,
        maxDynamicHeight: 9000,
        fontSize: "0.875rem",
        textStyle: "BODY",
        minDynamicHeight: 4,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 4,
        widgetName: "txt_assignedTo",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 0,
        bottomRow: 4,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 28,
        animateLoading: true,
        overflow: "NONE",
        dynamicTriggerPathList: [],
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        dynamicBindingPathList: [
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
          {
            key: "text",
          },
        ],
        leftColumn: 40,
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "{{currentItem.assigned_to ? currentItem.assigned_to : 'Not Assigned'}}",
        key: "av6oo17qn4",
        isDeprecated: false,
        rightColumn: 53,
        textAlign: "LEFT",
        dynamicHeight: "FIXED",
        widgetId: "cdo8lfmyq2",
        minWidth: 450,
        isVisible: true,
        fontStyle: "",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 16,
        maxDynamicHeight: 9000,
        fontSize: "0.875rem",
        textStyle: "HEADING",
        minDynamicHeight: 4,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 4,
        widgetName: "txt_dueDate",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 4,
        bottomRow: 8,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 28,
        animateLoading: true,
        overflow: "NONE",
        dynamicTriggerPathList: [],
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        dynamicBindingPathList: [
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
          {
            key: "text",
          },
        ],
        leftColumn: 40,
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "{{new Date(currentItem.due_date).toDateString().substring(3)}}",
        key: "av6oo17qn4",
        isDeprecated: false,
        rightColumn: 53,
        textAlign: "LEFT",
        dynamicHeight: "FIXED",
        widgetId: "u26bhtsugr",
        minWidth: 450,
        isVisible: true,
        fontStyle: "",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 16,
        maxDynamicHeight: 9000,
        fontSize: "0.875rem",
        textStyle: "HEADING",
        minDynamicHeight: 4,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 4,
        widgetName: "txt_dueDateLabel",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 4,
        bottomRow: 8,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 28,
        animateLoading: true,
        overflow: "NONE",
        dynamicTriggerPathList: [],
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        dynamicBindingPathList: [
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
        ],
        leftColumn: 26,
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "Due Date:",
        key: "av6oo17qn4",
        isDeprecated: false,
        rightColumn: 39,
        textAlign: "LEFT",
        dynamicHeight: "FIXED",
        widgetId: "eeg88q6qo0",
        minWidth: 450,
        isVisible: true,
        fontStyle: "BOLD",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 16,
        maxDynamicHeight: 9000,
        fontSize: "0.875rem",
        textStyle: "HEADING",
        minDynamicHeight: 4,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 4,
        widgetName: "txt_assignedToLabel",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 0,
        bottomRow: 4,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 28,
        animateLoading: true,
        overflow: "NONE",
        dynamicTriggerPathList: [],
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        dynamicBindingPathList: [
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
        ],
        leftColumn: 26,
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "Assigned To:",
        key: "av6oo17qn4",
        isDeprecated: false,
        rightColumn: 39,
        textAlign: "LEFT",
        dynamicHeight: "FIXED",
        widgetId: "zhwuvr8gny",
        minWidth: 450,
        isVisible: true,
        fontStyle: "BOLD",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 16,
        maxDynamicHeight: 9000,
        fontSize: "0.875rem",
        textStyle: "HEADING",
        minDynamicHeight: 4,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 4,
        widgetName: "txt_user",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 4,
        bottomRow: 8,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 28,
        animateLoading: true,
        overflow: "NONE",
        dynamicTriggerPathList: [],
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        dynamicBindingPathList: [
          {
            key: "text",
          },
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
          {
            key: "text",
          },
        ],
        leftColumn: 0,
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "{{currentItem.user}}",
        key: "av6oo17qn4",
        isDeprecated: false,
        rightColumn: 26,
        textAlign: "LEFT",
        dynamicHeight: "FIXED",
        widgetId: "oeu7013kx2",
        minWidth: 450,
        isVisible: true,
        fontStyle: "BOLD",
        textColor: "#231F20",
        version: 1,
        parentId: "fy9k9pu256",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 0,
        responsiveBehavior: ResponsiveBehavior.Fill,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 16,
        maxDynamicHeight: 9000,
        fontSize: "0.875rem",
        textStyle: "HEADING",
        minDynamicHeight: 4,
        parentColumnSpace: 13,
        parentRowSpace: 10,
      },
      {
        boxShadow: "none",
        mobileBottomRow: 20,
        widgetName: "icn_resetFiltersButton",
        onClick:
          "{{\n(() => {\nresetWidget('sel_status');\nresetWidget('sel_sort');\nfunctions.getFilteredTickets();\n})()\n}}",
        buttonColor: "{{appsmith.theme.colors.primaryColor}}",
        dynamicPropertyPathList: [
          {
            key: "onClick",
          },
        ],
        displayName: "Icon button",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.b08054586989b185a0801e9a34f8ad49.svg",
        searchTags: ["click", "submit"],
        topRow: 7,
        bottomRow: 12,
        tooltip: "Reset Filters",
        parentRowSpace: 10,
        type: "ICON_BUTTON_WIDGET",
        hideCard: false,
        mobileRightColumn: 63,
        animateLoading: true,
        parentColumnSpace: 12.280303955078125,
        dynamicTriggerPathList: [
          {
            key: "onClick",
          },
        ],
        leftColumn: 60,
        dynamicBindingPathList: [
          {
            key: "buttonColor",
          },
          {
            key: "borderRadius",
          },
        ],
        isDisabled: false,
        key: "gvvmaeh2y0",
        isDeprecated: false,
        rightColumn: 64,
        iconName: "reset",
        widgetId: "dfcdc9r5im",
        minWidth: 50,
        isVisible: true,
        version: 1,
        parentId: "za7wfwo8ko",
        tags: ["Buttons"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 16,
        responsiveBehavior: ResponsiveBehavior.Hug,
        originalTopRow: 7,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 59,
        originalBottomRow: 12,
        buttonVariant: "PRIMARY",
      },
      {
        boxShadow: "none",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a7b19dc8b31d68fcff57f1d2c0084a18.svg",
        labelText: "Sort by Date",
        topRow: 5,
        labelWidth: 5,
        type: "SELECT_WIDGET",
        serverSideFiltering: false,
        defaultOptionValue: "newest",
        animateLoading: true,
        leftColumn: 18,
        dynamicBindingPathList: [
          {
            key: "accentColor",
          },
          {
            key: "borderRadius",
          },
        ],
        placeholderText: "Select option",
        isDisabled: false,
        isRequired: false,
        dynamicHeight: "FIXED",
        accentColor: "{{appsmith.theme.colors.primaryColor}}",
        isVisible: true,
        version: 1,
        tags: ["Suggested", "Select"],
        isLoading: false,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        originalBottomRow: 12,
        mobileBottomRow: 22,
        widgetName: "sel_sort",
        isFilterable: false,
        dynamicPropertyPathList: [
          {
            key: "sourceData",
          },
        ],
        displayName: "Select",
        searchTags: ["dropdown"],
        bottomRow: 12,
        parentRowSpace: 10,
        hideCard: false,
        mobileRightColumn: 13,
        parentColumnSpace: 12.280303955078125,
        dynamicTriggerPathList: [
          {
            key: "onOptionChange",
          },
        ],
        labelPosition: "Top",
        sourceData:
          '[\n  {\n    "name": "Newest",\n    "code": "newest"\n  },\n  {\n    "name": "Oldest",\n    "code": "oldest"\n  }\n]',
        key: "ljnwdbfwyl",
        labelTextSize: "0.875rem",
        isDeprecated: false,
        rightColumn: 37,
        widgetId: "mks70rbc2g",
        optionValue: "code",
        minWidth: 450,
        parentId: "za7wfwo8ko",
        labelAlignment: "left",
        renderMode: "CANVAS",
        mobileTopRow: 15,
        optionLabel: "name",
        responsiveBehavior: ResponsiveBehavior.Fill,
        originalTopRow: 5,
        mobileLeftColumn: 0,
        maxDynamicHeight: 9000,
        onOptionChange: "{{functions.getFilteredTickets();}}",
        minDynamicHeight: 4,
      },
      {
        boxShadow: "none",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a7b19dc8b31d68fcff57f1d2c0084a18.svg",
        labelText: "Filter Status",
        topRow: 5,
        labelWidth: 5,
        type: "SELECT_WIDGET",
        serverSideFiltering: false,
        defaultOptionValue: "",
        animateLoading: true,
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "accentColor",
          },
          {
            key: "borderRadius",
          },
        ],
        placeholderText: "Status",
        isDisabled: false,
        isRequired: false,
        dynamicHeight: "FIXED",
        accentColor: "{{appsmith.theme.colors.primaryColor}}",
        isVisible: true,
        version: 1,
        tags: ["Suggested", "Select"],
        isLoading: false,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        originalBottomRow: 12,
        mobileBottomRow: 19,
        widgetName: "sel_status",
        isFilterable: false,
        dynamicPropertyPathList: [
          {
            key: "sourceData",
          },
        ],
        displayName: "Select",
        searchTags: ["dropdown"],
        bottomRow: 12,
        parentRowSpace: 10,
        hideCard: false,
        mobileRightColumn: 47,
        parentColumnSpace: 11.7392578125,
        dynamicTriggerPathList: [
          {
            key: "onOptionChange",
          },
        ],
        labelPosition: "Top",
        sourceData:
          '[\n  {\n    "name": "Open",\n    "code": "open"\n  },\n  {\n    "name": "In Progress",\n    "code": "in progress"\n  },\n  {\n    "name": "Closed",\n    "code": "closed"\n  }\n]',
        key: "u8mwcq927c",
        labelTextSize: "0.875rem",
        isDeprecated: false,
        rightColumn: 18,
        widgetId: "k26uf25ngy",
        optionValue: "code",
        minWidth: 450,
        parentId: "za7wfwo8ko",
        labelAlignment: "left",
        renderMode: "CANVAS",
        mobileTopRow: 12,
        optionLabel: "name",
        responsiveBehavior: ResponsiveBehavior.Fill,
        originalTopRow: 5,
        mobileLeftColumn: 27,
        maxDynamicHeight: 9000,
        onOptionChange: "{{functions.getFilteredTickets();}}",
        minDynamicHeight: 4,
      },
      {
        mobileBottomRow: 8,
        widgetName: "txt_title",
        displayName: "Text",
        iconSVG:
          "https://appcdn.appsmith.com/static/media/icon.a47d6d5dbbb718c4dc4b2eb4f218c1b7.svg",
        searchTags: ["typography", "paragraph", "label"],
        topRow: 0,
        bottomRow: 5,
        parentRowSpace: 10,
        type: "TEXT_WIDGET",
        hideCard: false,
        mobileRightColumn: 21,
        animateLoading: true,
        overflow: "NONE",
        fontFamily: "{{appsmith.theme.fontFamily.appFont}}",
        parentColumnSpace: 20.75,
        dynamicTriggerPathList: [],
        leftColumn: 0,
        dynamicBindingPathList: [
          {
            key: "truncateButtonColor",
          },
          {
            key: "fontFamily",
          },
          {
            key: "borderRadius",
          },
        ],
        shouldTruncate: false,
        truncateButtonColor: "{{appsmith.theme.colors.primaryColor}}",
        text: "Support Queries",
        key: "wp8ijwfpam",
        isDeprecated: false,
        rightColumn: 64,
        textAlign: "LEFT",
        dynamicHeight: "AUTO_HEIGHT",
        widgetId: "wrggux789b",
        minWidth: 450,
        isVisible: true,
        fontStyle: "BOLD",
        textColor: "#231F20",
        version: 1,
        parentId: "za7wfwo8ko",
        tags: ["Suggested", "Content"],
        renderMode: "CANVAS",
        isLoading: false,
        mobileTopRow: 4,
        responsiveBehavior: ResponsiveBehavior.Fill,
        originalTopRow: 0,
        borderRadius: "{{appsmith.theme.borderRadius.appBorderRadius}}",
        mobileLeftColumn: 5,
        maxDynamicHeight: 9000,
        originalBottomRow: 5,
        fontSize: "1.875rem",
        minDynamicHeight: 4,
      },
    ],
    parentId: "0",
    widgetId: "ppci5prygm",
  },
];
