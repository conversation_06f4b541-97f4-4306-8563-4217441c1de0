{"extends": ["../.eslintrc.base.json"], "ignorePatterns": ["locators", "fixtures"], "env": {"cypress/globals": true}, "rules": {"@typescript-eslint/prefer-nullish-coalescing": "off", "@typescript-eslint/strict-boolean-expressions": "off", "@typescript-eslint/no-array-constructor": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-unused-vars": "off", "cypress/no-unnecessary-waiting": "off", "no-console": "off", "prefer-const": "off", "@typescript-eslint/no-non-null-assertion": "error", "cypress/unsafe-to-chain-command": "off", "@typescript-eslint/adjacent-overload-signatures": "off", "jest/no-disabled-tests": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "padding-line-between-statements": "off"}}