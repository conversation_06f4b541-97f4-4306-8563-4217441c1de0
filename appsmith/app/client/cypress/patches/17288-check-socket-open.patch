--- Cypress/resources/app/node_modules/http-proxy/lib/http-proxy/passes/ws-incoming.js
+++ Cypress/resources/app/node_modules/http-proxy/lib/http-proxy/passes/ws-incoming.js
@@ -111,7 +111,7 @@ module.exports = {
     proxyReq.on('error', onOutgoingError);
     proxyReq.on('response', function (res) {
       // if upgrade event isn't going to happen, close the socket
-      if (!res.upgrade && !socket.destroyed) {
+      if (!res.upgrade && socket.readyState === socket.OPEN && !socket.destroyed) {
         socket.write(createHttpHeader('HTTP/' + res.httpVersion + ' ' + res.statusCode + ' ' + res.statusMessage, res.headers));
         res.pipe(socket);
       }
