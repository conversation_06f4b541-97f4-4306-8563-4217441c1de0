{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1296, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 800, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 51, "minHeight": 810, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"isVisible": true, "backgroundColor": "transparent", "itemBackgroundColor": "#FFFFFF", "animateLoading": true, "gridType": "vertical", "template": {}, "enhancements": true, "gridGap": 0, "listData": [{"id": "001", "name": "Blue", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "002", "name": "Green", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "003", "name": "Red", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}], "widgetName": "List1", "children": [{"isVisible": true, "widgetName": "Canvas6", "version": 1, "detachFromLayout": true, "type": "CANVAS_WIDGET", "hideCard": true, "displayName": "<PERSON><PERSON>", "key": "prgmua3liw", "containerStyle": "none", "canExtend": false, "dropDisabled": true, "openParentPropertyPane": true, "noPad": true, "children": [{"isVisible": true, "backgroundColor": "white", "widgetName": "Container1", "containerStyle": "card", "borderColor": "transparent", "borderWidth": "0", "borderRadius": "0", "boxShadow": "NONE", "animateLoading": true, "children": [{"isVisible": true, "widgetName": "Canvas7", "version": 1, "detachFromLayout": true, "type": "CANVAS_WIDGET", "hideCard": true, "displayName": "<PERSON><PERSON>", "key": "prgmua3liw", "containerStyle": "none", "canExtend": false, "children": [], "minHeight": null, "widgetId": "wof8oynef3", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": null, "topRow": 0, "bottomRow": 20, "parentId": "72hv75hszx"}], "version": 1, "type": "CONTAINER_WIDGET", "hideCard": false, "displayName": "Container", "key": "5v3ghmpxex", "iconSVG": "/static/media/icon.1977dca3.svg", "isCanvas": true, "dragDisabled": true, "isDeletable": false, "disallowCopy": true, "disablePropertyPane": true, "openParentPropertyPane": true, "widgetId": "72hv75hszx", "renderMode": "CANVAS", "isLoading": false, "leftColumn": 0, "rightColumn": 64, "topRow": 0, "bottomRow": 62, "parentId": "vnwk63rc14"}], "minHeight": 400, "widgetId": "vnwk63rc14", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 481.5, "topRow": 0, "bottomRow": 640, "parentId": "1qyviq7phz"}], "type": "LIST_WIDGET", "hideCard": false, "displayName": "List", "key": "twqaemvoat", "iconSVG": "/static/media/icon.9925ee17.svg", "isCanvas": true, "widgetId": "1qyviq7phz", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 20.0625, "parentRowSpace": 10, "leftColumn": 3, "rightColumn": 50, "topRow": 3, "bottomRow": 72, "parentId": "0", "dynamicBindingPathList": [], "privateWidgets": {}, "dynamicTriggerPathList": []}]}}