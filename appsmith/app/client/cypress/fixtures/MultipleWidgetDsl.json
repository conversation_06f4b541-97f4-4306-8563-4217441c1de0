{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224, "snapColumns": 16, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 1280, "containerStyle": "none", "snapRows": 33, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 8, "minHeight": 1292, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"isVisible": true, "inputType": "TEXT", "label": "", "widgetName": "Input1", "type": "INPUT_WIDGET_V2", "isLoading": false, "parentColumnSpace": 74, "parentRowSpace": 40, "leftColumn": 6, "rightColumn": 11, "topRow": 13, "bottomRow": 14, "parentId": "0", "widgetId": "1bek8n8byg"}, {"isVisible": true, "label": "", "selectionType": "SINGLE_SELECT", "sourceData": "", "optionLabel": "label", "optionValue": "value", "dynamicPropertyPathList": [{"key": "sourceData"}], "widgetName": "Dropdown1", "defaultOptionValue": {"value": "VEG", "label": "VEG"}, "type": "SELECT_WIDGET", "isLoading": false, "parentColumnSpace": 74, "parentRowSpace": 40, "leftColumn": 0, "rightColumn": 5, "topRow": 15, "bottomRow": 16, "parentId": "0", "widgetId": "zd6jycngj7", "dynamicBindingPathList": []}, {"isVisible": true, "label": "Data", "widgetName": "Table1", "searchKey": "", "tableData": "{{[\n  {\n    \"id\": 2381224,\n    \"email\": \"micha<PERSON>.<EMAIL>\",\n    \"userName\": \"<PERSON>\",\n    \"productName\": \"Chicken Sandwich\",\n    \"orderAmount\": 4.99\n  },\n  {\n    \"id\": 2736212,\n    \"email\": \"<EMAIL>\",\n    \"userName\": \"<PERSON>\",\n    \"productName\": \"Tuna Sal<PERSON>\",\n    \"orderAmount\": 9.99\n  },\n  {\n    \"id\": 6788734,\n    \"email\": \"<EMAIL>\",\n    \"userName\": \"<PERSON>\",\n    \"productName\": \"Beef steak\",\n    \"orderAmount\": 19.99\n  }\n]}}", "type": "TABLE_WIDGET", "isLoading": false, "parentColumnSpace": 74, "parentRowSpace": 40, "leftColumn": 4, "rightColumn": 12, "topRow": 19, "bottomRow": 26, "parentId": "0", "widgetId": "ei38nqop3s", "dynamicBindingPathList": []}]}}