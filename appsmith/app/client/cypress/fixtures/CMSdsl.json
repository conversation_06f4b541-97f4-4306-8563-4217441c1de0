{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1118, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 750, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 45, "minHeight": 740, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"boxShadow": "NONE", "widgetName": "Container1", "borderColor": "transparent", "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3.svg", "topRow": 10, "bottomRow": 70, "parentRowSpace": 10, "type": "CONTAINER_WIDGET", "hideCard": false, "parentColumnSpace": 17.28125, "leftColumn": 2, "children": [{"widgetName": "Canvas1", "rightColumn": 414.75, "detachFromLayout": true, "displayName": "<PERSON><PERSON>", "widgetId": "zos1ro7s26", "containerStyle": "none", "topRow": 0, "bottomRow": 570, "parentRowSpace": 1, "isVisible": true, "type": "CANVAS_WIDGET", "canExtend": false, "version": 1, "hideCard": true, "parentId": "qbyu9spy6e", "minHeight": 400, "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "leftColumn": 0, "children": [{"widgetName": "Table1", "defaultPageSize": 0, "columnOrder": ["id", "due", "assignee", "title", "description"], "isVisibleDownload": true, "dynamicPropertyPathList": [], "displayName": "Table", "iconSVG": "/static/media/icon.db8a9cbd.svg", "topRow": 1, "bottomRow": 49, "isSortable": true, "parentRowSpace": 10, "type": "TABLE_WIDGET", "defaultSelectedRow": "0", "hideCard": false, "parentColumnSpace": 15.888671875, "dynamicTriggerPathList": [], "dynamicBindingPathList": [{"key": "tableData"}, {"key": "primaryColumns.due.computedValue"}, {"key": "primaryColumns.assignee.computedValue"}, {"key": "primaryColumns.title.computedValue"}, {"key": "primaryColumns.description.computedValue"}, {"key": "primaryColumns.id.computedValue"}], "leftColumn": 3, "primaryColumns": {"due": {"index": 0, "width": 150, "id": "due", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "date", "textSize": "PARAGRAPH", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellVisible": true, "isDerived": false, "label": "Due date", "computedValue": "{{Table1.sanitizedTableData.map((currentRow) => ( currentRow.due))}}", "inputFormat": "YYYY-MM-DD", "outputFormat": "YYYY-MM-DD"}, "assignee": {"index": 1, "width": 150, "id": "assignee", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "PARAGRAPH", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellVisible": true, "isDerived": false, "label": "Assignee", "computedValue": "{{Table1.sanitizedTableData.map((currentRow) => ( currentRow.assignee))}}"}, "title": {"index": 2, "width": 150, "id": "title", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "PARAGRAPH", "enableFilter": true, "enableSort": true, "isVisible": false, "isDisabled": false, "isCellVisible": true, "isDerived": false, "label": "title", "computedValue": "{{Table1.sanitizedTableData.map((currentRow) => ( currentRow.title))}}"}, "description": {"index": 3, "width": 150, "id": "description", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "PARAGRAPH", "enableFilter": true, "enableSort": true, "isVisible": false, "isDisabled": false, "isCellVisible": true, "isDerived": false, "label": "description", "computedValue": "{{Table1.sanitizedTableData.map((currentRow) => ( currentRow.description))}}"}, "id": {"index": 4, "width": 150, "id": "id", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "PARAGRAPH", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellVisible": true, "isDerived": false, "label": "id", "computedValue": "{{Table1.sanitizedTableData.map((currentRow) => ( currentRow.id))}}"}}, "delimiter": ",", "key": "gim9l3s5p2", "derivedColumns": {}, "rightColumn": 28, "textSize": "PARAGRAPH", "widgetId": "h4mh57zrj1", "isVisibleFilters": true, "tableData": "{{get_data.data.headers.Info}}", "isVisible": true, "label": "Data", "searchKey": "", "version": 3, "totalRecordsCount": 0, "parentId": "zos1ro7s26", "renderMode": "CANVAS", "isLoading": false, "horizontalAlignment": "LEFT", "isVisibleSearch": true, "isVisiblePagination": true, "verticalAlignment": "CENTER", "columnSizeMap": {"task": 245, "step": 62, "status": 75, "due": 114, "id": 60}}, {"widgetName": "Form1", "backgroundColor": "white", "rightColumn": 61, "isCanvas": true, "displayName": "Form", "iconSVG": "/static/media/icon.ea3e08d1.svg", "widgetId": "nnyzjgf35r", "topRow": 1, "bottomRow": 49, "parentRowSpace": 10, "isVisible": true, "type": "FORM_WIDGET", "hideCard": false, "parentId": "zos1ro7s26", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 15.888671875, "leftColumn": 32, "children": [{"widgetName": "Canvas2", "rightColumn": 381.328125, "detachFromLayout": true, "displayName": "<PERSON><PERSON>", "widgetId": "hvvj91zc5v", "containerStyle": "none", "topRow": 0, "bottomRow": 470, "parentRowSpace": 1, "isVisible": true, "type": "CANVAS_WIDGET", "canExtend": false, "version": 1, "hideCard": true, "parentId": "nnyzjgf35r", "minHeight": 400, "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "leftColumn": 0, "children": [{"resetFormOnClick": true, "widgetName": "FormButton1", "onClick": "{{showModal(Modal1.name)}}", "buttonColor": "#03B365", "dynamicPropertyPathList": [{"key": "onClick"}], "displayName": "FormButton", "iconSVG": "/static/media/icon.c8f649ed.svg", "topRow": 41, "bottomRow": 45, "type": "FORM_BUTTON_WIDGET", "hideCard": true, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 46, "dynamicBindingPathList": [], "text": "Mail", "key": "3yro25toql", "rightColumn": 62, "isDefaultClickDisabled": true, "widgetId": "xg747y64mq", "recaptchaV2": false, "isVisible": true, "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": true, "buttonVariant": "PRIMARY"}, {"widgetName": "Input1", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 8, "bottomRow": 20, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 5.645751953125, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 23, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 62, "widgetId": "o97o13rsru", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.description}}"}, {"widgetName": "Input2", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 22, "bottomRow": 26, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 5.645751953125, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 24, "dynamicBindingPathList": [], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 53, "widgetId": "nfx01pnpxu", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "In Progress"}, {"widgetName": "Input3", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 28, "bottomRow": 32, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 5.645751953125, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 24, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 39, "widgetId": "udt44xvy4p", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.due}}"}, {"widgetName": "Input4", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 34, "bottomRow": 38, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 5.645751953125, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 24, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "EMAIL", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 56, "widgetId": "xf4mcip7d2", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.assignee}}"}, {"widgetName": "Input5", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 2, "bottomRow": 6, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.887054443359375, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 23, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 62, "widgetId": "5ma00c7uoh", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.title}}"}, {"widgetName": "Text4", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 2, "bottomRow": 6, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.887054443359375, "dynamicTriggerPathList": [], "leftColumn": 3, "dynamicBindingPathList": [], "text": "Title", "key": "6szdyqy4kw", "rightColumn": 19, "textAlign": "LEFT", "widgetId": "0cqbtz8byh", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text5", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 8, "bottomRow": 12, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.887054443359375, "dynamicTriggerPathList": [], "leftColumn": 3, "dynamicBindingPathList": [], "text": "Description", "key": "6szdyqy4kw", "rightColumn": 19, "textAlign": "LEFT", "widgetId": "clk59gs1y0", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text6", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 27, "bottomRow": 31, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.887054443359375, "dynamicTriggerPathList": [], "leftColumn": 3, "dynamicBindingPathList": [], "text": "Due", "key": "6szdyqy4kw", "rightColumn": 19, "textAlign": "LEFT", "widgetId": "qvw2pd4je8", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text7", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 34, "bottomRow": 38, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.887054443359375, "dynamicTriggerPathList": [], "leftColumn": 3, "dynamicBindingPathList": [], "text": "Assignee", "key": "6szdyqy4kw", "rightColumn": 19, "textAlign": "LEFT", "widgetId": "oc6y87200o", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text8", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 22, "bottomRow": 26, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.887054443359375, "dynamicTriggerPathList": [], "leftColumn": 3, "dynamicBindingPathList": [], "text": "Status", "key": "6szdyqy4kw", "rightColumn": 19, "textAlign": "LEFT", "widgetId": "z9ke6qdjsv", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "hvvj91zc5v", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}], "key": "op9a2ii2bm"}], "key": "7me5bjjd3w"}, {"widgetName": "Divider1", "thickness": 2, "displayName": "Divider", "iconSVG": "/static/media/icon.cbe8f608.svg", "topRow": 1, "bottomRow": 49, "parentRowSpace": 10, "type": "DIVIDER_WIDGET", "capType": "nc", "hideCard": false, "parentColumnSpace": 15.888671875, "dynamicTriggerPathList": [], "leftColumn": 29, "dynamicBindingPathList": [], "key": "ddn6drz055", "dividerColor": "#EBEBEB", "orientation": "vertical", "strokeStyle": "solid", "rightColumn": 31, "widgetId": "1buwzc2fir", "capSide": 0, "isVisible": true, "version": 1, "parentId": "zos1ro7s26", "renderMode": "CANVAS", "isLoading": false}, {"widgetName": "Button4", "onClick": "{{showModal(Modal2.name)}}", "buttonColor": "#03B365", "dynamicPropertyPathList": [{"key": "onClick"}], "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca02633.svg", "topRow": 51, "bottomRow": 55, "parentRowSpace": 10, "type": "BUTTON_WIDGET", "hideCard": false, "parentColumnSpace": 17.28125, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 3, "dynamicBindingPathList": [], "text": "Delete Proposal", "isDisabled": false, "key": "8qgjvzg52n", "rightColumn": 13, "isDefaultClickDisabled": true, "widgetId": "ykz7d6gbld", "recaptchaV2": false, "isVisible": true, "version": 1, "parentId": "zos1ro7s26", "renderMode": "CANVAS", "isLoading": false, "buttonVariant": "PRIMARY"}], "key": "op9a2ii2bm"}], "borderWidth": "0", "key": "6winp67jci", "backgroundColor": "#FFFFFF", "rightColumn": 62, "widgetId": "qbyu9spy6e", "containerStyle": "card", "isVisible": true, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "0"}, {"boxShadow": "NONE", "widgetName": "Container2", "borderColor": "transparent", "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3.svg", "topRow": 4, "bottomRow": 10, "parentRowSpace": 10, "type": "CONTAINER_WIDGET", "hideCard": false, "parentColumnSpace": 17.28125, "leftColumn": 2, "children": [{"widgetName": "Canvas3", "rightColumn": 414.75, "detachFromLayout": true, "displayName": "<PERSON><PERSON>", "widgetId": "voago0sim9", "containerStyle": "none", "topRow": 0, "bottomRow": 390, "parentRowSpace": 1, "isVisible": true, "type": "CANVAS_WIDGET", "canExtend": false, "version": 1, "hideCard": true, "parentId": "pa6iz68csi", "minHeight": 400, "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "leftColumn": 0, "children": [{"widgetName": "Text2", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 0, "bottomRow": 4, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 15.888671875, "dynamicTriggerPathList": [], "leftColumn": 0, "dynamicBindingPathList": [], "text": "Submit New Proposal", "key": "6szdyqy4kw", "rightColumn": 16, "textAlign": "LEFT", "widgetId": "ml2f9v9ite", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "voago0sim9", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Button1", "buttonColor": "#03B365", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca02633.svg", "topRow": 0, "bottomRow": 4, "parentRowSpace": 10, "type": "BUTTON_WIDGET", "hideCard": false, "parentColumnSpace": 15.888671875, "leftColumn": 54, "text": "Submit", "isDisabled": false, "key": "8qgjvzg52n", "rightColumn": 64, "isDefaultClickDisabled": true, "widgetId": "7h2wrs1iyt", "recaptchaV2": false, "isVisible": true, "version": 1, "parentId": "voago0sim9", "renderMode": "CANVAS", "isLoading": false, "buttonVariant": "PRIMARY"}], "key": "op9a2ii2bm"}], "borderWidth": "0", "key": "6winp67jci", "backgroundColor": "#FFFFFF", "rightColumn": 62, "widgetId": "pa6iz68csi", "containerStyle": "card", "isVisible": true, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "0"}, {"widgetName": "Text3", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 0, "bottomRow": 4, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 17.28125, "dynamicTriggerPathList": [], "leftColumn": 29, "dynamicBindingPathList": [], "text": "Echo CMS ", "key": "6szdyqy4kw", "rightColumn": 36, "textAlign": "LEFT", "widgetId": "7wm8mw3w2d", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Modal1", "isCanvas": true, "displayName": "Modal", "iconSVG": "/static/media/icon.4975978e.svg", "topRow": 22, "bottomRow": 46, "parentRowSpace": 10, "type": "MODAL_WIDGET", "hideCard": false, "shouldScrollContents": true, "parentColumnSpace": 15.888671875, "leftColumn": 21, "children": [{"widgetName": "Canvas4", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 460, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "hideCard": true, "shouldScrollContents": false, "minHeight": 468, "parentColumnSpace": 1, "dynamicTriggerPathList": [], "leftColumn": 0, "dynamicBindingPathList": [], "children": [{"widgetName": "Icon1", "rightColumn": 64, "onClick": "{{closeModal(Modal1.name)}}", "color": "#040627", "iconName": "cross", "displayName": "Icon", "iconSVG": "/static/media/icon.31d6cfe0.svg", "widgetId": "11p85abc5c", "topRow": 1, "bottomRow": 5, "isVisible": true, "type": "ICON_WIDGET", "version": 1, "hideCard": true, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "leftColumn": 58, "iconSize": 24, "key": "8i0vmxrr9g"}, {"widgetName": "Text9", "rightColumn": 41, "textAlign": "LEFT", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "widgetId": "3l9j4myadm", "topRow": 1, "bottomRow": 5, "isVisible": true, "fontStyle": "BOLD", "type": "TEXT_WIDGET", "textColor": "#231F20", "version": 1, "hideCard": false, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "dynamicTriggerPathList": [], "leftColumn": 1, "dynamicBindingPathList": [], "fontSize": "HEADING2", "text": "Send Mail", "key": "6szdyqy4kw"}, {"widgetName": "Button2", "onClick": "{{closeModal(Modal1.name)}}", "buttonColor": "#03B365", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca02633.svg", "topRow": 40, "bottomRow": 44, "type": "BUTTON_WIDGET", "hideCard": false, "leftColumn": 7, "text": "Close", "isDisabled": false, "key": "8qgjvzg52n", "rightColumn": 19, "isDefaultClickDisabled": true, "widgetId": "o4gbjwa6om", "buttonStyle": "PRIMARY", "recaptchaV2": false, "isVisible": true, "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "buttonVariant": "SECONDARY"}, {"widgetName": "Button3", "onClick": "{{send_mail.run()}}", "buttonColor": "#03B365", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca02633.svg", "topRow": 40, "bottomRow": 44, "type": "BUTTON_WIDGET", "hideCard": false, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 45, "dynamicBindingPathList": [], "text": "Confirm", "isDisabled": false, "key": "8qgjvzg52n", "rightColumn": 59, "isDefaultClickDisabled": true, "widgetId": "7wpkvbzz6w", "buttonStyle": "PRIMARY_BUTTON", "recaptchaV2": false, "isVisible": true, "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "buttonVariant": "PRIMARY"}, {"widgetName": "Text10", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 21, "bottomRow": 25, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.34375, "dynamicTriggerPathList": [], "leftColumn": 4, "dynamicBindingPathList": [], "text": "Content", "key": "6szdyqy4kw", "rightColumn": 20, "textAlign": "LEFT", "widgetId": "5bki1kuxaj", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text11", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 15, "bottomRow": 19, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.34375, "dynamicTriggerPathList": [], "leftColumn": 4, "dynamicBindingPathList": [], "text": "Subject", "key": "6szdyqy4kw", "rightColumn": 20, "textAlign": "LEFT", "widgetId": "jfyra9co8z", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text12", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 9, "bottomRow": 13, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.34375, "dynamicTriggerPathList": [], "leftColumn": 4, "dynamicBindingPathList": [], "text": "To", "key": "6szdyqy4kw", "rightColumn": 20, "textAlign": "LEFT", "widgetId": "b7pssbui2t", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "to_input", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 9, "bottomRow": 13, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.34375, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 25, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 60, "widgetId": "q8j9r8htkk", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Input4.text}}"}, {"widgetName": "subject", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 15, "bottomRow": 19, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.34375, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 25, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 60, "widgetId": "hqzde4u9yp", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{send_mail.data.body.subject}}"}, {"widgetName": "content", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 21, "bottomRow": 36, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.34375, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 25, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "0ob107w0gh", "isRequired": false, "rightColumn": 60, "widgetId": "cymag7b6bu", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "6fd0z9su7u", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{send_mail.data.body.content}}"}], "isDisabled": false, "key": "op9a2ii2bm", "rightColumn": 381.328125, "detachFromLayout": true, "widgetId": "6fd0z9su7u", "isVisible": true, "version": 1, "parentId": "pxiiwd73sn", "renderMode": "CANVAS", "isLoading": false}], "key": "72wb1osqtu", "height": 468, "rightColumn": 45, "detachFromLayout": true, "widgetId": "pxiiwd73sn", "canOutsideClickClose": true, "canEscapeKeyClose": true, "version": 2, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "width": 418}, {"widgetName": "Modal2", "isCanvas": true, "displayName": "Modal", "iconSVG": "/static/media/icon.4975978e.svg", "topRow": 15, "bottomRow": 39, "parentRowSpace": 10, "type": "MODAL_WIDGET", "hideCard": false, "shouldScrollContents": true, "parentColumnSpace": 15.888671875, "leftColumn": 16, "children": [{"widgetName": "Canvas5", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 320, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "hideCard": true, "shouldScrollContents": false, "minHeight": 328, "parentColumnSpace": 1, "dynamicTriggerPathList": [], "leftColumn": 0, "dynamicBindingPathList": [], "children": [{"widgetName": "Text15Copy", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 14, "bottomRow": 18, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.0625, "dynamicTriggerPathList": [], "leftColumn": 4, "dynamicBindingPathList": [], "text": "Title", "key": "92hnh6q5gi", "rightColumn": 20, "textAlign": "LEFT", "widgetId": "3lmz9urj17", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "due", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 20, "bottomRow": 24, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.0625, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 27, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "49t2tbzqin", "isRequired": false, "rightColumn": 40, "widgetId": "slrqhjfhmg", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.due}}"}, {"widgetName": "Text13", "rightColumn": 27, "textAlign": "LEFT", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "widgetId": "ovmh5o8gk4", "topRow": 1, "bottomRow": 5, "isVisible": true, "fontStyle": "BOLD", "type": "TEXT_WIDGET", "textColor": "#231F20", "version": 1, "hideCard": false, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "dynamicTriggerPathList": [], "leftColumn": 1, "dynamicBindingPathList": [], "fontSize": "HEADING2", "text": "Delete this task", "key": "6szdyqy4kw"}, {"widgetName": "Button6", "onClick": "{{delete_proposal.run(() => closeModal(), () => {})}}", "buttonColor": "#03B365", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca02633.svg", "topRow": 26, "bottomRow": 30, "type": "BUTTON_WIDGET", "hideCard": false, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 45, "dynamicBindingPathList": [], "text": "Confirm", "isDisabled": false, "key": "8qgjvzg52n", "rightColumn": 60, "isDefaultClickDisabled": true, "widgetId": "lkt2yqvuin", "buttonStyle": "PRIMARY_BUTTON", "recaptchaV2": false, "isVisible": true, "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "buttonVariant": "PRIMARY"}, {"widgetName": "assignee", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 8, "bottomRow": 12, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.0625, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 27, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "49t2tbzqin", "isRequired": false, "rightColumn": 58, "widgetId": "adg0b4mlzi", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.assignee}}"}, {"widgetName": "title", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 14, "bottomRow": 18, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "parentColumnSpace": 6.0625, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 27, "dynamicBindingPathList": [{"key": "defaultText"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "49t2tbzqin", "isRequired": false, "rightColumn": 58, "widgetId": "birjt5sfb1", "isVisible": true, "label": "", "allowCurrencyChange": false, "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": "{{Table1.selectedRow.title}}"}, {"widgetName": "Text14", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 20, "bottomRow": 24, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.0625, "dynamicTriggerPathList": [], "leftColumn": 5, "dynamicBindingPathList": [], "text": "Due", "key": "92hnh6q5gi", "rightColumn": 21, "textAlign": "LEFT", "widgetId": "8ve9jkgxyz", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}, {"widgetName": "Text15", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 8, "bottomRow": 12, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "parentColumnSpace": 6.0625, "dynamicTriggerPathList": [], "leftColumn": 4, "dynamicBindingPathList": [], "text": "Assignee", "key": "92hnh6q5gi", "rightColumn": 20, "textAlign": "LEFT", "widgetId": "bi0yxies82", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "7m8cn2pecl", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}], "isDisabled": false, "key": "op9a2ii2bm", "rightColumn": 381.328125, "detachFromLayout": true, "widgetId": "7m8cn2pecl", "isVisible": true, "version": 1, "parentId": "zy74peeknl", "renderMode": "CANVAS", "isLoading": false}], "key": "72wb1osqtu", "height": 328, "rightColumn": 40, "detachFromLayout": true, "widgetId": "zy74peeknl", "canOutsideClickClose": true, "canEscapeKeyClose": true, "version": 2, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "width": 506}]}}