{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 816, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 270, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 53, "minHeight": 280, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"template": {"Input1": {"isVisible": true, "label": "", "widgetName": "Input1", "version": 2, "defaultText": "", "iconAlign": "left", "autoFocus": false, "labelStyle": "", "resetOnSubmit": true, "isRequired": false, "isDisabled": false, "animateLoading": true, "inputType": "TEXT", "type": "INPUT_WIDGET_V2", "hideCard": false, "displayName": "Input", "key": "ccm4k3q41x", "iconSVG": "/static/media/icon.9f505595.svg", "widgetId": "u0sc4bf6lg", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 7.0859375, "parentRowSpace": 10, "leftColumn": 38, "rightColumn": 58, "topRow": 2, "bottomRow": 6, "parentId": "31i770948x", "logBlackList": {"isVisible": true, "label": true, "widgetName": true, "version": true, "defaultText": true, "iconAlign": true, "autoFocus": true, "labelStyle": true, "resetOnSubmit": true, "isRequired": true, "isDisabled": true, "animateLoading": true, "inputType": true, "type": true, "hideCard": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "minHeight": true, "widgetId": true, "renderMode": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true}}}, "widgetName": "List1", "listData": [{"id": "001", "name": "Blue", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "002", "name": "Green", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "003", "name": "Red", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}], "isCanvas": true, "displayName": "List", "iconSVG": "/static/media/icon.9925ee17.svg", "topRow": 3, "bottomRow": 20, "parentRowSpace": 10, "type": "LIST_WIDGET", "hideCard": false, "gridGap": 0, "animateLoading": true, "parentColumnSpace": 20.0625, "dynamicTriggerPathList": [], "leftColumn": 3, "dynamicBindingPathList": [], "gridType": "vertical", "enhancements": true, "children": [{"widgetName": "Canvas1", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 400, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "dropDisabled": true, "openParentPropertyPane": true, "minHeight": 400, "noPad": true, "parentColumnSpace": 1, "leftColumn": 0, "children": [{"boxShadow": "NONE", "widgetName": "Container1", "borderColor": "transparent", "disallowCopy": true, "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3.svg", "topRow": 0, "bottomRow": 12, "dragDisabled": true, "type": "CONTAINER_WIDGET", "hideCard": false, "openParentPropertyPane": true, "isDeletable": false, "animateLoading": true, "leftColumn": 0, "children": [{"widgetName": "Canvas2", "detachFromLayout": true, "displayName": "<PERSON><PERSON>", "widgetId": "31i770948x", "containerStyle": "none", "topRow": 0, "bottomRow": 80, "parentRowSpace": 1, "isVisible": true, "type": "CANVAS_WIDGET", "canExtend": false, "version": 1, "hideCard": true, "parentId": "wop20uagxv", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "leftColumn": 0, "children": [{"widgetName": "Input1", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595.svg", "topRow": 2, "bottomRow": 6, "parentRowSpace": 10, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "animateLoading": true, "parentColumnSpace": 7.0859375, "resetOnSubmit": true, "leftColumn": 4, "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "ccm4k3q41x", "isRequired": false, "rightColumn": 60, "widgetId": "u0sc4bf6lg", "logBlackList": {"isVisible": true, "label": true, "widgetName": true, "version": true, "defaultText": true, "iconAlign": true, "autoFocus": true, "labelStyle": true, "resetOnSubmit": true, "isRequired": true, "isDisabled": true, "animateLoading": true, "inputType": true, "type": true, "hideCard": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "minHeight": true, "widgetId": true, "renderMode": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true}, "isVisible": true, "label": "", "version": 2, "parentId": "31i770948x", "renderMode": "CANVAS", "isLoading": false, "iconAlign": "left", "defaultText": ""}], "key": "v48l5zt2p7"}], "borderWidth": "0", "key": "w7zaemmrn8", "disablePropertyPane": true, "backgroundColor": "white", "rightColumn": 64, "widgetId": "wop20uagxv", "containerStyle": "card", "isVisible": true, "version": 1, "parentId": "nzmybzveu8", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "0"}], "key": "v48l5zt2p7", "rightColumn": 481.5, "detachFromLayout": true, "widgetId": "nzmybzveu8", "containerStyle": "none", "isVisible": true, "version": 1, "parentId": "78m8pd80bu", "renderMode": "CANVAS", "isLoading": false}], "privateWidgets": {"Input1": true}, "key": "ns0yjeaevj", "backgroundColor": "transparent", "rightColumn": 39, "itemBackgroundColor": "#FFFFFF", "widgetId": "78m8pd80bu", "isVisible": true, "parentId": "0", "renderMode": "CANVAS", "isLoading": false}, {"widgetName": "Text3", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b52.svg", "topRow": 6, "bottomRow": 15, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 20.0625, "dynamicTriggerPathList": [], "leftColumn": 43, "dynamicBindingPathList": [{"key": "text"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "{{List1.items[0].Input1.text}}:{{List1.items[0].Input1.isVisible}}:{{List1.items[0].Input1.isDisabled}}", "key": "pl30s9buf7", "rightColumn": 59, "textAlign": "LEFT", "widgetId": "3ipmljkgo1", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "shouldScroll": false, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "fontSize": "PARAGRAPH"}]}}