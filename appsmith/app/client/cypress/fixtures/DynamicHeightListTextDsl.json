{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 490, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 71, "minHeight": 1292, "dynamicTriggerPathList": [], "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"isVisible": true, "backgroundColor": "transparent", "itemBackgroundColor": "#FFFFFF", "animateLoading": true, "gridType": "vertical", "template": {"Image1": {"isVisible": true, "defaultImage": "http://host.docker.internal:4200/clouddefaultImage.png", "imageShape": "RECTANGLE", "maxZoomLevel": 1, "enableRotation": false, "enableDownload": false, "objectFit": "cover", "image": "{{List1.listData.map((currentItem) => currentItem.img)}}", "widgetName": "Image1", "version": 1, "animateLoading": true, "type": "IMAGE_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Image", "key": "wx4zlsy7dr", "iconSVG": "/static/media/icon.52d8fb963abcb95c79b10f1553389f22.svg", "boxShadow": "none", "dynamicBindingPathList": [{"key": "image"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "widgetId": "2ot120jbrx", "renderMode": "CANVAS", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 0, "rightColumn": 16, "topRow": 0, "bottomRow": 8, "parentId": "5au9y2ssom"}, "Text1": {"isVisible": true, "text": "{{List1.listData.map((currentItem) => currentItem.name)}}", "fontSize": "1rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "widgetName": "Text1", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "reqlgz3yka", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "textStyle": "HEADING", "boxShadow": "none", "dynamicBindingPathList": [{"key": "text"}, {"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "widgetId": "nc7q04zsip", "renderMode": "CANVAS", "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 16, "rightColumn": 28, "topRow": 0, "bottomRow": 4, "parentId": "5au9y2ssom"}, "Text2": {"isVisible": true, "text": "{{List1.listData.map((currentItem) => currentItem.id)}}", "fontSize": "1rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "widgetName": "Text2", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "reqlgz3yka", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "textStyle": "BODY", "boxShadow": "none", "dynamicBindingPathList": [{"key": "text"}, {"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "widgetId": "c77g11n6cr", "renderMode": "CANVAS", "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 16, "rightColumn": 24, "topRow": 4, "bottomRow": 8, "parentId": "5au9y2ssom"}}, "enhancements": true, "gridGap": 0, "listData": [{"id": "001", "name": "Blue", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "002", "name": "Green", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "003", "name": "Red", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}], "widgetName": "List1", "children": [{"isVisible": true, "widgetName": "Canvas1", "version": 1, "detachFromLayout": true, "type": "CANVAS_WIDGET", "hideCard": true, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "05bjn17eby", "containerStyle": "none", "canExtend": false, "dropDisabled": true, "openParentPropertyPane": true, "noPad": true, "children": [{"isVisible": true, "backgroundColor": "white", "widgetName": "Container1", "containerStyle": "card", "borderColor": "#E0DEDE", "borderWidth": "1", "boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "animateLoading": true, "children": [{"isVisible": true, "widgetName": "Canvas2", "version": 1, "detachFromLayout": true, "type": "CANVAS_WIDGET", "hideCard": true, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "05bjn17eby", "containerStyle": "none", "canExtend": false, "children": [{"isVisible": true, "defaultImage": "http://host.docker.internal:4200/clouddefaultImage.png", "imageShape": "RECTANGLE", "maxZoomLevel": 1, "enableRotation": false, "enableDownload": false, "objectFit": "cover", "image": "{{currentItem.img}}", "widgetName": "Image1", "version": 1, "animateLoading": true, "type": "IMAGE_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Image", "key": "wx4zlsy7dr", "iconSVG": "/static/media/icon.52d8fb963abcb95c79b10f1553389f22.svg", "boxShadow": "none", "dynamicBindingPathList": [{"key": "image"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "widgetId": "2ot120jbrx", "renderMode": "CANVAS", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 0, "rightColumn": 16, "topRow": 0, "bottomRow": 8, "parentId": "5au9y2ssom", "logBlackList": {"isVisible": true, "defaultImage": true, "imageShape": true, "maxZoomLevel": true, "enableRotation": true, "enableDownload": true, "objectFit": true, "image": true, "widgetName": true, "version": true, "animateLoading": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "boxShadow": true, "dynamicBindingPathList": true, "dynamicTriggerPathList": true, "minHeight": true, "widgetId": true, "renderMode": true, "borderRadius": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true}}, {"isVisible": true, "text": "{{currentItem.name}}", "fontSize": "1rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "widgetName": "Text1", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "reqlgz3yka", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "textStyle": "HEADING", "boxShadow": "none", "dynamicBindingPathList": [{"key": "text"}, {"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "widgetId": "nc7q04zsip", "renderMode": "CANVAS", "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 16, "rightColumn": 28, "topRow": 0, "bottomRow": 4, "parentId": "5au9y2ssom", "logBlackList": {"isVisible": true, "text": true, "fontSize": true, "fontStyle": true, "textAlign": true, "textColor": true, "widgetName": true, "shouldTruncate": true, "overflow": true, "version": true, "animateLoading": true, "minDynamicHeight": true, "maxDynamicHeight": true, "dynamicHeight": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "textStyle": true, "boxShadow": true, "dynamicBindingPathList": true, "dynamicTriggerPathList": true, "minHeight": true, "widgetId": true, "renderMode": true, "truncateButtonColor": true, "fontFamily": true, "borderRadius": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true}}, {"isVisible": true, "text": "{{currentItem.id}}", "fontSize": "1rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "widgetName": "Text2", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "reqlgz3yka", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "textStyle": "BODY", "boxShadow": "none", "dynamicBindingPathList": [{"key": "text"}, {"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "widgetId": "c77g11n6cr", "renderMode": "CANVAS", "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 16, "rightColumn": 24, "topRow": 4, "bottomRow": 8, "parentId": "5au9y2ssom", "logBlackList": {"isVisible": true, "text": true, "fontSize": true, "fontStyle": true, "textAlign": true, "textColor": true, "widgetName": true, "shouldTruncate": true, "overflow": true, "version": true, "animateLoading": true, "minDynamicHeight": true, "maxDynamicHeight": true, "dynamicHeight": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "textStyle": true, "boxShadow": true, "dynamicBindingPathList": true, "dynamicTriggerPathList": true, "minHeight": true, "widgetId": true, "renderMode": true, "truncateButtonColor": true, "fontFamily": true, "borderRadius": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true}}], "minHeight": null, "widgetId": "5au9y2ssom", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": null, "topRow": 0, "bottomRow": 380, "parentId": "yqzayxlaq7", "dynamicBindingPathList": []}], "version": 1, "minDynamicHeight": 10, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "shouldScrollContents": true, "searchTags": ["div", "parent", "group"], "type": "CONTAINER_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Container", "key": "dk4hjfw4uv", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "isCanvas": true, "dragDisabled": true, "isDeletable": false, "disallowCopy": true, "disablePropertyPane": true, "openParentPropertyPane": true, "widgetId": "yqzayxlaq7", "renderMode": "CANVAS", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 0, "rightColumn": 64, "topRow": 0, "bottomRow": 12, "parentId": "xoh1wmrcqx", "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}]}], "minHeight": 400, "widgetId": "xoh1wmrcqx", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 239.25, "topRow": 0, "bottomRow": 400, "parentId": "kkk9wh8jf3", "dynamicBindingPathList": []}], "type": "LIST_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "List", "key": "d5j4n0gsl6", "iconSVG": "/static/media/icon.9925ee17dee37bf1ba7374412563a8a7.svg", "isCanvas": true, "widgetId": "kkk9wh8jf3", "renderMode": "CANVAS", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "isLoading": false, "parentColumnSpace": 9.96875, "parentRowSpace": 10, "leftColumn": 20, "rightColumn": 44, "topRow": 1, "bottomRow": 41, "parentId": "0", "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}, {"key": "template.Image1.image"}, {"key": "template.Text1.text"}, {"key": "template.Text2.text"}], "privateWidgets": {"undefined": true}, "dynamicTriggerPathList": []}, {"isVisible": true, "text": "LabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabelLabel", "fontSize": "1rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "widgetName": "Text3", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "reqlgz3yka", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "widgetId": "8g9vzptfvr", "renderMode": "CANVAS", "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "parentColumnSpace": 9.96875, "parentRowSpace": 10, "leftColumn": 4, "rightColumn": 20, "topRow": 16, "bottomRow": 20, "parentId": "0", "dynamicBindingPathList": [{"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "shouldScrollContents": true}]}}