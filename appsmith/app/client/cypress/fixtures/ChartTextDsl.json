{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224, "snapColumns": 16, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 1292, "containerStyle": "none", "snapRows": 33, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "dynamicBindingPathList": [], "version": 4, "minHeight": 1292, "parentColumnSpace": 1, "leftColumn": 0, "children": [{"backgroundColor": "#FFFFFF", "widgetName": "Container1", "type": "CONTAINER_WIDGET", "containerStyle": "card", "isVisible": true, "isLoading": false, "parentColumnSpace": 75.25, "parentRowSpace": 38, "dynamicBindingPathList": [], "leftColumn": 0, "rightColumn": 8, "topRow": 0, "bottomRow": 9, "snapColumns": 16, "orientation": "VERTICAL", "children": [{"backgroundColor": "transparent", "widgetName": "kydabisaxj", "type": "CANVAS_WIDGET", "containerStyle": "none", "isVisible": true, "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 602, "topRow": 0, "bottomRow": 342, "snapColumns": 16, "orientation": "VERTICAL", "children": [{"isVisible": true, "widgetName": "Chart1", "chartType": "LINE_CHART", "chartName": "Sales on working days", "allowScroll": false, "chartData": {"some-random-id": {"seriesName": "Sales", "data": []}}, "xAxisName": "Last Week", "yAxisName": "Total Order Revenue $", "type": "CHART_WIDGET", "isLoading": false, "parentColumnSpace": 34.6875, "parentRowSpace": 38, "leftColumn": 5, "rightColumn": 13, "topRow": 0, "bottomRow": 8, "parentId": "56c5odk5ic", "widgetId": "64jukpgbzh", "dynamicBindingPathList": []}], "widgetId": "56c5odk5ic", "detachFromLayout": true, "canExtend": false}], "widgetId": "kzlk5ductp"}, {"backgroundColor": "#FFFFFF", "widgetName": "Container3", "type": "CONTAINER_WIDGET", "containerStyle": "card", "isVisible": true, "isLoading": false, "parentColumnSpace": 75.25, "parentRowSpace": 38, "dynamicBindingPathList": [], "leftColumn": 0, "rightColumn": 16, "topRow": 9, "bottomRow": 23, "snapColumns": 16, "orientation": "VERTICAL", "children": [{"backgroundColor": "transparent", "widgetName": "za6o0unktq", "type": "CANVAS_WIDGET", "containerStyle": "none", "isVisible": true, "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 1204, "topRow": 0, "bottomRow": 532, "snapColumns": 16, "orientation": "VERTICAL", "children": [{"isVisible": true, "text": "Label", "textStyle": "LABEL", "textAlign": "LEFT", "widgetName": "Text1", "type": "TEXT_WIDGET", "isLoading": false, "parentColumnSpace": 71.75, "parentRowSpace": 38, "leftColumn": 3, "rightColumn": 7, "topRow": 2, "bottomRow": 3, "parentId": "d2i9xsy2fk", "widgetId": "u210slvpsz", "dynamicBindingPathList": []}], "widgetId": "d2i9xsy2fk", "detachFromLayout": true, "canExtend": false}], "widgetId": "t2a7se9pxe"}, {"backgroundColor": "#FFFFFF", "widgetName": "Container4", "type": "CONTAINER_WIDGET", "containerStyle": "card", "isVisible": true, "isLoading": false, "parentColumnSpace": 75.25, "parentRowSpace": 38, "dynamicBindingPathList": [], "leftColumn": 8, "rightColumn": 16, "topRow": 0, "bottomRow": 9, "snapColumns": 16, "orientation": "VERTICAL", "children": [{"backgroundColor": "transparent", "widgetName": "cli9vgw4yj", "type": "CANVAS_WIDGET", "containerStyle": "none", "isVisible": true, "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 602, "topRow": 0, "bottomRow": 342, "snapColumns": 16, "orientation": "VERTICAL", "children": [], "widgetId": "qzniae78ab", "detachFromLayout": true, "canExtend": false}], "widgetId": "gtsbf2q08n"}]}}