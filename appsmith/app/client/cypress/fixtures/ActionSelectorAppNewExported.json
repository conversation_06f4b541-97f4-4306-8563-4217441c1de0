{"artifactJsonType": "APPLICATION", "clientSchemaVersion": 1.0, "serverSchemaVersion": 11.0, "exportedApplication": {"name": "ActionSelectorAppNew (2)", "isPublic": false, "pages": [{"id": "Page1", "isDefault": true}, {"id": "Page2", "isDefault": false}], "publishedPages": [{"id": "Page1", "isDefault": true}, {"id": "Page2", "isDefault": false}], "viewMode": false, "appIsExample": false, "unreadCommentThreads": 0.0, "clonedFromApplicationId": "6757fbf1747bef33b4a0a9e8", "unpublishedApplicationDetail": {"appPositioning": {"type": "FIXED"}, "navigationSetting": {}, "themeSetting": {"sizing": 1.0, "density": 1.0, "appMaxWidth": "LARGE"}}, "publishedApplicationDetail": {"appPositioning": {"type": "FIXED"}, "navigationSetting": {}, "themeSetting": {"sizing": 1.0, "density": 1.0, "appMaxWidth": "LARGE"}}, "color": "#FFEBFB", "icon": "alien", "slug": "actionselectorappnew-2", "unpublishedCustomJSLibs": [], "publishedCustomJSLibs": [], "evaluationVersion": 2.0, "applicationVersion": 2.0, "collapseInvisibleWidgets": true, "isManualUpdate": false, "isCommunityTemplate": false, "deleted": false}, "datasourceList": [], "customJSLibList": [], "pageList": [{"unpublishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 124.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 90.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"resetFormOnClick": false, "needsErrorInfo": false, "boxShadow": "none", "mobileBottomRow": 11.0, "widgetName": "Button1", "onClick": "{{showModal().catch(() => {\n  showAlert('Failure Callback', '');\n});}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "dynamicPropertyPathList": [], "topRow": 7.0, "bottomRow": 11.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "mobileRightColumn": 25.0, "animateLoading": true, "parentColumnSpace": 12.4375, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 9.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "kcco8g7h4l", "rightColumn": 25.0, "isDefaultClickDisabled": true, "widgetId": "6pyc3sxek8", "minWidth": 120.0, "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 7.0, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 9.0, "buttonVariant": "PRIMARY", "placement": "CENTER"}]}, "layoutOnLoadActions": [], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "publishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 124.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 90.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"resetFormOnClick": false, "needsErrorInfo": false, "boxShadow": "none", "mobileBottomRow": 11.0, "widgetName": "Button1", "onClick": "{{showModal().catch(() => {\n  showAlert('Failure Callback', '');\n});}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "dynamicPropertyPathList": [], "topRow": 7.0, "bottomRow": 11.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "mobileRightColumn": 25.0, "animateLoading": true, "parentColumnSpace": 12.4375, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 9.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "kcco8g7h4l", "rightColumn": 25.0, "isDefaultClickDisabled": true, "widgetId": "6pyc3sxek8", "minWidth": 120.0, "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 7.0, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 9.0, "buttonVariant": "PRIMARY", "placement": "CENTER"}]}, "layoutOnLoadActions": [], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "gitSyncId": "6757fca9747bef33b4a0a9fa_0a866ce4-b725-4867-b5cc-c721ff1671b1", "deleted": false}, {"unpublishedPage": {"name": "Page2", "slug": "page2", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 103.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 90.0, "minHeight": 1050.0, "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"resetFormOnClick": false, "needsErrorInfo": false, "boxShadow": "none", "mobileBottomRow": 17.0, "widgetName": "Button1", "onClick": "{{showAlert('main alert', '').then(() => {\n  showAlert('success alert', 'success');\n});}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "dynamicPropertyPathList": [], "topRow": 1.0, "bottomRow": 5.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "mobileRightColumn": 28.0, "animateLoading": true, "parentColumnSpace": 13.0625, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 12.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "06wp4fr6dl", "rightColumn": 28.0, "isDefaultClickDisabled": true, "widgetId": "kcjwx0c5mv", "minWidth": 120.0, "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 13.0, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 12.0, "buttonVariant": "PRIMARY", "placement": "CENTER"}]}, "layoutOnLoadActions": [], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page2", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "publishedPage": {"name": "Page2", "slug": "page2", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 103.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 90.0, "minHeight": 1050.0, "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"resetFormOnClick": false, "needsErrorInfo": false, "boxShadow": "none", "mobileBottomRow": 17.0, "widgetName": "Button1", "onClick": "{{showAlert('main alert', '').then(() => {\n  showAlert('success alert', 'success');\n});}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "dynamicPropertyPathList": [], "topRow": 1.0, "bottomRow": 5.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "mobileRightColumn": 28.0, "animateLoading": true, "parentColumnSpace": 13.0625, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 12.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "06wp4fr6dl", "rightColumn": 28.0, "isDefaultClickDisabled": true, "widgetId": "kcjwx0c5mv", "minWidth": 120.0, "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 13.0, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 12.0, "buttonVariant": "PRIMARY", "placement": "CENTER"}]}, "layoutOnLoadActions": [], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page2", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "gitSyncId": "6757fca9747bef33b4a0a9fa_d339d325-80e3-4ab3-a1b3-a202017a5499", "deleted": false}], "actionList": [], "actionCollectionList": [], "editModeTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}, "publishedTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}}