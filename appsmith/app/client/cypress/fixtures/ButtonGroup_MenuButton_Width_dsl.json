{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1160, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 680, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 54, "minHeight": 690, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"widgetName": "ButtonGroup1", "orientation": "horizontal", "rightColumn": 50, "isCanvas": false, "displayName": "Button Group", "iconSVG": "/static/media/icon.d6773218.svg", "widgetId": "t5l24fccio", "topRow": 15, "bottomRow": 19, "parentRowSpace": 10, "isVisible": true, "groupButtons": {"groupButton1": {"label": "Favorite", "iconName": "heart", "id": "groupButton1", "widgetId": "", "buttonColor": "#03B365", "buttonType": "SIMPLE", "placement": "CENTER", "isVisible": true, "isDisabled": false, "index": 0, "menuItems": {}}, "groupButton2": {"label": "Add", "iconName": "add", "id": "groupButton2", "buttonColor": "#03B365", "buttonType": "SIMPLE", "placement": "CENTER", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 1, "menuItems": {}}, "groupButton3": {"label": "More", "iconName": "more", "id": "groupButton3", "buttonType": "MENU", "placement": "CENTER", "buttonColor": "#03B365", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 2, "menuItems": {"menuItem1": {"label": "First Option", "backgroundColor": "#FFFFFF", "id": "menuItem1", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 0}, "menuItem2": {"label": "Second Option", "backgroundColor": "#FFFFFF", "id": "menuItem2", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 1}, "menuItem3": {"label": "Delete", "iconName": "trash", "iconColor": "#FFFFFF", "iconAlign": "right", "textColor": "#FFFFFF", "backgroundColor": "#DD4B34", "id": "menuItem3", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 2}}}}, "type": "BUTTON_GROUP_WIDGET", "version": 1, "hideCard": false, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "animateLoading": true, "parentColumnSpace": 11.9375, "leftColumn": 1, "buttonVariant": "PRIMARY", "key": "qxtmv7r8yb"}, {"widgetName": "ButtonGroup2", "orientation": "horizontal", "rightColumn": 25, "isCanvas": false, "displayName": "Button Group", "iconSVG": "/static/media/icon.d6773218.svg", "widgetId": "yxjq5sck7d", "topRow": 4, "bottomRow": 8, "parentRowSpace": 10, "isVisible": true, "groupButtons": {"groupButton1": {"label": "Favorite", "iconName": "heart", "id": "groupButton1", "widgetId": "", "buttonColor": "#03B365", "buttonType": "SIMPLE", "placement": "CENTER", "isVisible": true, "isDisabled": false, "index": 0, "menuItems": {}}, "groupButton2": {"label": "Add", "iconName": "add", "id": "groupButton2", "buttonColor": "#03B365", "buttonType": "SIMPLE", "placement": "CENTER", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 1, "menuItems": {}}, "groupButton3": {"label": "More", "iconName": "more", "id": "groupButton3", "buttonType": "MENU", "placement": "CENTER", "buttonColor": "#03B365", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 2, "menuItems": {"menuItem1": {"label": "First Option", "backgroundColor": "#FFFFFF", "id": "menuItem1", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 0}, "menuItem2": {"label": "Second Option", "backgroundColor": "#FFFFFF", "id": "menuItem2", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 1}, "menuItem3": {"label": "Delete", "iconName": "trash", "iconColor": "#FFFFFF", "iconAlign": "right", "textColor": "#FFFFFF", "backgroundColor": "#DD4B34", "id": "menuItem3", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 2}}}}, "type": "BUTTON_GROUP_WIDGET", "version": 1, "hideCard": false, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "animateLoading": true, "parentColumnSpace": 11.9375, "leftColumn": 1, "buttonVariant": "PRIMARY", "key": "qxtmv7r8yb"}, {"widgetName": "ButtonGroup3", "isCanvas": false, "displayName": "Button Group", "iconSVG": "/static/media/icon.d6773218.svg", "topRow": 29, "bottomRow": 55, "parentRowSpace": 10, "groupButtons": {"groupButton1": {"label": "Favorite", "iconName": "heart", "id": "groupButton1", "widgetId": "", "buttonColor": "#03B365", "buttonType": "SIMPLE", "placement": "CENTER", "isVisible": true, "isDisabled": false, "index": 0, "menuItems": {}}, "groupButton2": {"label": "Add", "iconName": "add", "id": "groupButton2", "buttonColor": "#03B365", "buttonType": "SIMPLE", "placement": "CENTER", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 1, "menuItems": {}}, "groupButton3": {"label": "More", "iconName": "more", "id": "groupButton3", "buttonType": "MENU", "placement": "CENTER", "buttonColor": "#03B365", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 2, "menuItems": {"menuItem1": {"label": "First Option", "backgroundColor": "#FFFFFF", "id": "menuItem1", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 0}, "menuItem2": {"label": "Second Option", "backgroundColor": "#FFFFFF", "id": "menuItem2", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 1}, "menuItem3": {"label": "Delete", "iconName": "trash", "iconColor": "#FFFFFF", "iconAlign": "right", "textColor": "#FFFFFF", "backgroundColor": "#DD4B34", "id": "menuItem3", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 2}}}}, "type": "BUTTON_GROUP_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 11.9375, "dynamicTriggerPathList": [], "leftColumn": 1, "dynamicBindingPathList": [], "key": "qxtmv7r8yb", "orientation": "horizontal", "rightColumn": 50, "widgetId": "mr048y04aq", "isVisible": true, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "buttonVariant": "PRIMARY"}]}}