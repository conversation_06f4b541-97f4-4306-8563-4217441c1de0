{"artifactJsonType": "APPLICATION", "clientSchemaVersion": 2.0, "serverSchemaVersion": 11.0, "exportedApplication": {"name": "Untitled application 1", "isPublic": false, "pages": [{"id": "Page1", "isDefault": true}], "publishedPages": [{"id": "Page1", "isDefault": true}], "viewMode": false, "appIsExample": false, "unreadCommentThreads": 0.0, "unpublishedApplicationDetail": {"appPositioning": {"type": "FIXED"}, "navigationSetting": {}, "themeSetting": {"sizing": 1.0, "density": 1.0, "appMaxWidth": "LARGE"}}, "publishedApplicationDetail": {"appPositioning": {"type": "FIXED"}, "navigationSetting": {}, "themeSetting": {"sizing": 1.0, "density": 1.0, "appMaxWidth": "LARGE"}}, "color": "#C7F3E3", "icon": "bicycle", "slug": "untitled-application-1", "unpublishedCustomJSLibs": [], "publishedCustomJSLibs": [], "evaluationVersion": 2.0, "applicationVersion": 2.0, "collapseInvisibleWidgets": true, "isManualUpdate": false, "deleted": false}, "datasourceList": [], "customJSLibList": [], "pageList": [{"unpublishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 127.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 91.0, "minHeight": 1292.0, "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"boxShadow": "none", "widgetName": "Input1", "topRow": 0.0, "bottomRow": 4.0, "parentRowSpace": 40.0, "type": "INPUT_WIDGET_V2", "parentColumnSpace": 74.0, "dynamicTriggerPathList": [], "leftColumn": 4.0, "dynamicBindingPathList": [{"key": "accentColor"}], "inputType": "TEXT", "labelTextSize": "0.875rem", "rightColumn": 24.0, "dynamicHeight": "FIXED", "widgetId": "ftl8h620qf", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "", "version": 1.0, "parentId": "0", "isLoading": false, "borderRadius": "0px", "maxDynamicHeight": 9000.0, "defaultText": "3", "minDynamicHeight": 4.0}, {"labelTextSize": "0.875rem", "boxShadow": "none", "widgetName": "Input2", "rightColumn": 48.0, "dynamicHeight": "FIXED", "widgetId": "p2oen3muq5", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "topRow": 4.0, "bottomRow": 8.0, "parentRowSpace": 40.0, "isVisible": true, "label": "", "type": "INPUT_WIDGET_V2", "version": 1.0, "parentId": "0", "isLoading": false, "parentColumnSpace": 74.0, "leftColumn": 28.0, "dynamicBindingPathList": [{"key": "accentColor"}], "borderRadius": "0px", "maxDynamicHeight": 9000.0, "inputType": "TEXT", "minDynamicHeight": 4.0}, {"boxShadow": "none", "widgetName": "Input3", "topRow": 8.0, "bottomRow": 12.0, "parentRowSpace": 40.0, "type": "INPUT_WIDGET_V2", "parentColumnSpace": 74.0, "dynamicTriggerPathList": [], "leftColumn": 4.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "defaultText"}], "inputType": "TEXT", "labelTextSize": "0.875rem", "rightColumn": 24.0, "dynamicHeight": "FIXED", "widgetId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "", "version": 1.0, "parentId": "0", "isLoading": false, "borderRadius": "0px", "maxDynamicHeight": 9000.0, "defaultText": "{{PageLoadApi.data[1].id}}{{Input1.text}}", "minDynamicHeight": 4.0}]}, "layoutOnLoadActions": [[{"id": "Page1_PageLoadApi", "name": "PageLoadApi", "confirmBeforeExecute": false, "pluginType": "API", "jsonPathKeys": [], "timeoutInMillisecond": 10000.0}]], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "publishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 127.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 91.0, "minHeight": 1292.0, "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"boxShadow": "none", "widgetName": "Input1", "topRow": 0.0, "bottomRow": 4.0, "parentRowSpace": 40.0, "type": "INPUT_WIDGET_V2", "parentColumnSpace": 74.0, "dynamicTriggerPathList": [], "leftColumn": 4.0, "dynamicBindingPathList": [{"key": "accentColor"}], "inputType": "TEXT", "labelTextSize": "0.875rem", "rightColumn": 24.0, "dynamicHeight": "FIXED", "widgetId": "ftl8h620qf", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "", "version": 1.0, "parentId": "0", "isLoading": false, "borderRadius": "0px", "maxDynamicHeight": 9000.0, "defaultText": "3", "minDynamicHeight": 4.0}, {"labelTextSize": "0.875rem", "boxShadow": "none", "widgetName": "Input2", "rightColumn": 48.0, "dynamicHeight": "FIXED", "widgetId": "p2oen3muq5", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "topRow": 4.0, "bottomRow": 8.0, "parentRowSpace": 40.0, "isVisible": true, "label": "", "type": "INPUT_WIDGET_V2", "version": 1.0, "parentId": "0", "isLoading": false, "parentColumnSpace": 74.0, "leftColumn": 28.0, "dynamicBindingPathList": [{"key": "accentColor"}], "borderRadius": "0px", "maxDynamicHeight": 9000.0, "inputType": "TEXT", "minDynamicHeight": 4.0}, {"boxShadow": "none", "widgetName": "Input3", "topRow": 8.0, "bottomRow": 12.0, "parentRowSpace": 40.0, "type": "INPUT_WIDGET_V2", "parentColumnSpace": 74.0, "dynamicTriggerPathList": [], "leftColumn": 4.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "defaultText"}], "inputType": "TEXT", "labelTextSize": "0.875rem", "rightColumn": 24.0, "dynamicHeight": "FIXED", "widgetId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "", "version": 1.0, "parentId": "0", "isLoading": false, "borderRadius": "0px", "maxDynamicHeight": 9000.0, "defaultText": "{{PageLoadApi.data[1].id}}{{Input1.text}}", "minDynamicHeight": 4.0}]}, "layoutOnLoadActions": [[{"id": "Page1_PageLoadApi", "name": "PageLoadApi", "confirmBeforeExecute": false, "pluginType": "API", "jsonPathKeys": [], "timeoutInMillisecond": 10000.0}]], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "gitSyncId": "67a0b43bc732007ab0b1b269_ece4c61d-cb2b-4aef-a459-6f32e7d0eea3", "deleted": false}], "actionList": [{"pluginType": "API", "pluginId": "restapi-plugin", "unpublishedAction": {"name": "PageLoadApi", "datasource": {"name": "DEFAULT_REST_DATASOURCE", "pluginId": "restapi-plugin", "datasourceConfiguration": {"url": "http://host.docker.internal:5001"}, "invalids": [], "messages": [], "isAutoGenerated": false, "deleted": false, "policyMap": {}, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "path": "/v1/mock-api", "headers": [], "autoGeneratedHeaders": [], "encodeParamsToggle": true, "queryParameters": [{"key": "records", "value": "10"}], "body": "", "bodyFormData": [], "httpMethod": "GET", "httpVersion": "HTTP11", "selfReferencingDataPaths": [], "pluginSpecifiedTemplates": [{"value": true}], "formData": {"apiContentType": "none"}}, "runBehaviour": "ON_PAGE_LOAD", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": true, "confirmBeforeExecute": false, "policyMap": {}, "userPermissions": [], "createdAt": "2025-02-03T12:19:26Z"}, "publishedAction": {"name": "PageLoadApi", "datasource": {"name": "DEFAULT_REST_DATASOURCE", "pluginId": "restapi-plugin", "datasourceConfiguration": {"url": "http://host.docker.internal:5001"}, "invalids": [], "messages": [], "isAutoGenerated": false, "deleted": false, "policyMap": {}, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "path": "/v1/mock-api", "headers": [], "autoGeneratedHeaders": [], "encodeParamsToggle": true, "queryParameters": [{"key": "records", "value": "10"}], "body": "", "bodyFormData": [], "httpMethod": "GET", "httpVersion": "HTTP11", "selfReferencingDataPaths": [], "pluginSpecifiedTemplates": [{"value": true}], "formData": {"apiContentType": "none"}}, "runBehaviour": "ON_PAGE_LOAD", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": true, "confirmBeforeExecute": false, "policyMap": {}, "userPermissions": [], "createdAt": "2025-02-03T12:19:26Z"}, "gitSyncId": "67a0b43bc732007ab0b1b269_cda7f68f-d56e-49ae-a625-a3058b33535e", "id": "Page1_PageLoadApi", "deleted": false}], "actionCollectionList": [], "editModeTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}, "publishedTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}}