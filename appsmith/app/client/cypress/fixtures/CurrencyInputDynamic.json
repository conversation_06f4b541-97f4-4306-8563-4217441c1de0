{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1296, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 470, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 56, "minHeight": 480, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"isVisible": true, "label": "", "widgetName": "CurrencyInput1", "version": 1, "defaultText": "", "iconAlign": "left", "autoFocus": false, "labelStyle": "", "resetOnSubmit": true, "isRequired": false, "isDisabled": false, "animateLoading": true, "allowCurrencyChange": false, "defaultCurrencyCode": "{{appsmith.store.test}}", "decimals": 0, "type": "CURRENCY_INPUT_WIDGET", "hideCard": false, "displayName": "Currency Input", "key": "771ggs866e", "iconSVG": "/static/media/icon.01a1e03d.svg", "widgetId": "ny5fdwq1gi", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 20.0625, "parentRowSpace": 10, "leftColumn": 8, "rightColumn": 28, "topRow": 35, "bottomRow": 39, "parentId": "0", "dynamicPropertyPathList": [{"key": "defaultCurrencyCode"}], "dynamicBindingPathList": [{"key": "defaultCurrencyCode"}], "dynamicTriggerPathList": []}, {"isVisible": true, "text": "{{appsmith.store.test}}:{{CurrencyInput1.countryCode}}:{{CurrencyInput1.currencyCode}}", "fontSize": "PARAGRAPH", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "truncateButtonColor": "#FFC13D", "widgetName": "Text1", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "type": "TEXT_WIDGET", "hideCard": false, "displayName": "Text", "key": "knz3ywvzqn", "iconSVG": "/static/media/icon.97c59b52.svg", "widgetId": "0z42jje4qe", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 20.0625, "parentRowSpace": 10, "leftColumn": 32, "rightColumn": 48, "topRow": 35, "bottomRow": 39, "parentId": "0", "dynamicBindingPathList": [{"key": "text"}], "dynamicTriggerPathList": []}, {"isVisible": true, "placeholderText": "Select option", "labelText": "", "options": "[\n  {\n    \"label\": \"US\",\n    \"value\": \"USD\"\n  },\n  {\n    \"label\": \"India\",\n    \"value\": \"INR\"\n  }\n]", "serverSideFiltering": false, "widgetName": "Select1", "defaultOptionValue": "GREEN", "version": 1, "isFilterable": true, "isRequired": false, "isDisabled": false, "animateLoading": true, "type": "SELECT_WIDGET", "hideCard": false, "displayName": "Select", "key": "r1pq9vxrvi", "iconSVG": "/static/media/icon.bd99caba.svg", "widgetId": "0rhkmv6yvt", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 20.0625, "parentRowSpace": 10, "leftColumn": 32, "rightColumn": 52, "topRow": 12, "bottomRow": 16, "parentId": "0", "dynamicBindingPathList": [], "dynamicTriggerPathList": [{"key": "onOptionChange"}], "onOptionChange": "{{storeValue('test',Select1.selectedOptionValue)}}"}]}}