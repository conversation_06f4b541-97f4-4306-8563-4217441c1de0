{"clientSchemaVersion": 1.0, "serverSchemaVersion": 6.0, "exportedApplication": {"name": "ImportAppSelectedAccess", "isPublic": false, "pages": [{"id": "Page1", "isDefault": true}], "publishedPages": [{"id": "Page1", "isDefault": true}], "viewMode": false, "appIsExample": false, "unreadCommentThreads": 0.0, "color": "#CCCCCC", "icon": "call", "slug": "importappselectedaccess", "unpublishedCustomJSLibs": [], "publishedCustomJSLibs": [], "evaluationVersion": 2.0, "applicationVersion": 2.0, "collapseInvisibleWidgets": true, "isManualUpdate": false, "deleted": false}, "datasourceList": [{"name": "gsheet-selected", "pluginId": "google-sheets-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "gitSyncId": "6507db87f847163dfe7777c4_6507dc29f847163dfe7777d1"}], "customJSLibList": [], "pageList": [{"unpublishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 380.0, "containerStyle": "none", "snapRows": 124.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 86.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "borderColor": "#E0DEDE", "isVisibleDownload": true, "iconSVG": "/static/media/icon.e6911f8bb94dc6c4a102a74740c41763.svg", "topRow": 0.0, "isSortable": true, "type": "TABLE_WIDGET_V2", "inlineEditingSaveOption": "ROW_LEVEL", "animateLoading": true, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}, {"key": "tableData"}, {"key": "primaryColumns.uniq_id.computedValue"}, {"key": "primaryColumns.japanese_name.computedValue"}, {"key": "primaryColumns.currencies.computedValue"}, {"key": "primaryColumns.specialChars.computedValue"}, {"key": "primaryColumns.product_name.computedValue"}, {"key": "primaryColumns.manufacturer.computedValue"}, {"key": "primaryColumns.price.computedValue"}, {"key": "primaryColumns.number_available_in_stock.computedValue"}, {"key": "primaryColumns.number_of_reviews.computedValue"}, {"key": "primaryColumns.number_of_answered_questions.computedValue"}, {"key": "primaryColumns.average_review_rating.computedValue"}, {"key": "primaryColumns.amazon_category_and_sub_category.computedValue"}, {"key": "primaryColumns.customers_who_bought_this_item_also_bought.computedValue"}, {"key": "primaryColumns.rowIndex.computedValue"}], "needsHeightForContent": true, "leftColumn": 0.0, "delimiter": ",", "defaultSelectedRowIndex": 0.0, "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisibleFilters": true, "isVisible": true, "enableClientSideSearch": true, "version": 2.0, "totalRecordsCount": 0.0, "tags": ["Suggested", "Display"], "isLoading": false, "childStylesheet": {"button": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "menuButton": {"menuColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "iconButton": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "editActions": {"saveButtonColor": "{{appsmith.theme.colors.primaryColor}}", "saveBorderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "discardButtonColor": "{{appsmith.theme.colors.primaryColor}}", "discardBorderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}}, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "columnUpdatedAt": 1695963199756.0, "defaultSelectedRowIndices": [0.0], "mobileBottomRow": 29.0, "widgetName": "Table1", "defaultPageSize": 0.0, "columnOrder": ["uniq_id", "japanese_name", "currencies", "specialChars", "product_name", "manufacturer", "price", "number_available_in_stock", "number_of_reviews", "number_of_answered_questions", "average_review_rating", "amazon_category_and_sub_category", "customers_who_bought_this_item_also_bought", "rowIndex"], "dynamicPropertyPathList": [], "displayName": "Table", "bottomRow": 28.0, "columnWidthMap": {}, "parentRowSpace": 10.0, "hideCard": false, "mobileRightColumn": 34.0, "parentColumnSpace": 16.65625, "dynamicTriggerPathList": [], "borderWidth": "1", "primaryColumns": {"uniq_id": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 0.0, "width": 150.0, "originalId": "uniq_id", "id": "uniq_id", "alias": "uniq_id", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "uniq_id", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"uniq_id\"]))}}", "sticky": "", "validation": {}}, "japanese_name": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 1.0, "width": 150.0, "originalId": "japanese_name", "id": "japanese_name", "alias": "japanese_name", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "japanese_name", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"japanese_name\"]))}}", "sticky": "", "validation": {}}, "currencies": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 2.0, "width": 150.0, "originalId": "currencies", "id": "currencies", "alias": "currencies", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "currencies", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"currencies\"]))}}", "sticky": "", "validation": {}}, "specialChars": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 3.0, "width": 150.0, "originalId": "specialChars", "id": "specialChars", "alias": "specialChars", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "specialChars", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"specialChars\"]))}}", "sticky": "", "validation": {}}, "product_name": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 4.0, "width": 150.0, "originalId": "product_name", "id": "product_name", "alias": "product_name", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "product_name", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"product_name\"]))}}", "sticky": "", "validation": {}}, "manufacturer": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 5.0, "width": 150.0, "originalId": "manufacturer", "id": "manufacturer", "alias": "manufacturer", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "manufacturer", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"manufacturer\"]))}}", "sticky": "", "validation": {}}, "price": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 6.0, "width": 150.0, "originalId": "price", "id": "price", "alias": "price", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "price", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"price\"]))}}", "sticky": "", "validation": {}}, "number_available_in_stock": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 7.0, "width": 150.0, "originalId": "number_available_in_stock", "id": "number_available_in_stock", "alias": "number_available_in_stock", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "number_available_in_stock", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"number_available_in_stock\"]))}}", "sticky": "", "validation": {}}, "number_of_reviews": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 8.0, "width": 150.0, "originalId": "number_of_reviews", "id": "number_of_reviews", "alias": "number_of_reviews", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "number_of_reviews", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"number_of_reviews\"]))}}", "sticky": "", "validation": {}}, "number_of_answered_questions": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 9.0, "width": 150.0, "originalId": "number_of_answered_questions", "id": "number_of_answered_questions", "alias": "number_of_answered_questions", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "number_of_answered_questions", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"number_of_answered_questions\"]))}}", "sticky": "", "validation": {}}, "average_review_rating": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 10.0, "width": 150.0, "originalId": "average_review_rating", "id": "average_review_rating", "alias": "average_review_rating", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "average_review_rating", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"average_review_rating\"]))}}", "sticky": "", "validation": {}}, "amazon_category_and_sub_category": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 11.0, "width": 150.0, "originalId": "amazon_category_and_sub_category", "id": "amazon_category_and_sub_category", "alias": "amazon_category_and_sub_category", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "amazon_category_and_sub_category", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"amazon_category_and_sub_category\"]))}}", "sticky": "", "validation": {}}, "customers_who_bought_this_item_also_bought": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 12.0, "width": 150.0, "originalId": "customers_who_bought_this_item_also_bought", "id": "customers_who_bought_this_item_also_bought", "alias": "customers_who_bought_this_item_also_bought", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "customers_who_bought_this_item_also_bought", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"customers_who_bought_this_item_also_bought\"]))}}", "sticky": "", "validation": {}}, "rowIndex": {"allowCellWrapping": false, "allowSameOptionsInNewRow": true, "index": 13.0, "width": 150.0, "originalId": "rowIndex", "id": "rowIndex", "alias": "rowIndex", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "rowIndex", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"rowIndex\"]))}}", "sticky": "", "validation": {}}}, "key": "y3e8fye419", "canFreezeColumn": true, "isDeprecated": false, "rightColumn": 34.0, "textSize": "0.875rem", "widgetId": "5w278g8zo9", "minWidth": 450.0, "tableData": "{{fetch_many_selected_access.data}}", "label": "Data", "searchKey": "", "parentId": "0", "renderMode": "CANVAS", "mobileTopRow": 1.0, "horizontalAlignment": "LEFT", "isVisibleSearch": true, "responsiveBehavior": "fill", "mobileLeftColumn": 0.0, "isVisiblePagination": true, "verticalAlignment": "CENTER"}]}, "layoutOnLoadActions": [[{"id": "Page1_fetch_many_selected_access", "name": "fetch_many_selected_access", "confirmBeforeExecute": false, "pluginType": "SAAS", "jsonPathKeys": [], "timeoutInMillisecond": 10000.0}]], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policies": []}, "publishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224.0, "snapColumns": 16.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1250.0, "containerStyle": "none", "snapRows": 33.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 4.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": []}, "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policies": []}, "deleted": false, "gitSyncId": "65164f37b43d9217d781a524_65164f37b43d9217d781a526"}], "actionList": [{"pluginType": "SAAS", "pluginId": "google-sheets-plugin", "unpublishedAction": {"name": "fetch_many_selected_access", "datasource": {"name": "gsheet-selected", "pluginId": "google-sheets-plugin", "messages": [], "isAutoGenerated": false, "id": "gsheet-selected", "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "encodeParamsToggle": true, "selfReferencingDataPaths": [], "formData": {"command": {"data": "FETCH_MANY"}, "entityType": {"data": "ROWS"}, "tableHeaderIndex": {"data": "1"}, "projection": {"data": []}, "queryFormat": {"data": "ROWS"}, "range": {"data": ""}, "where": {"data": {"condition": "AND", "children": [{"condition": "LT"}]}}, "pagination": {"data": {"limit": "20", "offset": "0"}}, "smartSubstitution": {"data": true}, "sheetUrl": {"data": "https://docs.google.com/spreadsheets/d/1rD1Bo4x-y9B7EqaVs8HuJViGSEVBeiLPuTdxMoC2uDw/edit"}, "sheetName": {"data": "Sheet1"}, "sortBy": {"data": [{"column": "", "order": "Ascending"}]}}}, "runBehaviour": "ON_PAGE_LOAD", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": [], "createdAt": "2023-09-29T04:59:00Z"}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_fetch_many_selected_access", "deleted": false, "gitSyncId": "65164f37b43d9217d781a524_65164f5db43d9217d781a52b"}], "actionCollectionList": [], "updatedResources": {"customJSLibList": [], "actionList": ["fetch_many_selected_access##ENTITY_SEPARATOR##Page1"], "pageList": ["Page1"], "actionCollectionList": []}, "editModeTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}, "publishedTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}}