{"artifactJsonType": "APPLICATION", "clientSchemaVersion": 1.0, "serverSchemaVersion": 11.0, "exportedApplication": {"name": "Untitled application 2", "isPublic": false, "pages": [{"id": "Page1", "isDefault": true}], "publishedPages": [{"id": "Page1", "isDefault": true}], "viewMode": false, "appIsExample": false, "unreadCommentThreads": 0.0, "unpublishedApplicationDetail": {"appPositioning": {"type": "FIXED"}, "navigationSetting": {}, "themeSetting": {"sizing": 1.0, "density": 1.0, "appMaxWidth": "LARGE"}}, "publishedApplicationDetail": {"appPositioning": {"type": "FIXED"}, "navigationSetting": {}, "themeSetting": {"sizing": 1.0, "density": 1.0, "appMaxWidth": "LARGE"}}, "color": "#D9E7FF", "icon": "uk-pounds", "slug": "untitled-application-2", "unpublishedCustomJSLibs": [], "publishedCustomJSLibs": [], "evaluationVersion": 2.0, "applicationVersion": 2.0, "collapseInvisibleWidgets": true, "isManualUpdate": false, "deleted": false}, "datasourceList": [], "customJSLibList": [], "pageList": [{"unpublishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 690.0, "containerStyle": "none", "snapRows": 124.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 90.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"needsErrorInfo": false, "boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "mobileBottomRow": 39.0, "widgetName": "Iframe1", "dynamicPropertyPathList": [], "srcDoc": "<!DOCTYPE html>\n              <html lang=\"en\">\n              <head>\n              </head>\n              <body>\n                      <label class=\"custom-field one\" id=\"test\">\n                        <input type=\"text\" placeholder=\"getMsg\"/>\n                      </label>\n\n                      <script>\n                        const input = document.querySelector('input');\n                        input.addEventListener('change', (e) => {\n                          window.parent.postMessage(e.target.value, \"*\");\n                        });\n\n                        window.addEventListener('message', (e) => {\n                          input.value = e.data;\n                        });\n                      </script>\n              </body>\n            </html>", "topRow": 7.0, "bottomRow": 39.0, "parentRowSpace": 10.0, "source": "", "type": "IFRAME_WIDGET", "mobileRightColumn": 52.0, "borderOpacity": 100.0, "animateLoading": true, "parentColumnSpace": 13.0625, "dynamicTriggerPathList": [{"key": "onMessageReceived"}, {"key": "onSrcDocChanged"}, {"key": "onURLChanged"}], "leftColumn": 29.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "borderWidth": 1.0, "flexVerticalAlignment": "start", "key": "fueiozx7gr", "rightColumn": 53.0, "widgetId": "6i2a7e908e", "isVisible": true, "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 7.0, "onURLChanged": "{{showAlert('url updated', '');}}", "responsiveBehavior": "fill", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 28.0, "onMessageReceived": "{{storeValue('text', Iframe1.message);\nshowAlert('Hey Iframe Called.', '');}}", "onSrcDocChanged": "{{showAlert('src updated', '');}}"}, {"resetFormOnClick": false, "needsErrorInfo": false, "boxShadow": "none", "mobileBottomRow": 31.0, "widgetName": "Button1", "onClick": "{{postWindowMessage('submitclicked', \"Iframe1\", \"*\");}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "topRow": 27.0, "bottomRow": 31.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "mobileRightColumn": 14.0, "animateLoading": true, "parentColumnSpace": 13.0625, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "co05qm838o", "rightColumn": 14.0, "isDefaultClickDisabled": true, "widgetId": "9<PERSON><PERSON>rky4", "minWidth": 120.0, "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 27.0, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 0.0, "buttonVariant": "PRIMARY", "placement": "CENTER"}, {"needsErrorInfo": false, "boxShadow": "none", "mobileBottomRow": 16.0, "widgetName": "Input1", "topRow": 9.0, "bottomRow": 16.0, "parentRowSpace": 10.0, "labelWidth": 5.0, "autoFocus": false, "type": "INPUT_WIDGET_V2", "mobileRightColumn": 19.0, "animateLoading": true, "parentColumnSpace": 13.0625, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}, {"key": "defaultText"}], "labelPosition": "Top", "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "rionsri36t", "labelTextSize": "0.875rem", "isRequired": false, "rightColumn": 19.0, "dynamicHeight": "FIXED", "widgetId": "yd1qq15u3x", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "showStepArrows": false, "minWidth": 450.0, "isVisible": true, "label": "Label", "version": 2.0, "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 9.0, "responsiveBehavior": "fill", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 0.0, "maxDynamicHeight": 9000.0, "iconAlign": "left", "defaultText": "{{appsmith.store.text}}", "minDynamicHeight": 4.0}, {"needsErrorInfo": false, "boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "mobileBottomRow": 69.0, "widgetName": "Iframe2", "topRow": 37.0, "bottomRow": 69.0, "parentRowSpace": 10.0, "source": "https://docs.appsmith.com/", "type": "IFRAME_WIDGET", "mobileRightColumn": 29.0, "borderOpacity": 100.0, "animateLoading": true, "parentColumnSpace": 13.0625, "dynamicTriggerPathList": [], "leftColumn": 5.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "borderWidth": 1.0, "flexVerticalAlignment": "start", "key": "e6wucjdesu", "rightColumn": 29.0, "widgetId": "bapiv4pebd", "isVisible": true, "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 37.0, "responsiveBehavior": "fill", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 5.0}]}, "layoutOnLoadActions": [], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "publishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224.0, "snapColumns": 16.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1250.0, "containerStyle": "none", "snapRows": 33.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 4.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": []}, "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policyMap": {}}, "gitSyncId": "6752892a7db55e68232d2529_b5478fdf-4849-44e9-b4c1-152d6ec1bd9f", "deleted": false}], "actionList": [], "actionCollectionList": [], "editModeTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}, "publishedTheme": {"name": "Default-New", "displayName": "Modern", "isSystemTheme": true, "deleted": false}}