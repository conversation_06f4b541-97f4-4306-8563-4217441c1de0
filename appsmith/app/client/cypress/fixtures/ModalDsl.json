{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224, "snapColumns": 16, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 1280, "containerStyle": "none", "snapRows": 33, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 10, "minHeight": 1292, "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"size": "MODAL_SMALL", "canEscapeKeyClose": true, "detachFromLayout": true, "canOutsideClickClose": true, "shouldScrollContents": true, "widgetName": "Modal1", "children": [{"isVisible": true, "widgetName": "Canvas1", "detachFromLayout": true, "canExtend": true, "isDisabled": false, "shouldScrollContents": false, "children": [{"isVisible": true, "widgetName": "Icon1", "iconName": "cross", "iconSize": 24, "color": "#040627", "type": "ICON_WIDGET", "isLoading": false, "leftColumn": 14, "rightColumn": 16, "topRow": 0, "bottomRow": 1, "parentId": "yyyrxs383y", "widgetId": "kxdvolusyp", "onClick": "{{closeModal(Modal1.name)}}"}, {"isVisible": true, "text": "Modal Title", "textStyle": "HEADING", "textAlign": "LEFT", "widgetName": "Text1", "type": "TEXT_WIDGET", "isLoading": false, "leftColumn": 0, "rightColumn": 10, "topRow": 0, "bottomRow": 1, "parentId": "yyyrxs383y", "widgetId": "3fugqdtg8g"}, {"isVisible": true, "text": "Cancel", "buttonStyle": "SECONDARY_BUTTON", "widgetName": "Button1", "isDisabled": false, "isDefaultClickDisabled": true, "type": "BUTTON_WIDGET", "isLoading": false, "leftColumn": 9, "rightColumn": 12, "topRow": 4, "bottomRow": 5, "parentId": "yyyrxs383y", "widgetId": "6cjfbnjfa4"}, {"isVisible": true, "text": "Confirm", "buttonStyle": "PRIMARY_BUTTON", "widgetName": "Button2", "isDisabled": false, "isDefaultClickDisabled": true, "type": "BUTTON_WIDGET", "isLoading": false, "leftColumn": 12, "rightColumn": 16, "topRow": 4, "bottomRow": 5, "parentId": "yyyrxs383y", "widgetId": "3tmnukkm7m"}], "minHeight": 240, "type": "CANVAS_WIDGET", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 444, "topRow": 0, "bottomRow": 240, "parentId": "287u6pannr", "widgetId": "yyyrxs383y"}], "type": "MODAL_WIDGET", "isLoading": false, "parentColumnSpace": 74, "parentRowSpace": 40, "leftColumn": 5, "rightColumn": 11, "topRow": 15, "bottomRow": 21, "parentId": "0", "widgetId": "287u6pannr"}]}}