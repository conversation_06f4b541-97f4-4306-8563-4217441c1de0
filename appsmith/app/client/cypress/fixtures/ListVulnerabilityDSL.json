{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1130.0, "containerStyle": "none", "snapRows": 125.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 60.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"template": {"Text1": {"isVisible": true, "text": "{{List1.listData.map((currentItem, currentIndex) => {\n              return (function(){\n                return  'UserName: ' + currentItem.user_name;\n              })();\n            })}}", "fontSize": "1rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "truncateButtonColor": "#FFC13D", "widgetName": "Text1", "shouldTruncate": false, "overflow": "NONE", "version": 1.0, "animateLoading": true, "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "x4kskf46w4", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "widgetId": "7nv3vp3rwg", "renderMode": "CANVAS", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "parentColumnSpace": 10.173828125, "parentRowSpace": 10.0, "leftColumn": 0.0, "rightColumn": 16.0, "topRow": 0.0, "bottomRow": 4.0, "parentId": "iafd4c74uv", "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}], "logBlackList": {"isVisible": true, "text": true, "fontSize": true, "fontStyle": true, "textAlign": true, "textColor": true, "truncateButtonColor": true, "widgetName": true, "shouldTruncate": true, "overflow": true, "version": true, "animateLoading": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "minHeight": true, "widgetId": true, "renderMode": true, "fontFamily": true, "borderRadius": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true, "dynamicBindingPathList": true}}, "Button2": {"isVisible": true, "animateLoading": true, "text": "{{List1.listData.map((currentItem, currentIndex) => {\n              return (function(){\n                return  'Detail';\n              })();\n            })}}", "buttonVariant": "PRIMARY", "placement": "CENTER", "widgetName": "Button2", "isDisabled": false, "isDefaultClickDisabled": true, "disabledWhenInvalid": false, "resetFormOnClick": false, "recaptchaType": "V3", "version": 1.0, "searchTags": ["click", "submit"], "type": "BUTTON_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "5x7gbvr1uj", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "widgetId": "d72jq60zds", "renderMode": "CANVAS", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 10.173828125, "parentRowSpace": 10.0, "leftColumn": 47.0, "rightColumn": 63.0, "topRow": 1.0, "bottomRow": 5.0, "parentId": "iafd4c74uv", "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "logBlackList": {"isVisible": true, "animateLoading": true, "text": true, "buttonVariant": true, "placement": true, "widgetName": true, "isDisabled": true, "isDefaultClickDisabled": true, "disabledWhenInvalid": true, "resetFormOnClick": true, "recaptchaType": true, "version": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "minHeight": true, "widgetId": true, "renderMode": true, "buttonColor": true, "borderRadius": true, "boxShadow": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true, "dynamicBindingPathList": true}, "onClick": "{{showAlert(currentItem.user_name)}}"}}, "boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "List1", "listData": "{{[\n\t{\n\t\t\"id\": Input3.text,\n\t\t\"user_name\": Input1.text,\n\t\t\"desc\": Input2.text\n\t},{\n\t\t\"id\": 2,\n\t\t\"user_name\": \"demo user name\",\n\t\t\"desc\": \"hello\"\n\t}\n]}}", "isCanvas": true, "displayName": "List", "iconSVG": "/static/media/icon.9925ee17dee37bf1ba7374412563a8a7.svg", "topRow": 39.0, "bottomRow": 103.0, "parentRowSpace": 10.0, "type": "LIST_WIDGET", "hideCard": false, "gridGap": 0.0, "animateLoading": true, "parentColumnSpace": 30.0625, "dynamicTriggerPathList": [{"key": "template.Button2.onClick"}], "leftColumn": 1.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}, {"key": "listData"}, {"key": "template.Text1.text"}, {"key": "template.Button2.text"}], "gridType": "vertical", "enhancements": true, "children": [{"boxShadow": "none", "widgetName": "Canvas1", "displayName": "<PERSON><PERSON>", "topRow": 0.0, "bottomRow": 390.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "dropDisabled": true, "openParentPropertyPane": true, "minHeight": 400.0, "noPad": true, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "accentColor"}], "children": [{"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "Container1", "borderColor": "transparent", "disallowCopy": true, "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "searchTags": ["div", "parent", "group"], "topRow": 0.0, "bottomRow": 8.0, "dragDisabled": true, "type": "CONTAINER_WIDGET", "hideCard": false, "openParentPropertyPane": true, "isDeletable": false, "animateLoading": true, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"boxShadow": "none", "widgetName": "Canvas2", "displayName": "<PERSON><PERSON>", "topRow": 0.0, "bottomRow": 370.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "accentColor"}], "children": [{"widgetName": "Text1", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 0.0, "bottomRow": 4.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 10.173828125, "dynamicTriggerPathList": [], "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}, {"key": "text"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "UserName: {{currentItem.user_name}}", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 41.0, "textAlign": "LEFT", "widgetId": "7nv3vp3rwg", "logBlackList": {"isVisible": true, "text": true, "fontSize": true, "fontStyle": true, "textAlign": true, "textColor": true, "truncateButtonColor": true, "widgetName": true, "shouldTruncate": true, "overflow": true, "version": true, "animateLoading": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "minHeight": true, "widgetId": true, "renderMode": true, "fontFamily": true, "borderRadius": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true, "dynamicBindingPathList": true}, "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "iafd4c74uv", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1rem"}, {"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button2", "onClick": "{{showAlert(currentItem.user_name)}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 0.0, "bottomRow": 4.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 10.173828125, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 47.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Detail", "isDisabled": false, "key": "5x7gbvr1uj", "isDeprecated": false, "rightColumn": 63.0, "isDefaultClickDisabled": true, "widgetId": "d72jq60zds", "logBlackList": {"isVisible": true, "animateLoading": true, "text": true, "buttonVariant": true, "placement": true, "widgetName": true, "isDisabled": true, "isDefaultClickDisabled": true, "disabledWhenInvalid": true, "resetFormOnClick": true, "recaptchaType": true, "version": true, "searchTags": true, "type": true, "hideCard": true, "isDeprecated": true, "replacement": true, "displayName": true, "key": true, "iconSVG": true, "isCanvas": true, "minHeight": true, "widgetId": true, "renderMode": true, "buttonColor": true, "borderRadius": true, "boxShadow": true, "isLoading": true, "parentColumnSpace": true, "parentRowSpace": true, "leftColumn": true, "rightColumn": true, "topRow": true, "bottomRow": true, "parentId": true, "dynamicBindingPathList": true}, "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "iafd4c74uv", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "PRIMARY", "placement": "CENTER"}], "key": "f60igj2d3o", "isDeprecated": false, "detachFromLayout": true, "widgetId": "iafd4c74uv", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "containerStyle": "none", "isVisible": true, "version": 1.0, "parentId": "125vv3b0df", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}], "borderWidth": "0", "key": "jy0y56j50v", "disablePropertyPane": true, "backgroundColor": "white", "isDeprecated": false, "rightColumn": 64.0, "widgetId": "125vv3b0df", "containerStyle": "card", "isVisible": true, "version": 1.0, "parentId": "o5auo6qvk3", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}], "key": "f60igj2d3o", "isDeprecated": false, "rightColumn": 721.5, "detachFromLayout": true, "widgetId": "o5auo6qvk3", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "containerStyle": "none", "isVisible": true, "version": 1.0, "parentId": "kzh84l1uct", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}], "privateWidgets": {"Text1": true, "Button2": true}, "key": "u24sotuf5t", "backgroundColor": "transparent", "isDeprecated": false, "rightColumn": 25.0, "itemBackgroundColor": "#FFFFFF", "widgetId": "kzh84l1uct", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "Container2", "borderColor": "transparent", "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "searchTags": ["div", "parent", "group"], "topRow": 39.0, "bottomRow": 63.0, "parentRowSpace": 10.0, "type": "CONTAINER_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 28.296875, "leftColumn": 27.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"boxShadow": "none", "widgetName": "Canvas3", "displayName": "<PERSON><PERSON>", "topRow": 0.0, "bottomRow": 390.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "minHeight": 400.0, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "accentColor"}], "children": [{"boxShadow": "none", "widgetName": "Input3", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595da61a34f563dba82adeb06ec.svg", "searchTags": ["form", "text input", "number", "textarea"], "topRow": 8.0, "bottomRow": 12.0, "parentRowSpace": 10.0, "labelWidth": 5.0, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "animateLoading": true, "parentColumnSpace": 28.296875, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelPosition": "Left", "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "1zdp79900o", "labelTextSize": "0.875rem", "isRequired": false, "isDeprecated": false, "rightColumn": 62.0, "widgetId": "zgbpy1eapc", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "Id", "version": 2.0, "parentId": "zb5odeeikn", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "iconAlign": "left", "defaultText": ""}, {"boxShadow": "none", "widgetName": "Input1", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595da61a34f563dba82adeb06ec.svg", "searchTags": ["form", "text input", "number", "textarea"], "topRow": 12.0, "bottomRow": 16.0, "parentRowSpace": 10.0, "labelWidth": 5.0, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "animateLoading": true, "parentColumnSpace": 30.0625, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelPosition": "Left", "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "1zdp79900o", "labelTextSize": "0.875rem", "isRequired": false, "isDeprecated": false, "rightColumn": 62.0, "widgetId": "f5yrov034w", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "UserName", "version": 2.0, "parentId": "zb5odeeikn", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "iconAlign": "left", "defaultText": ""}, {"boxShadow": "none", "widgetName": "Input2", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595da61a34f563dba82adeb06ec.svg", "searchTags": ["form", "text input", "number", "textarea"], "topRow": 16.0, "bottomRow": 20.0, "parentRowSpace": 10.0, "labelWidth": 5.0, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "animateLoading": true, "parentColumnSpace": 30.0625, "dynamicTriggerPathList": [], "resetOnSubmit": true, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelPosition": "Left", "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "1zdp79900o", "labelTextSize": "0.875rem", "isRequired": false, "isDeprecated": false, "rightColumn": 62.0, "widgetId": "cead2dk8u0", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "Description", "version": 2.0, "parentId": "zb5odeeikn", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "iconAlign": "left", "defaultText": ""}], "key": "f60igj2d3o", "isDeprecated": false, "rightColumn": 679.125, "detachFromLayout": true, "widgetId": "zb5odeeikn", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "containerStyle": "none", "isVisible": true, "version": 1.0, "parentId": "kheczo3agx", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}], "borderWidth": "0", "key": "jy0y56j50v", "backgroundColor": "#FFFFFF", "isDeprecated": false, "rightColumn": 63.0, "widgetId": "kheczo3agx", "containerStyle": "card", "isVisible": true, "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}, {"boxShadow": "none", "widgetName": "Modal1", "isCanvas": true, "displayName": "Modal", "iconSVG": "/static/media/icon.4975978e9a961fb0bfb4e38de7ecc7c5.svg", "searchTags": ["dialog", "popup", "notification"], "topRow": 0.0, "bottomRow": 0.0, "parentRowSpace": 1.0, "type": "MODAL_WIDGET", "hideCard": false, "shouldScrollContents": true, "animateLoading": true, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}], "children": [{"boxShadow": "none", "widgetName": "Canvas4", "displayName": "<PERSON><PERSON>", "topRow": 0.0, "bottomRow": 420.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "hideCard": true, "shouldScrollContents": false, "minHeight": 422.0, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "accentColor"}], "children": [{"boxShadow": "none", "widgetName": "IconButton1", "onClick": "{{closeModal(Modal1.name)}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "Icon button", "iconSVG": "/static/media/icon.1a0c634ac75f9fa6b6ae7a8df882a3ba.svg", "searchTags": ["click", "submit"], "topRow": 1.0, "bottomRow": 5.0, "type": "ICON_BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "leftColumn": 56.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "iconSize": 24.0, "isDisabled": false, "key": "ucyfoaw39i", "isDeprecated": false, "rightColumn": 64.0, "iconName": "cross", "widgetId": "j6fux73o0f", "isVisible": true, "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "TERTIARY"}, {"widgetName": "Text2", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 1.0, "bottomRow": 5.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "dynamicTriggerPathList": [], "leftColumn": 1.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "User Detail", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 41.0, "textAlign": "LEFT", "widgetId": "5gfpkzguyw", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1.5rem"}, {"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button3", "onClick": "{{closeModal(Modal1.name)}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 35.0, "bottomRow": 39.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "leftColumn": 28.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Close", "isDisabled": false, "key": "5x7gbvr1uj", "isDeprecated": false, "rightColumn": 44.0, "isDefaultClickDisabled": true, "widgetId": "xwn8kc7ivc", "buttonStyle": "PRIMARY", "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "SECONDARY", "placement": "CENTER"}, {"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button4", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 35.0, "bottomRow": 39.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "leftColumn": 46.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Confirm", "isDisabled": false, "key": "5x7gbvr1uj", "isDeprecated": false, "rightColumn": 62.0, "isDefaultClickDisabled": true, "widgetId": "pvxblhxywd", "buttonStyle": "PRIMARY_BUTTON", "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "PRIMARY", "placement": "CENTER"}, {"widgetName": "Text3", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 11.0, "bottomRow": 15.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 12.75, "dynamicTriggerPathList": [], "leftColumn": 1.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}, {"key": "text"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "Username: {{List1.selectedItem.user_name}}", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 17.0, "textAlign": "LEFT", "widgetId": "w7x8uzuhf6", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1rem"}, {"widgetName": "Text4", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 15.0, "bottomRow": 19.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 12.75, "dynamicTriggerPathList": [], "leftColumn": 1.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}, {"key": "text"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "Desc: {{List1.selectedItem.desc}}", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 17.0, "textAlign": "LEFT", "widgetId": "cpd3ueyam9", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1rem"}, {"widgetName": "Text5", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 7.0, "bottomRow": 11.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 12.75, "dynamicTriggerPathList": [], "leftColumn": 1.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}, {"key": "text"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "Id: {{List1.selectedItem.id}}", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 17.0, "textAlign": "LEFT", "widgetId": "xitjf3nw4p", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "kspna5fec0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1rem"}], "isDisabled": false, "key": "f60igj2d3o", "isDeprecated": false, "rightColumn": 0.0, "detachFromLayout": true, "widgetId": "kspna5fec0", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "version": 1.0, "parentId": "7fwzl6yfvi", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}], "key": "d2iov5wtrr", "height": 422.0, "isDeprecated": false, "rightColumn": 0.0, "detachFromLayout": true, "widgetId": "7fwzl6yfvi", "canOutsideClickClose": true, "canEscapeKeyClose": true, "version": 2.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "width": 828.0}, {"widgetName": "Text6", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 72.0, "bottomRow": 101.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "SCROLL", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 28.296875, "dynamicTriggerPathList": [], "leftColumn": 27.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}, {"key": "text"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "{{List1.listData}}", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 43.0, "textAlign": "LEFT", "widgetId": "eght65adpa", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1rem"}, {"widgetName": "Text7", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 68.0, "bottomRow": 72.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 28.296875, "dynamicTriggerPathList": [], "leftColumn": 27.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "Current List Data:", "key": "x4kskf46w4", "isDeprecated": false, "rightColumn": 43.0, "textAlign": "LEFT", "widgetId": "2uz2at8plo", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "fontSize": "1rem"}]}}