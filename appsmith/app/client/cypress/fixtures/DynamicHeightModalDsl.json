{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 460, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 70, "minHeight": 1292, "dynamicTriggerPathList": [], "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"width": 456, "height": 240, "minDynamicHeight": 24, "canEscapeKeyClose": true, "animateLoading": true, "detachFromLayout": true, "canOutsideClickClose": true, "shouldScrollContents": true, "widgetName": "Modal1", "children": [{"isVisible": true, "widgetName": "Canvas1", "version": 1, "detachFromLayout": true, "type": "CANVAS_WIDGET", "hideCard": true, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "xx8sm87dws", "canExtend": true, "isDisabled": false, "shouldScrollContents": false, "children": [{"isVisible": true, "iconName": "cross", "buttonVariant": "TERTIARY", "isDisabled": false, "widgetName": "IconButton1", "version": 1, "animateLoading": true, "searchTags": ["click", "submit"], "type": "ICON_BUTTON_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Icon button", "key": "bn1n9qjfu3", "iconSVG": "/static/media/icon.1a0c634ac75f9fa6b6ae7a8df882a3ba.svg", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "iconSize": 24, "widgetId": "4xkphqyh71", "renderMode": "CANVAS", "boxShadow": "none", "isLoading": false, "leftColumn": 58, "rightColumn": 64, "topRow": 0, "bottomRow": 4, "parentId": "1573co96ns", "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "onClick": "{{closeModal(Modal1.name)}}"}, {"isVisible": true, "text": "Modal Title", "fontSize": "1.25rem", "fontStyle": "BOLD", "textAlign": "LEFT", "textColor": "#231F20", "widgetName": "Text1", "shouldTruncate": false, "overflow": "NONE", "version": 1, "animateLoading": true, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "AUTO_HEIGHT", "searchTags": ["typography", "paragraph", "label"], "type": "TEXT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Text", "key": "bvsp43k7tt", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "widgetId": "eru5zrhzz8", "renderMode": "CANVAS", "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "leftColumn": 1, "rightColumn": 41, "topRow": 1, "bottomRow": 5, "parentId": "1573co96ns", "dynamicBindingPathList": [{"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "dynamicTriggerPathList": [], "originalTopRow": 1, "originalBottomRow": 5}, {"isVisible": true, "animateLoading": true, "text": "Close", "buttonVariant": "SECONDARY", "placement": "CENTER", "widgetName": "Button1", "isDisabled": false, "isDefaultClickDisabled": true, "disabledWhenInvalid": false, "resetFormOnClick": false, "recaptchaType": "V3", "version": 1, "searchTags": ["click", "submit"], "type": "BUTTON_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "ytud4zy4dq", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "buttonStyle": "PRIMARY", "widgetId": "a0mgh5gph3", "renderMode": "CANVAS", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "leftColumn": 31, "rightColumn": 47, "topRow": 18, "bottomRow": 22, "parentId": "1573co96ns", "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "onClick": "{{closeModal(Modal1.name)}}", "originalTopRow": 18, "originalBottomRow": 22}, {"isVisible": true, "animateLoading": true, "text": "Confirm", "buttonVariant": "PRIMARY", "placement": "CENTER", "widgetName": "Button2", "isDisabled": false, "isDefaultClickDisabled": true, "disabledWhenInvalid": false, "resetFormOnClick": false, "recaptchaType": "V3", "version": 1, "searchTags": ["click", "submit"], "type": "BUTTON_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "ytud4zy4dq", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "buttonStyle": "PRIMARY_BUTTON", "widgetId": "togjqv7kh9", "renderMode": "CANVAS", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "leftColumn": 47, "rightColumn": 63, "topRow": 18, "bottomRow": 22, "parentId": "1573co96ns", "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}]}], "minHeight": 240, "widgetId": "1573co96ns", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 239.25, "topRow": 0, "bottomRow": 240, "parentId": "n06r0m7kc4", "dynamicBindingPathList": []}], "version": 2, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["dialog", "popup", "notification"], "type": "MODAL_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Modal", "key": "tdl81t4c2r", "iconSVG": "/static/media/icon.4975978e9a961fb0bfb4e38de7ecc7c5.svg", "isCanvas": true, "widgetId": "n06r0m7kc4", "renderMode": "CANVAS", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 9.96875, "parentRowSpace": 10, "leftColumn": 15, "rightColumn": 39, "topRow": 3, "bottomRow": 243, "parentId": "0", "dynamicBindingPathList": [{"key": "borderRadius"}], "dynamicTriggerPathList": []}]}}