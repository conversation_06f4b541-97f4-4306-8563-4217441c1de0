{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 380, "containerStyle": "none", "snapRows": 124, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 77, "minHeight": 1292, "useAutoLayout": true, "dynamicTriggerPathList": [], "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button1", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 0, "bottomRow": 4, "parentRowSpace": 10, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 10.59375, "leftColumn": 0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "5j1va0fjnq", "isDeprecated": false, "rightColumn": 16, "isDefaultClickDisabled": true, "widgetId": "uwfcx04z6m", "minWidth": 120, "isVisible": true, "recaptchaType": "V3", "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "PRIMARY", "placement": "CENTER", "alignment": "start"}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "Container2", "borderColor": "#E0DEDE", "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "searchTags": ["div", "parent", "group"], "topRow": 0, "bottomRow": 10, "parentRowSpace": 10, "type": "CONTAINER_WIDGET", "hideCard": false, "shouldScrollContents": true, "animateLoading": true, "parentColumnSpace": 10.59375, "leftColumn": 16, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"widgetName": "Canvas2", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 100, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "minHeight": 100, "parentColumnSpace": 1, "leftColumn": 0, "dynamicBindingPathList": [], "children": [], "key": "qkztw95rx0", "isDeprecated": false, "rightColumn": 64, "detachFromLayout": true, "widgetId": "yrjrbvmvp6", "containerStyle": "none", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "q8fj71e00n", "renderMode": "CANVAS", "isLoading": false, "responsiveBehavior": "fill", "flexLayers": []}], "borderWidth": "1", "key": "tugffz4d08", "backgroundColor": "#FFFFFF", "isDeprecated": false, "rightColumn": 40, "dynamicHeight": "AUTO_HEIGHT", "widgetId": "q8fj71e00n", "containerStyle": "card", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "responsiveBehavior": "fill", "originalTopRow": 0, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000, "originalBottomRow": 11, "alignment": "start", "minDynamicHeight": 10}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "Container1", "borderColor": "#E0DEDE", "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "searchTags": ["div", "parent", "group"], "topRow": 0, "bottomRow": 10, "parentRowSpace": 10, "type": "CONTAINER_WIDGET", "hideCard": false, "shouldScrollContents": true, "animateLoading": true, "parentColumnSpace": 10.59375, "leftColumn": 40, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"widgetName": "Canvas1", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 100, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "minHeight": 100, "parentColumnSpace": 1, "leftColumn": 0, "dynamicBindingPathList": [], "children": [], "key": "qkztw95rx0", "isDeprecated": false, "rightColumn": 64, "detachFromLayout": true, "widgetId": "i2oaw09sw5", "containerStyle": "none", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "jum0397j2c", "renderMode": "CANVAS", "isLoading": false, "responsiveBehavior": "fill", "flexLayers": []}], "borderWidth": "1", "key": "tugffz4d08", "backgroundColor": "#FFFFFF", "isDeprecated": false, "rightColumn": 64, "dynamicHeight": "AUTO_HEIGHT", "widgetId": "jum0397j2c", "containerStyle": "card", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "responsiveBehavior": "fill", "originalTopRow": 0, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000, "originalBottomRow": 11, "alignment": "start", "minDynamicHeight": 10}], "positioning": "vertical", "direction": "Vertical", "flexLayers": [{"children": [{"id": "uwfcx04z6m", "align": "start"}, {"id": "q8fj71e00n", "align": "start"}, {"id": "jum0397j2c", "align": "start"}]}, {"children": [{"id": "94xn6dpsab", "align": "start"}]}]}}