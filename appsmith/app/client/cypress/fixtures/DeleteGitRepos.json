{"clientSchemaVersion": 1.0, "serverSchemaVersion": 6.0, "exportedApplication": {"name": "DeleteGitRepos", "isPublic": false, "pages": [{"id": "Page1", "isDefault": true}], "publishedPages": [{"id": "Page1", "isDefault": true}], "viewMode": false, "appIsExample": false, "unreadCommentThreads": 0.0, "color": "#FBF4ED", "icon": "diamond", "slug": "deletegitrepos", "unpublishedCustomJSLibs": [], "publishedCustomJSLibs": [], "evaluationVersion": 2.0, "applicationVersion": 2.0, "collapseInvisibleWidgets": true, "isManualUpdate": false, "deleted": false}, "datasourceList": [], "customJSLibList": [], "pageList": [{"unpublishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1292.0, "containerStyle": "none", "snapRows": 125.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 76.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "borderColor": "#E0DEDE", "isVisibleDownload": true, "iconSVG": "/static/media/icon.db8a9cbd2acd22a31ea91cc37ea2a46c.svg", "topRow": 19.0, "isSortable": true, "type": "TABLE_WIDGET_V2", "inlineEditingSaveOption": "ROW_LEVEL", "animateLoading": true, "dynamicBindingPathList": [{"key": "tableData"}, {"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}, {"key": "primaryColumns.id.computedValue"}, {"key": "primaryColumns.owner.computedValue"}, {"key": "primaryColumns.name.computedValue"}, {"key": "primaryColumns.full_name.computedValue"}, {"key": "primaryColumns.description.computedValue"}, {"key": "primaryColumns.empty.computedValue"}, {"key": "primaryColumns.private.computedValue"}, {"key": "primaryColumns.fork.computedValue"}, {"key": "primaryColumns.template.computedValue"}, {"key": "primaryColumns.parent.computedValue"}, {"key": "primaryColumns.mirror.computedValue"}, {"key": "primaryColumns.size.computedValue"}, {"key": "primaryColumns.language.computedValue"}, {"key": "primaryColumns.languages_url.computedValue"}, {"key": "primaryColumns.html_url.computedValue"}, {"key": "primaryColumns.ssh_url.computedValue"}, {"key": "primaryColumns.clone_url.computedValue"}, {"key": "primaryColumns.original_url.computedValue"}, {"key": "primaryColumns.website.computedValue"}, {"key": "primaryColumns.stars_count.computedValue"}, {"key": "primaryColumns.forks_count.computedValue"}, {"key": "primaryColumns.watchers_count.computedValue"}, {"key": "primaryColumns.open_issues_count.computedValue"}, {"key": "primaryColumns.open_pr_counter.computedValue"}, {"key": "primaryColumns.release_counter.computedValue"}, {"key": "primaryColumns.default_branch.computedValue"}, {"key": "primaryColumns.archived.computedValue"}, {"key": "primaryColumns.created_at.computedValue"}, {"key": "primaryColumns.updated_at.computedValue"}, {"key": "primaryColumns.permissions.computedValue"}, {"key": "primaryColumns.has_issues.computedValue"}, {"key": "primaryColumns.internal_tracker.computedValue"}, {"key": "primaryColumns.has_wiki.computedValue"}, {"key": "primaryColumns.has_pull_requests.computedValue"}, {"key": "primaryColumns.has_projects.computedValue"}, {"key": "primaryColumns.ignore_whitespace_conflicts.computedValue"}, {"key": "primaryColumns.allow_merge_commits.computedValue"}, {"key": "primaryColumns.allow_rebase.computedValue"}, {"key": "primaryColumns.allow_rebase_explicit.computedValue"}, {"key": "primaryColumns.allow_squash_merge.computedValue"}, {"key": "primaryColumns.allow_rebase_update.computedValue"}, {"key": "primaryColumns.default_delete_branch_after_merge.computedValue"}, {"key": "primaryColumns.default_merge_style.computedValue"}, {"key": "primaryColumns.avatar_url.computedValue"}, {"key": "primaryColumns.internal.computedValue"}, {"key": "primaryColumns.mirror_interval.computedValue"}, {"key": "primaryColumns.mirror_updated.computedValue"}, {"key": "primaryColumns.repo_transfer.computedValue"}], "leftColumn": 0.0, "delimiter": ",", "defaultSelectedRowIndex": 0.0, "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisibleFilters": true, "isVisible": true, "enableClientSideSearch": true, "version": 1.0, "totalRecordsCount": 0.0, "isLoading": false, "childStylesheet": {"button": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "menuButton": {"menuColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "iconButton": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "editActions": {"saveButtonColor": "{{appsmith.theme.colors.primaryColor}}", "saveBorderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "discardButtonColor": "{{appsmith.theme.colors.primaryColor}}", "discardBorderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}}, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "defaultSelectedRowIndices": [0.0], "widgetName": "Table1", "defaultPageSize": 0.0, "columnOrder": ["id", "owner", "name", "full_name", "description", "empty", "private", "fork", "template", "parent", "mirror", "size", "language", "languages_url", "html_url", "ssh_url", "clone_url", "original_url", "website", "stars_count", "forks_count", "watchers_count", "open_issues_count", "open_pr_counter", "release_counter", "default_branch", "archived", "created_at", "updated_at", "permissions", "has_issues", "internal_tracker", "has_wiki", "has_pull_requests", "has_projects", "ignore_whitespace_conflicts", "allow_merge_commits", "allow_rebase", "allow_rebase_explicit", "allow_squash_merge", "allow_rebase_update", "default_delete_branch_after_merge", "default_merge_style", "avatar_url", "internal", "mirror_interval", "mirror_updated", "repo_transfer"], "dynamicPropertyPathList": [], "displayName": "Table", "bottomRow": 69.0, "columnWidthMap": {"task": 245.0, "step": 62.0, "status": 75.0}, "parentRowSpace": 10.0, "hideCard": false, "dynamicTriggerPathList": [{"key": "onRowSelected"}], "borderWidth": "1", "primaryColumns": {"id": {"allowCellWrapping": false, "index": 0.0, "width": 150.0, "originalId": "id", "id": "id", "alias": "id", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "id", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"id\"]))}}", "validation": {}, "cellBackground": ""}, "owner": {"allowCellWrapping": false, "index": 1.0, "width": 150.0, "originalId": "owner", "id": "owner", "alias": "owner", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "owner", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"owner\"]))}}", "validation": {}, "cellBackground": ""}, "name": {"allowCellWrapping": false, "index": 2.0, "width": 150.0, "originalId": "name", "id": "name", "alias": "name", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "name", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"name\"]))}}", "validation": {}, "cellBackground": ""}, "full_name": {"allowCellWrapping": false, "index": 3.0, "width": 150.0, "originalId": "full_name", "id": "full_name", "alias": "full_name", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "full_name", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"full_name\"]))}}", "validation": {}, "cellBackground": ""}, "description": {"allowCellWrapping": false, "index": 4.0, "width": 150.0, "originalId": "description", "id": "description", "alias": "description", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "description", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"description\"]))}}", "validation": {}, "cellBackground": ""}, "empty": {"allowCellWrapping": false, "index": 5.0, "width": 150.0, "originalId": "empty", "id": "empty", "alias": "empty", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "empty", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"empty\"]))}}", "validation": {}, "cellBackground": ""}, "private": {"allowCellWrapping": false, "index": 6.0, "width": 150.0, "originalId": "private", "id": "private", "alias": "private", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "private", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"private\"]))}}", "validation": {}, "cellBackground": ""}, "fork": {"allowCellWrapping": false, "index": 7.0, "width": 150.0, "originalId": "fork", "id": "fork", "alias": "fork", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "fork", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"fork\"]))}}", "validation": {}, "cellBackground": ""}, "template": {"allowCellWrapping": false, "index": 8.0, "width": 150.0, "originalId": "template", "id": "template", "alias": "template", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "template", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"template\"]))}}", "validation": {}, "cellBackground": ""}, "parent": {"allowCellWrapping": false, "index": 9.0, "width": 150.0, "originalId": "parent", "id": "parent", "alias": "parent", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "parent", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"parent\"]))}}", "validation": {}, "cellBackground": ""}, "mirror": {"allowCellWrapping": false, "index": 10.0, "width": 150.0, "originalId": "mirror", "id": "mirror", "alias": "mirror", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "mirror", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"mirror\"]))}}", "validation": {}, "cellBackground": ""}, "size": {"allowCellWrapping": false, "index": 11.0, "width": 150.0, "originalId": "size", "id": "size", "alias": "size", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "size", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"size\"]))}}", "validation": {}, "cellBackground": ""}, "language": {"allowCellWrapping": false, "index": 12.0, "width": 150.0, "originalId": "language", "id": "language", "alias": "language", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "language", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"language\"]))}}", "validation": {}, "cellBackground": ""}, "languages_url": {"allowCellWrapping": false, "index": 13.0, "width": 150.0, "originalId": "languages_url", "id": "languages_url", "alias": "languages_url", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "languages_url", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"languages_url\"]))}}", "validation": {}, "cellBackground": ""}, "html_url": {"allowCellWrapping": false, "index": 14.0, "width": 150.0, "originalId": "html_url", "id": "html_url", "alias": "html_url", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "html_url", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"html_url\"]))}}", "validation": {}, "cellBackground": ""}, "ssh_url": {"allowCellWrapping": false, "index": 15.0, "width": 150.0, "originalId": "ssh_url", "id": "ssh_url", "alias": "ssh_url", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "ssh_url", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"ssh_url\"]))}}", "validation": {}, "cellBackground": ""}, "clone_url": {"allowCellWrapping": false, "index": 16.0, "width": 150.0, "originalId": "clone_url", "id": "clone_url", "alias": "clone_url", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "clone_url", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"clone_url\"]))}}", "validation": {}, "cellBackground": ""}, "original_url": {"allowCellWrapping": false, "index": 17.0, "width": 150.0, "originalId": "original_url", "id": "original_url", "alias": "original_url", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "original_url", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"original_url\"]))}}", "validation": {}, "cellBackground": ""}, "website": {"allowCellWrapping": false, "index": 18.0, "width": 150.0, "originalId": "website", "id": "website", "alias": "website", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "website", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"website\"]))}}", "validation": {}, "cellBackground": ""}, "stars_count": {"allowCellWrapping": false, "index": 19.0, "width": 150.0, "originalId": "stars_count", "id": "stars_count", "alias": "stars_count", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "stars_count", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"stars_count\"]))}}", "validation": {}, "cellBackground": ""}, "forks_count": {"allowCellWrapping": false, "index": 20.0, "width": 150.0, "originalId": "forks_count", "id": "forks_count", "alias": "forks_count", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "forks_count", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"forks_count\"]))}}", "validation": {}, "cellBackground": ""}, "watchers_count": {"allowCellWrapping": false, "index": 21.0, "width": 150.0, "originalId": "watchers_count", "id": "watchers_count", "alias": "watchers_count", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "watchers_count", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"watchers_count\"]))}}", "validation": {}, "cellBackground": ""}, "open_issues_count": {"allowCellWrapping": false, "index": 22.0, "width": 150.0, "originalId": "open_issues_count", "id": "open_issues_count", "alias": "open_issues_count", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "open_issues_count", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"open_issues_count\"]))}}", "validation": {}, "cellBackground": ""}, "open_pr_counter": {"allowCellWrapping": false, "index": 23.0, "width": 150.0, "originalId": "open_pr_counter", "id": "open_pr_counter", "alias": "open_pr_counter", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "open_pr_counter", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"open_pr_counter\"]))}}", "validation": {}, "cellBackground": ""}, "release_counter": {"allowCellWrapping": false, "index": 24.0, "width": 150.0, "originalId": "release_counter", "id": "release_counter", "alias": "release_counter", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "number", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "release_counter", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"release_counter\"]))}}", "validation": {}, "cellBackground": ""}, "default_branch": {"allowCellWrapping": false, "index": 25.0, "width": 150.0, "originalId": "default_branch", "id": "default_branch", "alias": "default_branch", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "default_branch", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"default_branch\"]))}}", "validation": {}, "cellBackground": ""}, "archived": {"allowCellWrapping": false, "index": 26.0, "width": 150.0, "originalId": "archived", "id": "archived", "alias": "archived", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "archived", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"archived\"]))}}", "validation": {}, "cellBackground": ""}, "created_at": {"allowCellWrapping": false, "index": 27.0, "width": 150.0, "originalId": "created_at", "id": "created_at", "alias": "created_at", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "created_at", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"created_at\"]))}}", "validation": {}, "cellBackground": ""}, "updated_at": {"allowCellWrapping": false, "index": 28.0, "width": 150.0, "originalId": "updated_at", "id": "updated_at", "alias": "updated_at", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "updated_at", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"updated_at\"]))}}", "validation": {}, "cellBackground": ""}, "permissions": {"allowCellWrapping": false, "index": 29.0, "width": 150.0, "originalId": "permissions", "id": "permissions", "alias": "permissions", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "permissions", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"permissions\"]))}}", "validation": {}, "cellBackground": ""}, "has_issues": {"allowCellWrapping": false, "index": 30.0, "width": 150.0, "originalId": "has_issues", "id": "has_issues", "alias": "has_issues", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "has_issues", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"has_issues\"]))}}", "validation": {}, "cellBackground": ""}, "internal_tracker": {"allowCellWrapping": false, "index": 31.0, "width": 150.0, "originalId": "internal_tracker", "id": "internal_tracker", "alias": "internal_tracker", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "internal_tracker", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"internal_tracker\"]))}}", "validation": {}, "cellBackground": ""}, "has_wiki": {"allowCellWrapping": false, "index": 32.0, "width": 150.0, "originalId": "has_wiki", "id": "has_wiki", "alias": "has_wiki", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "has_wiki", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"has_wiki\"]))}}", "validation": {}, "cellBackground": ""}, "has_pull_requests": {"allowCellWrapping": false, "index": 33.0, "width": 150.0, "originalId": "has_pull_requests", "id": "has_pull_requests", "alias": "has_pull_requests", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "has_pull_requests", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"has_pull_requests\"]))}}", "validation": {}, "cellBackground": ""}, "has_projects": {"allowCellWrapping": false, "index": 34.0, "width": 150.0, "originalId": "has_projects", "id": "has_projects", "alias": "has_projects", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "has_projects", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"has_projects\"]))}}", "validation": {}, "cellBackground": ""}, "ignore_whitespace_conflicts": {"allowCellWrapping": false, "index": 35.0, "width": 150.0, "originalId": "ignore_whitespace_conflicts", "id": "ignore_whitespace_conflicts", "alias": "ignore_whitespace_conflicts", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "ignore_whitespace_conflicts", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"ignore_whitespace_conflicts\"]))}}", "validation": {}, "cellBackground": ""}, "allow_merge_commits": {"allowCellWrapping": false, "index": 36.0, "width": 150.0, "originalId": "allow_merge_commits", "id": "allow_merge_commits", "alias": "allow_merge_commits", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "allow_merge_commits", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"allow_merge_commits\"]))}}", "validation": {}, "cellBackground": ""}, "allow_rebase": {"allowCellWrapping": false, "index": 37.0, "width": 150.0, "originalId": "allow_rebase", "id": "allow_rebase", "alias": "allow_rebase", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "allow_rebase", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"allow_rebase\"]))}}", "validation": {}, "cellBackground": ""}, "allow_rebase_explicit": {"allowCellWrapping": false, "index": 38.0, "width": 150.0, "originalId": "allow_rebase_explicit", "id": "allow_rebase_explicit", "alias": "allow_rebase_explicit", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "allow_rebase_explicit", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"allow_rebase_explicit\"]))}}", "validation": {}, "cellBackground": ""}, "allow_squash_merge": {"allowCellWrapping": false, "index": 39.0, "width": 150.0, "originalId": "allow_squash_merge", "id": "allow_squash_merge", "alias": "allow_squash_merge", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "allow_squash_merge", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"allow_squash_merge\"]))}}", "validation": {}, "cellBackground": ""}, "allow_rebase_update": {"allowCellWrapping": false, "index": 40.0, "width": 150.0, "originalId": "allow_rebase_update", "id": "allow_rebase_update", "alias": "allow_rebase_update", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "allow_rebase_update", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"allow_rebase_update\"]))}}", "validation": {}, "cellBackground": ""}, "default_delete_branch_after_merge": {"allowCellWrapping": false, "index": 41.0, "width": 150.0, "originalId": "default_delete_branch_after_merge", "id": "default_delete_branch_after_merge", "alias": "default_delete_branch_after_merge", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "default_delete_branch_after_merge", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"default_delete_branch_after_merge\"]))}}", "validation": {}, "cellBackground": ""}, "default_merge_style": {"allowCellWrapping": false, "index": 42.0, "width": 150.0, "originalId": "default_merge_style", "id": "default_merge_style", "alias": "default_merge_style", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "default_merge_style", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"default_merge_style\"]))}}", "validation": {}, "cellBackground": ""}, "avatar_url": {"allowCellWrapping": false, "index": 43.0, "width": 150.0, "originalId": "avatar_url", "id": "avatar_url", "alias": "avatar_url", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "avatar_url", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"avatar_url\"]))}}", "validation": {}, "cellBackground": ""}, "internal": {"allowCellWrapping": false, "index": 44.0, "width": 150.0, "originalId": "internal", "id": "internal", "alias": "internal", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "checkbox", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "internal", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"internal\"]))}}", "validation": {}, "cellBackground": ""}, "mirror_interval": {"allowCellWrapping": false, "index": 45.0, "width": 150.0, "originalId": "mirror_interval", "id": "mirror_interval", "alias": "mirror_interval", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "mirror_interval", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"mirror_interval\"]))}}", "validation": {}, "cellBackground": ""}, "mirror_updated": {"allowCellWrapping": false, "index": 46.0, "width": 150.0, "originalId": "mirror_updated", "id": "mirror_updated", "alias": "mirror_updated", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "mirror_updated", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"mirror_updated\"]))}}", "validation": {}, "cellBackground": ""}, "repo_transfer": {"allowCellWrapping": false, "index": 47.0, "width": 150.0, "originalId": "repo_transfer", "id": "repo_transfer", "alias": "repo_transfer", "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textColor": "", "textSize": "0.875rem", "fontStyle": "", "enableFilter": true, "enableSort": true, "isVisible": true, "isDisabled": false, "isCellEditable": false, "isEditable": false, "isCellVisible": true, "isDerived": false, "label": "repo_transfer", "isSaveVisible": true, "isDiscardVisible": true, "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"repo_transfer\"]))}}", "validation": {}, "cellBackground": ""}}, "onRowSelected": "{{Delete_repos.run(() => Gitea_getallrepos.run(), () => {})}}", "key": "xnuqsooiqp", "isDeprecated": false, "rightColumn": 57.0, "textSize": "0.875rem", "widgetId": "jbqhx86b4j", "tableData": "{{Gitea_getallrepos.data.data}}", "label": "Data", "searchKey": "", "parentId": "0", "renderMode": "CANVAS", "horizontalAlignment": "LEFT", "isVisibleSearch": true, "isVisiblePagination": true, "verticalAlignment": "CENTER"}, {"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button1", "onClick": "{{JSObject1.myFun1();\nDelete_repos.run();}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "dynamicPropertyPathList": [{"key": "onClick"}], "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 15.0, "bottomRow": 19.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 16.71875, "dynamicTriggerPathList": [{"key": "onClick"}], "leftColumn": 27.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Delete", "isDisabled": false, "key": "1owdh9nu5h", "isDeprecated": false, "rightColumn": 43.0, "isDefaultClickDisabled": true, "widgetId": "h7tkxxdsq1", "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "PRIMARY", "placement": "CENTER"}]}, "layoutOnLoadActions": [[{"id": "Page1_Gitea_getallrepos", "name": "G<PERSON><PERSON>_getallrepos", "confirmBeforeExecute": false, "pluginType": "API", "jsonPathKeys": [], "timeoutInMillisecond": 10000.0}]], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policies": []}, "publishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224.0, "snapColumns": 16.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1250.0, "containerStyle": "none", "snapRows": 33.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 4.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": []}, "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policies": []}, "deleted": false, "gitSyncId": "63e0b95d6dd7a322279d010c_63e0b95d6dd7a322279d010f"}], "actionList": [{"pluginType": "API", "pluginId": "restapi-plugin", "unpublishedAction": {"name": "G<PERSON><PERSON>_getallrepos", "datasource": {"name": "http://35.154.225.218:3000", "pluginId": "restapi-plugin", "datasourceConfiguration": {"url": "http://35.154.225.218:3000"}, "invalids": [], "messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "path": "/api/v1/repos/search", "headers": [{"key": "accept", "value": "application/json"}], "encodeParamsToggle": true, "queryParameters": [{"key": "token", "value": "token a4f7ec6eaa7b69be297bd140acd1612c4d69311a"}], "httpMethod": "GET", "selfReferencingDataPaths": []}, "runBehaviour": "ON_PAGE_LOAD", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_Gitea_getallrepos", "deleted": false, "gitSyncId": "63e0b95d6dd7a322279d010c_63e0b96c6dd7a322279d011a"}, {"pluginType": "API", "pluginId": "restapi-plugin", "unpublishedAction": {"name": "Delete_repos", "datasource": {"name": "http://35.154.225.218:3000", "pluginId": "restapi-plugin", "datasourceConfiguration": {"url": "http://35.154.225.218:3000"}, "invalids": [], "messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "path": "/api/v1/repos/CI-Gitea/{{JSObject1.myFun1.data}}", "headers": [{"key": "accept", "value": "application/json"}, {"key": "Authorization", "value": "token a4f7ec6eaa7b69be297bd140acd1612c4d69311a"}], "encodeParamsToggle": true, "queryParameters": [], "body": "", "httpMethod": "DELETE", "selfReferencingDataPaths": []}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [{"key": "path"}], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": ["JSObject1.myFun1.data"], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_Delete_repos", "deleted": false, "gitSyncId": "63e0b95d6dd7a322279d010c_63e0bd676dd7a322279d011f"}, {"pluginType": "JS", "pluginId": "js-plugin", "unpublishedAction": {"name": "myFun1", "fullyQualifiedName": "JSObject1.myFun1", "datasource": {"name": "UNUSED_DATASOURCE", "pluginId": "js-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "collectionId": "Page1_JSObject1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "encodeParamsToggle": true, "body": "async () => {\n  await Gitea_getallrepos.run();\n  return Gitea_getallrepos.data.data[0].name;\n}", "selfReferencingDataPaths": [], "jsArguments": [], "isAsync": true}, "runBehaviour": "MANUAL", "clientSideExecution": true, "dynamicBindingPathList": [{"key": "body"}], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": ["async () => {\n  await Gitea_getallrepos.run();\n  return Gitea_getallrepos.data.data[0].name;\n}"], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_JSObject1.myFun1", "deleted": false, "gitSyncId": "63e0b95d6dd7a322279d010c_63e0be796dd7a322279d013e"}], "actionCollectionList": [{"unpublishedCollection": {"name": "JSObject1", "pageId": "Page1", "pluginId": "js-plugin", "pluginType": "JS", "actions": [], "archivedActions": [], "body": "export default {\n\tmyVar1: [],\n\tmyVar2: {},\n\tmyFun1: async () => {\n\t\tawait Gitea_getallrepos.run();\n\t\treturn Gitea_getallrepos.data.data[0].name;\n\t}\n}", "variables": [{"name": "myVar1", "value": "[]"}, {"name": "myVar2", "value": "{}"}], "userPermissions": []}, "id": "Page1_JSObject1", "deleted": false, "gitSyncId": "63e0b95d6dd7a322279d010c_63e0be796dd7a322279d0144"}], "updatedResources": {"actionList": ["Gitea_getallrepos##ENTITY_SEPARATOR##Page1", "JSObject1.myFun1##ENTITY_SEPARATOR##Page1", "Delete_repos##ENTITY_SEPARATOR##Page1"], "pageList": ["Page1"], "actionCollectionList": ["JSObject1##ENTITY_SEPARATOR##Page1"]}, "editModeTheme": {"name": "<PERSON><PERSON><PERSON>", "displayName": "Modern", "isSystemTheme": true, "deleted": false}, "publishedTheme": {"name": "<PERSON><PERSON><PERSON>", "displayName": "Modern", "isSystemTheme": true, "deleted": false}}