{"clientSchemaVersion": 1.0, "serverSchemaVersion": 6.0, "exportedApplication": {"name": "ContextSwitching", "isPublic": false, "pages": [{"id": "Page1", "isDefault": true}], "publishedPages": [{"id": "Page1", "isDefault": true}], "viewMode": false, "appIsExample": false, "unreadCommentThreads": 0.0, "color": "#C2DAF0", "icon": "frame", "slug": "contextswitching-1", "unpublishedAppLayout": {"type": "FLUID"}, "publishedCustomJSLibs": [], "evaluationVersion": 2.0, "applicationVersion": 2.0, "isManualUpdate": false, "deleted": false}, "datasourceList": [{"name": "<PERSON><PERSON><PERSON>", "pluginId": "restapi-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "gitSyncId": "5df9cd9109c7073d4ce37d49_61bb76c6cd5d7045095281f7"}, {"name": "Appsmith Image Assets", "pluginId": "amazons3-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "gitSyncId": "5df9cd9109c7073d4ce37d49_61bb76c6cd5d7045095281f9"}, {"name": "<PERSON><PERSON><PERSON>", "pluginId": "restapi-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "gitSyncId": "5df9cd9109c7073d4ce37d49_61bb76c5cd5d704509527eb3"}, {"name": "Internal DB", "pluginId": "postgres-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "gitSyncId": "5df9cd9109c7073d4ce37d49_61bb76c5cd5d704509527ec7"}, {"name": "Movies", "pluginId": "mongo-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "gitSyncId": "5df9cd9109c7073d4ce37d49_61bb76c7cd5d7045095285e2"}], "customJSLibList": [], "pageList": [{"unpublishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896.0, "snapColumns": 64.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1250.0, "containerStyle": "none", "snapRows": 125.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 76.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": [{"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button1", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 10.0, "bottomRow": 14.0, "parentRowSpace": 10.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 15.6875, "leftColumn": 5.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "s011uot4ul", "isDeprecated": false, "rightColumn": 21.0, "isDefaultClickDisabled": true, "widgetId": "h1np8mutzo", "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "PRIMARY", "placement": "CENTER"}, {"boxShadow": "none", "widgetName": "Input1", "displayName": "Input", "iconSVG": "/static/media/icon.9f505595da61a34f563dba82adeb06ec.svg", "searchTags": ["form", "text input", "number", "textarea"], "topRow": 5.0, "bottomRow": 9.0, "parentRowSpace": 10.0, "labelWidth": 5.0, "autoFocus": false, "type": "INPUT_WIDGET_V2", "hideCard": false, "animateLoading": true, "parentColumnSpace": 16.3125, "resetOnSubmit": true, "leftColumn": 2.0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelPosition": "Left", "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "key": "2qsnrpp5zu", "labelTextSize": "0.875rem", "isRequired": false, "isDeprecated": false, "rightColumn": 25.0, "dynamicHeight": "FIXED", "widgetId": "onii9k4vfq", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "Label", "version": 2.0, "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000.0, "iconAlign": "left", "defaultText": "", "minDynamicHeight": 4.0}, {"boxShadow": "none", "widgetName": "Image1", "displayName": "Image", "iconSVG": "/static/media/icon.52d8fb963abcb95c79b10f1553389f22.svg", "topRow": 16.0, "bottomRow": 45.0, "parentRowSpace": 10.0, "type": "IMAGE_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 16.3125, "imageShape": "RECTANGLE", "leftColumn": 2.0, "dynamicBindingPathList": [{"key": "borderRadius"}], "defaultImage": "http://host.docker.internal:4200/clouddefaultImage.png", "key": "r8ob443ol9", "image": "", "isDeprecated": false, "rightColumn": 25.0, "objectFit": "cover", "widgetId": "leg9shh821", "isVisible": true, "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "maxZoomLevel": 1.0, "enableDownload": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "enableRotation": false}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "Chart1", "allowScroll": false, "displayName": "Chart", "iconSVG": "/static/media/icon.6adbe31ed817fc4bfd66f9f0a6fc105c.svg", "searchTags": ["graph", "visuals", "visualisations"], "topRow": 5.0, "bottomRow": 45.0, "parentRowSpace": 10.0, "type": "CHART_WIDGET", "hideCard": false, "chartData": {"w8tod7yb6x": {"seriesName": "Sales", "data": "[\n  {\n    \"x\": \"Product1\",\n    \"y\": 20000\n  },\n  {\n    \"x\": \"Product2\",\n    \"y\": 22000\n  },\n  {\n    \"x\": \"Product3\",\n    \"y\": 32000\n  }\n]"}}, "animateLoading": true, "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 16.3125, "dynamicTriggerPathList": [], "leftColumn": 26.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}, {"key": "accentColor"}, {"key": "fontFamily"}], "customFusionChartConfig": {"type": "column2d", "dataSource": {"data": [{"label": "Product1", "value": 20000.0}, {"label": "Product2", "value": 22000.0}, {"label": "Product3", "value": 32000.0}], "chart": {"caption": "Sales Report", "xAxisName": "Product Line", "yAxisName": "Revenue($)", "theme": "fusion", "alignCaptionWithCanvas": 1.0, "captionFontSize": "24", "captionAlignment": "center", "captionPadding": "20", "captionFontColor": "#231F20", "legendIconSides": "4", "legendIconBgAlpha": "100", "legendIconAlpha": "100", "legendPosition": "top", "canvasPadding": "0", "chartLeftMargin": "20", "chartTopMargin": "10", "chartRightMargin": "40", "chartBottomMargin": "10", "xAxisNameFontSize": "14", "labelFontSize": "12", "labelFontColor": "#716E6E", "xAxisNameFontColor": "#716E6E", "yAxisNameFontSize": "14", "yAxisValueFontSize": "12", "yAxisValueFontColor": "#716E6E", "yAxisNameFontColor": "#716E6E"}}}, "key": "7nrtlw0qwh", "isDeprecated": false, "rightColumn": 61.0, "widgetId": "hj3w<PERSON>z8", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "version": 1.0, "parentId": "0", "labelOrientation": "auto", "renderMode": "CANVAS", "isLoading": false, "yAxisName": "Revenue($)", "chartName": "Sales Report", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "xAxisName": "Product Line", "chartType": "COLUMN_CHART"}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "widgetName": "Container1", "borderColor": "transparent", "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "searchTags": ["div", "parent", "group"], "topRow": 46.0, "bottomRow": 86.0, "parentRowSpace": 10.0, "type": "CONTAINER_WIDGET", "hideCard": false, "animateLoading": true, "parentColumnSpace": 16.3125, "leftColumn": 2.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"boxShadow": "none", "widgetName": "Canvas1", "displayName": "<PERSON><PERSON>", "topRow": 0.0, "bottomRow": 400.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "minHeight": 400.0, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "accentColor"}], "children": [{"widgetName": "Text1", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 8.0, "bottomRow": 29.0, "parentRowSpace": 10.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 14.7255859375, "dynamicTriggerPathList": [], "leftColumn": 16.0, "dynamicBindingPathList": [{"key": "fontFamily"}, {"key": "borderRadius"}], "shouldTruncate": false, "truncateButtonColor": "#FFC13D", "text": "Label", "key": "6i9fc2hoj5", "isDeprecated": false, "rightColumn": 47.0, "textAlign": "CENTER", "dynamicHeight": "FIXED", "widgetId": "hiejqpgenc", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "hto4699x1l", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000.0, "fontSize": "3rem", "minDynamicHeight": 4.0}], "key": "30ud2srz1w", "isDeprecated": false, "rightColumn": 391.5, "detachFromLayout": true, "widgetId": "hto4699x1l", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "containerStyle": "none", "isVisible": true, "version": 1.0, "parentId": "lfbrjo1aae", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}], "borderWidth": "0", "key": "l2gxaxceyf", "backgroundColor": "#FFFFFF", "isDeprecated": false, "rightColumn": 61.0, "dynamicHeight": "FIXED", "widgetId": "lfbrjo1aae", "containerStyle": "card", "isVisible": true, "version": 1.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000.0, "minDynamicHeight": 4.0}, {"boxShadow": "none", "widgetName": "Modal1", "isCanvas": true, "displayName": "Modal", "iconSVG": "/static/media/icon.4975978e9a961fb0bfb4e38de7ecc7c5.svg", "searchTags": ["dialog", "popup", "notification"], "topRow": 32.0, "bottomRow": 272.0, "parentRowSpace": 10.0, "type": "MODAL_WIDGET", "hideCard": false, "shouldScrollContents": true, "animateLoading": true, "parentColumnSpace": 16.3125, "leftColumn": 20.0, "dynamicBindingPathList": [{"key": "borderRadius"}], "children": [{"widgetName": "Canvas2", "displayName": "<PERSON><PERSON>", "topRow": 0.0, "bottomRow": 240.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "hideCard": true, "shouldScrollContents": false, "minHeight": 240.0, "parentColumnSpace": 1.0, "leftColumn": 0.0, "dynamicBindingPathList": [], "children": [{"boxShadow": "none", "widgetName": "IconButton1", "onClick": "{{closeModal(Modal1.name)}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "Icon button", "iconSVG": "/static/media/icon.1a0c634ac75f9fa6b6ae7a8df882a3ba.svg", "searchTags": ["click", "submit"], "topRow": 0.0, "bottomRow": 4.0, "type": "ICON_BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "leftColumn": 58.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "iconSize": 24.0, "isDisabled": false, "key": "h6asgvi0vd", "isDeprecated": false, "rightColumn": 64.0, "iconName": "cross", "widgetId": "8gwy85xlqd", "isVisible": true, "version": 1.0, "parentId": "pcni4dljt9", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "TERTIARY"}, {"widgetName": "Text2", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 1.0, "bottomRow": 5.0, "type": "TEXT_WIDGET", "hideCard": false, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "leftColumn": 1.0, "dynamicBindingPathList": [{"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "shouldTruncate": false, "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "text": "Modal Title", "key": "ymfamodlxd", "isDeprecated": false, "rightColumn": 41.0, "textAlign": "LEFT", "dynamicHeight": "AUTO_HEIGHT", "widgetId": "lc1ovalrv0", "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1.0, "parentId": "pcni4dljt9", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000.0, "fontSize": "1.25rem", "minDynamicHeight": 4.0}, {"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button2", "onClick": "{{closeModal(Modal1.name)}}", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 18.0, "bottomRow": 22.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "leftColumn": 31.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Close", "isDisabled": false, "key": "g4h46nuarm", "isDeprecated": false, "rightColumn": 47.0, "isDefaultClickDisabled": true, "widgetId": "86cyr9be1d", "buttonStyle": "PRIMARY", "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "pcni4dljt9", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "SECONDARY", "placement": "CENTER"}, {"resetFormOnClick": false, "boxShadow": "none", "widgetName": "Button3", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 18.0, "bottomRow": 22.0, "type": "BUTTON_WIDGET", "hideCard": false, "animateLoading": true, "leftColumn": 47.0, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Confirm", "isDisabled": false, "key": "g4h46nuarm", "isDeprecated": false, "rightColumn": 63.0, "isDefaultClickDisabled": true, "widgetId": "laiw2q2841", "buttonStyle": "PRIMARY_BUTTON", "isVisible": true, "recaptchaType": "V3", "version": 1.0, "parentId": "pcni4dljt9", "renderMode": "CANVAS", "isLoading": false, "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "buttonVariant": "PRIMARY", "placement": "CENTER"}], "isDisabled": false, "key": "pt1a71cj3d", "isDeprecated": false, "rightColumn": 391.5, "detachFromLayout": true, "widgetId": "pcni4dljt9", "isVisible": true, "version": 1.0, "parentId": "xhf80p6oeo", "renderMode": "CANVAS", "isLoading": false}], "key": "0cohv4e2yc", "height": 240.0, "isDeprecated": false, "rightColumn": 44.0, "detachFromLayout": true, "dynamicHeight": "AUTO_HEIGHT", "widgetId": "xhf80p6oeo", "canOutsideClickClose": true, "canEscapeKeyClose": true, "version": 2.0, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "maxDynamicHeight": 9000.0, "width": 456.0, "minDynamicHeight": 24.0}]}, "layoutOnLoadActions": [], "layoutOnLoadActionErrors": [], "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policies": []}, "publishedPage": {"name": "Page1", "slug": "page1", "layouts": [{"viewMode": false, "dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 1224.0, "snapColumns": 16.0, "detachFromLayout": true, "widgetId": "0", "topRow": 0.0, "bottomRow": 1250.0, "containerStyle": "none", "snapRows": 33.0, "parentRowSpace": 1.0, "type": "CANVAS_WIDGET", "canExtend": true, "version": 4.0, "minHeight": 1292.0, "dynamicTriggerPathList": [], "parentColumnSpace": 1.0, "dynamicBindingPathList": [], "leftColumn": 0.0, "children": []}, "validOnPageLoadActions": true, "id": "Page1", "deleted": false, "policies": [], "userPermissions": []}], "userPermissions": [], "policies": []}, "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_630714bdb3409d60b4bf4ab7"}], "actionList": [{"pluginType": "API", "pluginId": "restapi-plugin", "unpublishedAction": {"name": "Rest_Api_1", "datasource": {"name": "<PERSON><PERSON><PERSON>", "pluginId": "restapi-plugin", "messages": [], "isAutoGenerated": false, "id": "<PERSON><PERSON><PERSON>", "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "path": "/users", "headers": [{"key": "content-type", "value": "application/json"}], "encodeParamsToggle": true, "queryParameters": [], "bodyFormData": [], "httpMethod": "GET", "selfReferencingDataPaths": [], "pluginSpecifiedTemplates": [{"value": true}], "formData": {"apiContentType": "application/json"}}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": ["appsmith.URL.queryParams.key", "appsmith.URL.fullPath"], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_Rest_Api_1", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_63105a16e84aab6f3c7d16ba"}, {"pluginType": "API", "pluginId": "graphql-plugin", "unpublishedAction": {"name": "Graphql_Query", "datasource": {"name": "DEFAULT_GRAPHQL_DATASOURCE", "pluginId": "graphql-plugin", "invalids": ["No datasource configuration found. Please configure it and try again."], "messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "headers": [{"key": "content-type", "value": "application/json"}], "encodeParamsToggle": true, "queryParameters": [], "body": "{\n\tname: $name\n}", "httpMethod": "POST", "selfReferencingDataPaths": [], "pluginSpecifiedTemplates": [{"value": true}, {"value": "{\n\tname: \"<PERSON>\",\n\t\n}"}, {"value": {}}], "formData": {"apiContentType": "application/json"}}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_Graphql_Query", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_631eb376a831a05a31ef635b"}, {"pluginType": "JS", "pluginId": "js-plugin", "unpublishedAction": {"name": "myFun1", "fullyQualifiedName": "JSObject2.myFun1", "datasource": {"name": "UNUSED_DATASOURCE", "pluginId": "js-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "collectionId": "Page1_JSObject2", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "encodeParamsToggle": true, "body": "() => {}", "selfReferencingDataPaths": [], "jsArguments": [], "isAsync": false}, "runBehaviour": "MANUAL", "clientSideExecution": true, "dynamicBindingPathList": [{"key": "body"}], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": ["() => {}"], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_JSObject2.myFun1", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_6319753a8eed172d1cad67d5"}, {"pluginType": "API", "pluginId": "restapi-plugin", "unpublishedAction": {"name": "Rest_Api_2", "datasource": {"name": "<PERSON><PERSON><PERSON>", "pluginId": "restapi-plugin", "messages": [], "isAutoGenerated": false, "id": "<PERSON><PERSON><PERSON>", "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "headers": [{"key": "abc"}], "encodeParamsToggle": true, "queryParameters": [], "bodyFormData": [], "httpMethod": "GET", "selfReferencingDataPaths": [], "pluginSpecifiedTemplates": [{"value": true}], "formData": {"apiContentType": "none"}}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_Rest_Api_2", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_6319886c8eed172d1cad6880"}, {"pluginType": "DB", "pluginId": "amazons3-plugin", "unpublishedAction": {"name": "S3_Query", "datasource": {"name": "Appsmith Image Assets", "pluginId": "amazons3-plugin", "messages": [], "isAutoGenerated": false, "id": "Appsmith Image Assets", "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "encodeParamsToggle": true, "selfReferencingDataPaths": [], "formData": {"command": {"data": "LIST"}, "bucket": {"data": "sss"}, "path": {"data": ""}, "create": {"dataType": {"data": "YES"}, "expiry": {"data": "5"}}, "body": {"data": ""}, "list": {"prefix": {"data": ""}, "where": {"data": {"condition": "AND", "children": [{"condition": "EQ"}]}}, "signedUrl": {"data": "NO"}, "expiry": {"data": "5"}, "unSignedUrl": {"data": "YES"}, "sortBy": {"data": [{"column": "", "order": "Ascending"}]}}, "read": {"dataType": {"data": "YES"}}, "smartSubstitution": {"data": true}}}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_S3_Query", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_632c2543ec371b142c33d149"}, {"pluginType": "DB", "pluginId": "postgres-plugin", "unpublishedAction": {"name": "SQL_Query", "datasource": {"name": "Internal DB", "pluginId": "postgres-plugin", "messages": [], "isAutoGenerated": false, "id": "Internal DB", "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "encodeParamsToggle": true, "body": "SELECT * FROM users ORDER BY id LIMIT 10;", "selfReferencingDataPaths": [], "pluginSpecifiedTemplates": [{"value": true}]}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_SQL_Query", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_632c2609ec371b142c33d14f"}, {"pluginType": "DB", "pluginId": "mongo-plugin", "unpublishedAction": {"name": "Mongo_Query", "datasource": {"name": "Movies", "pluginId": "mongo-plugin", "messages": [], "isAutoGenerated": false, "id": "Movies", "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "actionConfiguration": {"paginationType": "NONE", "encodeParamsToggle": true, "selfReferencingDataPaths": [], "formData": {"command": {"data": "FIND"}, "aggregate": {"limit": {"data": "10"}}, "delete": {"limit": {"data": "SINGLE"}}, "updateMany": {"limit": {"data": "SINGLE"}}, "smartSubstitution": {"data": true}, "misc": {"formToNativeQuery": {"data": "{\n  \"find\": \"null\",\n  \"limit\": 10,\n  \"batchSize\": 10\n}\n", "status": "SUCCESS"}}}}, "runBehaviour": "MANUAL", "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_Mongo_Query", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_632c25654b8d9c0c2da23eee"}, {"pluginType": "JS", "pluginId": "js-plugin", "unpublishedAction": {"name": "myFun1", "fullyQualifiedName": "JSObject1.myFun1", "datasource": {"name": "UNUSED_DATASOURCE", "pluginId": "js-plugin", "messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "pageId": "Page1", "collectionId": "Page1_JSObject1", "actionConfiguration": {"timeoutInMillisecond": 10000.0, "paginationType": "NONE", "encodeParamsToggle": true, "body": "() => {}", "selfReferencingDataPaths": [], "jsArguments": [], "isAsync": false}, "runBehaviour": "MANUAL", "dynamicBindingPathList": [{"key": "body"}], "isValid": true, "invalids": [], "messages": [], "jsonPathKeys": ["() => {}"], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "publishedAction": {"datasource": {"messages": [], "isAutoGenerated": false, "deleted": false, "policies": [], "userPermissions": []}, "messages": [], "userSetOnLoad": false, "confirmBeforeExecute": false, "policies": [], "userPermissions": []}, "id": "Page1_JSObject1.myFun1", "deleted": false, "gitSyncId": "6389ced3e9ac34674708eeb7_6389db31e9ac34674708ef8a"}], "actionCollectionList": [{"unpublishedCollection": {"name": "JSObject1", "pageId": "Page1", "pluginId": "js-plugin", "pluginType": "JS", "actions": [], "archivedActions": [], "body": "export default {\n\tmyVar1: [],\n\tmyVar2: {},\n\tmyFun1: () => {\n\t\t\n\t\t//write code here\n\t},\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n}", "variables": [{"name": "myVar1", "value": "[]"}, {"name": "myVar2", "value": "{}"}], "userPermissions": []}, "id": "Page1_JSObject1", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_6319706b93a914550ebf3cc2"}, {"unpublishedCollection": {"name": "JSObject2", "pageId": "Page1", "pluginId": "js-plugin", "pluginType": "JS", "actions": [], "archivedActions": [], "body": "export default {\n\tmyVar1: [],\n\tmyVar2: {},\n\tmyFun1: () => {\n\t\t//write code here\n\t},\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n}", "variables": [{"name": "myVar1", "value": "[]"}, {"name": "myVar2", "value": "{}"}], "userPermissions": []}, "id": "Page1_JSObject2", "deleted": false, "gitSyncId": "630714bdb3409d60b4bf4ab5_6319753a8eed172d1cad67d9"}], "updatedResources": {"actionList": ["Rest_Api_2##ENTITY_SEPARATOR##Page1", "Graphql_Query##ENTITY_SEPARATOR##Page1", "JSObject2.myFun1##ENTITY_SEPARATOR##Page1", "Mongo_Query##ENTITY_SEPARATOR##Page1", "Rest_Api_1##ENTITY_SEPARATOR##Page1", "S3_Query##ENTITY_SEPARATOR##Page1", "JSObject1.myFun1##ENTITY_SEPARATOR##Page1", "SQL_Query##ENTITY_SEPARATOR##Page1"], "pageList": ["Page1"], "actionCollectionList": ["JSObject1##ENTITY_SEPARATOR##Page1", "JSObject2##ENTITY_SEPARATOR##Page1"]}, "editModeTheme": {"name": "<PERSON><PERSON><PERSON>", "displayName": "Modern", "isSystemTheme": true, "deleted": false}, "publishedTheme": {"name": "<PERSON><PERSON><PERSON>", "displayName": "Modern", "isSystemTheme": true, "deleted": false}}