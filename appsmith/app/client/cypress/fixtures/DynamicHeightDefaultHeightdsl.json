{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 490, "containerStyle": "none", "snapRows": 125, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 69, "minHeight": 1292, "dynamicTriggerPathList": [], "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"isVisible": true, "backgroundColor": "#FFFFFF", "widgetName": "Container1", "containerStyle": "card", "borderColor": "#E0DEDE", "borderWidth": "1", "boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "animateLoading": true, "children": [{"isVisible": true, "widgetName": "Canvas1", "version": 1, "detachFromLayout": true, "type": "CANVAS_WIDGET", "hideCard": true, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "v2o6lv3nzy", "containerStyle": "none", "canExtend": false, "children": [{"isVisible": true, "animateLoading": true, "text": "Submit", "buttonVariant": "PRIMARY", "placement": "CENTER", "widgetName": "Button1", "isDisabled": false, "isDefaultClickDisabled": true, "disabledWhenInvalid": false, "resetFormOnClick": false, "recaptchaType": "V3", "version": 1, "searchTags": ["click", "submit"], "type": "BUTTON_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "<PERSON><PERSON>", "key": "314dya6t5f", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "widgetId": "ky4p2dinmv", "renderMode": "CANVAS", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 3.42578125, "parentRowSpace": 10, "leftColumn": 19, "rightColumn": 35, "topRow": 29, "bottomRow": 33, "parentId": "3dktwb98ur", "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}]}], "minHeight": 350, "widgetId": "3dktwb98ur", "renderMode": "CANVAS", "boxShadow": "none", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isLoading": false, "parentColumnSpace": 1, "parentRowSpace": 1, "leftColumn": 0, "rightColumn": 239.25, "topRow": 0, "bottomRow": 350, "parentId": "vu6m5e6y57", "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "accentColor"}]}], "version": 1, "minDynamicHeight": 10, "maxDynamicHeight": 9000, "dynamicHeight": "AUTO_HEIGHT", "shouldScrollContents": true, "searchTags": ["div", "parent", "group"], "type": "CONTAINER_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Container", "key": "48gjvjaig3", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "isCanvas": true, "widgetId": "vu6m5e6y57", "renderMode": "CANVAS", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "isLoading": false, "parentColumnSpace": 9.96875, "parentRowSpace": 10, "leftColumn": 17, "rightColumn": 41, "topRow": 6, "bottomRow": 41, "parentId": "0", "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "originalTopRow": 6, "originalBottomRow": 16}]}}