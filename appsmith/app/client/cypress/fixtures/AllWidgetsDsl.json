{"dsl": {"widgetName": "MainContainer", "backgroundColor": "none", "rightColumn": 4896, "snapColumns": 64, "detachFromLayout": true, "widgetId": "0", "topRow": 0, "bottomRow": 2650, "containerStyle": "none", "snapRows": 124, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "version": 78, "minHeight": 1292, "useAutoLayout": false, "dynamicTriggerPathList": [], "parentColumnSpace": 1, "dynamicBindingPathList": [], "leftColumn": 0, "children": [{"mobileBottomRow": 9, "widgetName": "Audio1", "displayName": "Audio", "iconSVG": "/static/media/icon.cb54df7a09016b0af5e520895be927b9.svg", "searchTags": ["mp3", "sound", "wave", "player"], "topRow": 5, "bottomRow": 9, "parentRowSpace": 10, "type": "AUDIO_WIDGET", "hideCard": false, "mobileRightColumn": 22, "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 0, "dynamicBindingPathList": [], "key": "wepftp423b", "isDeprecated": false, "rightColumn": 22, "widgetId": "cqx0cffadl", "minWidth": 450, "isVisible": true, "version": 1, "url": "https://assets.appsmith.com/widgets/birds_chirping.mp3", "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 5, "responsiveBehavior": "fill", "mobileLeftColumn": 0, "autoPlay": false}, {"resetFormOnClick": false, "boxShadow": "none", "mobileBottomRow": 15, "widgetName": "Button1", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "displayName": "<PERSON><PERSON>", "iconSVG": "/static/media/icon.cca026338f1c8eb6df8ba03d084c2fca.svg", "searchTags": ["click", "submit"], "topRow": 11, "bottomRow": 15, "parentRowSpace": 10, "type": "BUTTON_WIDGET", "hideCard": false, "mobileRightColumn": 19, "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 3, "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "text": "Submit", "isDisabled": false, "key": "hnl12ftpb9", "isDeprecated": false, "rightColumn": 19, "isDefaultClickDisabled": true, "widgetId": "dde68q7diz", "minWidth": 120, "isVisible": true, "recaptchaType": "V3", "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 11, "responsiveBehavior": "hug", "disabledWhenInvalid": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 3, "buttonVariant": "PRIMARY", "placement": "CENTER"}, {"boxShadow": "none", "mobileBottomRow": 21, "widgetName": "ButtonGroup1", "isCanvas": false, "displayName": "Button Group", "iconSVG": "/static/media/icon.d6773218cfb61dcfa5f460d43371e30d.svg", "searchTags": ["click", "submit"], "topRow": 17, "bottomRow": 21, "parentRowSpace": 10, "groupButtons": {"groupButton1": {"label": "Favorite", "iconName": "heart", "id": "groupButton1", "widgetId": "", "buttonType": "SIMPLE", "placement": "CENTER", "isVisible": true, "isDisabled": false, "index": 0, "menuItems": {}, "buttonColor": "{{appsmith.theme.colors.primaryColor}}"}, "groupButton2": {"label": "Add", "iconName": "add", "id": "groupButton2", "buttonType": "SIMPLE", "placement": "CENTER", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 1, "menuItems": {}, "buttonColor": "{{appsmith.theme.colors.primaryColor}}"}, "groupButton3": {"label": "More", "iconName": "more", "id": "groupButton3", "buttonType": "MENU", "placement": "CENTER", "widgetId": "", "isVisible": true, "isDisabled": false, "index": 2, "menuItems": {"menuItem1": {"label": "First Option", "backgroundColor": "#FFFFFF", "id": "menuItem1", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 0}, "menuItem2": {"label": "Second Option", "backgroundColor": "#FFFFFF", "id": "menuItem2", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 1}, "menuItem3": {"label": "Delete", "iconName": "trash", "iconColor": "#FFFFFF", "iconAlign": "right", "textColor": "#FFFFFF", "backgroundColor": "#DD4B34", "id": "menuItem3", "widgetId": "", "onClick": "", "isVisible": true, "isDisabled": false, "index": 2}}, "buttonColor": "{{appsmith.theme.colors.primaryColor}}"}}, "type": "BUTTON_GROUP_WIDGET", "hideCard": false, "mobileRightColumn": 25, "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 1, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "groupButtons.groupButton1.buttonColor"}, {"key": "groupButtons.groupButton2.buttonColor"}, {"key": "groupButtons.groupButton3.buttonColor"}], "key": "lomd6hddy3", "orientation": "horizontal", "isDeprecated": false, "rightColumn": 25, "widgetId": "tdxq4aj65p", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 17, "responsiveBehavior": "fill", "childStylesheet": {"button": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}"}}, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 1, "buttonVariant": "PRIMARY"}, {"mobileBottomRow": 27, "widgetName": "Checkbox1", "displayName": "Checkbox", "iconSVG": "/static/media/icon.********************************.svg", "searchTags": ["boolean"], "topRow": 23, "bottomRow": 27, "parentRowSpace": 10, "type": "CHECKBOX_WIDGET", "alignWidget": "LEFT", "hideCard": false, "mobileRightColumn": 15, "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 3, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelPosition": "Left", "isDisabled": false, "key": "dt2dmbqchf", "isRequired": false, "isDeprecated": false, "rightColumn": 15, "dynamicHeight": "AUTO_HEIGHT", "widgetId": "05fz5frvxf", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "minWidth": 450, "isVisible": true, "label": "Label", "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 23, "responsiveBehavior": "fill", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 3, "defaultCheckedState": true, "maxDynamicHeight": 9000, "minDynamicHeight": 4}, {"mobileBottomRow": 34, "widgetName": "CheckboxGroup1", "displayName": "Checkbox Group", "iconSVG": "/static/media/icon.********************************.svg", "labelText": "Label", "topRow": 28, "bottomRow": 38, "parentRowSpace": 10, "labelWidth": 5, "type": "CHECKBOX_GROUP_WIDGET", "hideCard": false, "mobileRightColumn": 25, "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 2, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelPosition": "Top", "options": [{"label": "Blue", "value": "BLUE"}, {"label": "Green", "value": "GREEN"}, {"label": "Red", "value": "RED"}], "isDisabled": false, "key": "ugac8kwnmi", "labelTextSize": "0.875rem", "isRequired": false, "isDeprecated": false, "rightColumn": 25, "defaultSelectedValues": ["BLUE"], "dynamicHeight": "AUTO_HEIGHT", "widgetId": "amwq9rkvss", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "version": 2, "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 28, "originalTopRow": 28, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 2, "maxDynamicHeight": 9000, "originalBottomRow": 38, "isInline": true, "minDynamicHeight": 4}, {"boxShadow": "none", "dateFormat": "YYYY-MM-DD HH:mm", "iconSVG": "/static/media/icon.300e5ab8e2e1c26c7a0bad06116842b7.svg", "topRow": 40, "labelWidth": 5, "type": "DATE_PICKER_WIDGET2", "animateLoading": true, "leftColumn": 2, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "isDisabled": false, "isRequired": false, "dynamicHeight": "FIXED", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "datePickerType": "DATE_PICKER", "version": 2, "isLoading": false, "timePrecision": "minute", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "originalBottomRow": 47, "closeOnSelection": true, "mobileBottomRow": 47, "widgetName": "DatePicker1", "minDate": "1920-12-31T18:30:00.000Z", "displayName": "DatePicker", "searchTags": ["calendar"], "bottomRow": 47, "shortcuts": false, "parentRowSpace": 10, "hideCard": false, "mobileRightColumn": 22, "parentColumnSpace": 10.03125, "labelPosition": "Top", "key": "325umfw0jz", "labelTextSize": "0.875rem", "defaultDate": "2023-04-13T16:51:33.585Z", "isDeprecated": false, "rightColumn": 22, "widgetId": "08k5ekcorx", "minWidth": 450, "label": "Label", "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "mobileTopRow": 40, "responsiveBehavior": "fill", "originalTopRow": 40, "mobileLeftColumn": 2, "maxDynamicHeight": 9000, "firstDayOfWeek": 0, "maxDate": "2121-12-31T18:29:00.000Z", "minDynamicHeight": 4}, {"boxShadow": "none", "iconSVG": "/static/media/icon.9f505595da61a34f563dba82adeb06ec.svg", "topRow": 49, "labelWidth": 5, "type": "INPUT_WIDGET_V2", "animateLoading": true, "resetOnSubmit": true, "leftColumn": 3, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelStyle": "", "inputType": "TEXT", "isDisabled": false, "isRequired": false, "dynamicHeight": "FIXED", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "showStepArrows": false, "isVisible": true, "version": 2, "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "originalBottomRow": 56, "mobileBottomRow": 56, "widgetName": "Input1", "displayName": "Input", "searchTags": ["form", "text input", "number", "textarea"], "bottomRow": 56, "parentRowSpace": 10, "autoFocus": false, "hideCard": false, "mobileRightColumn": 23, "parentColumnSpace": 10.03125, "labelPosition": "Top", "key": "6hdoxb99f2", "labelTextSize": "0.875rem", "isDeprecated": false, "rightColumn": 23, "widgetId": "zkk2j3slsu", "minWidth": 450, "label": "Label", "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "mobileTopRow": 49, "responsiveBehavior": "fill", "originalTopRow": 49, "mobileLeftColumn": 3, "maxDynamicHeight": 9000, "iconAlign": "left", "defaultText": "", "minDynamicHeight": 4}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "requiresFlatWidgetChildren": true, "isCanvas": true, "iconSVG": "/static/media/icon.9925ee17dee37bf1ba7374412563a8a7.svg", "topRow": 58, "pageSize": 3, "type": "LIST_WIDGET_V2", "itemSpacing": 8, "animateLoading": true, "dynamicBindingPathList": [{"key": "currentItemsView"}, {"key": "<PERSON><PERSON><PERSON><PERSON>ie<PERSON>"}, {"key": "triggeredItemView"}, {"key": "primaryKeys"}, {"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}], "leftColumn": 1, "enhancements": true, "children": [{"mobileBottomRow": 400, "widgetName": "Canvas1", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 400, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "dropDisabled": true, "openParentPropertyPane": true, "minHeight": 400, "mobileRightColumn": 240.75, "noPad": true, "parentColumnSpace": 1, "leftColumn": 0, "dynamicBindingPathList": [], "children": [{"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "mobileBottomRow": 12, "widgetName": "Container1", "borderColor": "#E0DEDE", "disallowCopy": true, "isCanvas": true, "displayName": "Container", "iconSVG": "/static/media/icon.1977dca3370505e2db3a8e44cfd54907.svg", "searchTags": ["div", "parent", "group"], "topRow": 0, "bottomRow": 12, "dragDisabled": true, "type": "CONTAINER_WIDGET", "hideCard": false, "shouldScrollContents": false, "isDeletable": false, "mobileRightColumn": 64, "animateLoading": true, "leftColumn": 0, "dynamicBindingPathList": [{"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"widgetName": "Canvas2", "displayName": "<PERSON><PERSON>", "topRow": 0, "bottomRow": 120, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": false, "hideCard": true, "useAutoLayout": false, "parentColumnSpace": 1, "leftColumn": 0, "dynamicBindingPathList": [], "children": [{"boxShadow": "none", "mobileBottomRow": 8, "widgetName": "Image1", "displayName": "Image", "iconSVG": "/static/media/icon.52d8fb963abcb95c79b10f1553389f22.svg", "topRow": 0, "bottomRow": 8, "type": "IMAGE_WIDGET", "hideCard": false, "mobileRightColumn": 16, "animateLoading": true, "dynamicTriggerPathList": [], "imageShape": "RECTANGLE", "dynamicBindingPathList": [{"key": "image"}, {"key": "borderRadius"}], "leftColumn": 0, "defaultImage": "http://host.docker.internal:4200/clouddefaultImage.png", "key": "1d8czar35l", "image": "{{currentItem.img}}", "isDeprecated": false, "rightColumn": 16, "objectFit": "cover", "widgetId": "abgaodgyhx", "isVisible": true, "version": 1, "parentId": "zp7wqq5ndn", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "maxZoomLevel": 1, "enableDownload": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 0, "enableRotation": false}, {"boxShadow": "none", "mobileBottomRow": 4, "widgetName": "Text1", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 0, "bottomRow": 4, "type": "TEXT_WIDGET", "hideCard": false, "mobileRightColumn": 28, "animateLoading": true, "overflow": "NONE", "dynamicTriggerPathList": [], "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "dynamicBindingPathList": [{"key": "text"}, {"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "leftColumn": 16, "shouldTruncate": false, "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "text": "{{currentItem.name}}", "key": "y05kp45w45", "isDeprecated": false, "rightColumn": 28, "textAlign": "LEFT", "dynamicHeight": "FIXED", "widgetId": "1bf9w049x1", "minWidth": 450, "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "zp7wqq5ndn", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "responsiveBehavior": "fill", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 16, "maxDynamicHeight": 9000, "fontSize": "1rem", "textStyle": "HEADING", "minDynamicHeight": 4}, {"boxShadow": "none", "mobileBottomRow": 8, "widgetName": "Text2", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 4, "bottomRow": 8, "type": "TEXT_WIDGET", "hideCard": false, "mobileRightColumn": 24, "animateLoading": true, "overflow": "NONE", "dynamicTriggerPathList": [], "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "dynamicBindingPathList": [{"key": "text"}, {"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "leftColumn": 16, "shouldTruncate": false, "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "text": "{{currentItem.id}}", "key": "y05kp45w45", "isDeprecated": false, "rightColumn": 24, "textAlign": "LEFT", "dynamicHeight": "FIXED", "widgetId": "fhda7vbt1s", "minWidth": 450, "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "zp7wqq5ndn", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 4, "responsiveBehavior": "fill", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 16, "maxDynamicHeight": 9000, "fontSize": "1rem", "textStyle": "BODY", "minDynamicHeight": 4}], "key": "4n2p4ppe93", "isDeprecated": false, "detachFromLayout": true, "widgetId": "zp7wqq5ndn", "containerStyle": "none", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "qmmk26pieg", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "responsiveBehavior": "fill", "mobileLeftColumn": 0, "flexLayers": []}], "borderWidth": "1", "positioning": "fixed", "key": "4pw4gqptcn", "backgroundColor": "white", "isDeprecated": false, "rightColumn": 64, "dynamicHeight": "FIXED", "widgetId": "qmmk26pieg", "containerStyle": "card", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "0lys7d9whp", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "responsiveBehavior": "fill", "noContainerOffset": true, "disabledWidgetFeatures": ["dynamicHeight"], "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 0, "maxDynamicHeight": 9000, "minDynamicHeight": 10}], "key": "4n2p4ppe93", "isDeprecated": false, "rightColumn": 240.75, "detachFromLayout": true, "widgetId": "0lys7d9whp", "containerStyle": "none", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "lp5w5fpgbw", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "responsiveBehavior": "fill", "mobileLeftColumn": 0, "flexLayers": []}], "itemBackgroundColor": "#FFFFFF", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "hasMetaWidgets": true, "isLoading": false, "mainCanvasId": "0lys7d9whp", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "originalBottomRow": 98, "additionalStaticProps": ["level", "levelData", "prefixMetaWidgetId", "metaWidgetId"], "mobileBottomRow": 98, "currentItemsView": "{{[]}}", "triggeredItemView": "{{{}}}", "widgetName": "List1", "listData": [{"id": "001", "name": "Blue", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "002", "name": "Green", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}, {"id": "003", "name": "Red", "img": "http://host.docker.internal:4200/clouddefaultImage.png"}], "displayName": "List", "bottomRow": 98, "parentRowSpace": 10, "hideCard": false, "templateBottomRow": 16, "mobileRightColumn": 25, "mainContainerId": "qmmk26pieg", "primaryKeys": "{{List1.listData.map((currentItem, currentIndex) => currentItem[\"id\"] )}}", "parentColumnSpace": 10.03125, "gridType": "vertical", "key": "mra6j9j9ml", "backgroundColor": "transparent", "isDeprecated": false, "rightColumn": 25, "widgetId": "lp5w5fpgbw", "minWidth": 450, "parentId": "0", "renderMode": "CANVAS", "mobileTopRow": 58, "responsiveBehavior": "fill", "originalTopRow": 58, "mobileLeftColumn": 1, "selectedItemView": "{{{}}}"}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "mobileBottomRow": 116, "widgetName": "Tabs1", "borderColor": "#E0DEDE", "isCanvas": true, "displayName": "Tabs", "iconSVG": "/static/media/icon.74a6d653c8201e66f1cd367a3fba2657.svg", "topRow": 101, "bottomRow": 120, "parentRowSpace": 10, "type": "TABS_WIDGET", "hideCard": false, "shouldScrollContents": true, "mobileRightColumn": 25, "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 1, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}], "children": [{"tabId": "tab1", "mobileBottomRow": 150, "widgetName": "Canvas3", "displayName": "<PERSON><PERSON>", "bottomRow": 150, "topRow": 0, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "hideCard": true, "shouldScrollContents": false, "minHeight": 150, "mobileRightColumn": 240.75, "parentColumnSpace": 1, "leftColumn": 0, "dynamicBindingPathList": [], "children": [], "isDisabled": false, "key": "4n2p4ppe93", "isDeprecated": false, "tabName": "Tab 1", "rightColumn": 240.75, "detachFromLayout": true, "widgetId": "hxi8t9w9jh", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "fw9bl8utai", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "responsiveBehavior": "fill", "mobileLeftColumn": 0, "flexLayers": []}, {"tabId": "tab2", "mobileBottomRow": 150, "widgetName": "Canvas4", "displayName": "<PERSON><PERSON>", "bottomRow": 150, "topRow": 0, "parentRowSpace": 1, "type": "CANVAS_WIDGET", "canExtend": true, "hideCard": true, "shouldScrollContents": false, "minHeight": 150, "mobileRightColumn": 240.75, "parentColumnSpace": 1, "leftColumn": 0, "dynamicBindingPathList": [], "children": [], "isDisabled": false, "key": "4n2p4ppe93", "isDeprecated": false, "tabName": "Tab 2", "rightColumn": 240.75, "detachFromLayout": true, "widgetId": "uyhoargjdb", "minWidth": 450, "isVisible": true, "version": 1, "parentId": "fw9bl8utai", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 0, "responsiveBehavior": "fill", "mobileLeftColumn": 0, "flexLayers": []}], "borderWidth": 1, "key": "n5ws0upilc", "backgroundColor": "#FFFFFF", "isDeprecated": false, "rightColumn": 25, "dynamicHeight": "AUTO_HEIGHT", "widgetId": "fw9bl8utai", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "defaultTab": "Tab 1", "shouldShowTabs": true, "minWidth": 450, "tabsObj": {"tab1": {"label": "Tab 1", "id": "tab1", "widgetId": "hxi8t9w9jh", "isVisible": true, "index": 0, "positioning": "vertical"}, "tab2": {"label": "Tab 2", "id": "tab2", "widgetId": "uyhoargjdb", "isVisible": true, "index": 1, "positioning": "vertical"}}, "isVisible": true, "version": 3, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 101, "responsiveBehavior": "fill", "originalTopRow": 101, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 1, "maxDynamicHeight": 9000, "originalBottomRow": 120, "minDynamicHeight": 15}, {"boxShadow": "{{appsmith.theme.boxShadow.appBoxShadow}}", "borderColor": "#E0DEDE", "isVisibleDownload": true, "iconSVG": "/static/media/icon.db8a9cbd2acd22a31ea91cc37ea2a46c.svg", "topRow": 123, "isSortable": true, "type": "TABLE_WIDGET_V2", "inlineEditingSaveOption": "ROW_LEVEL", "animateLoading": true, "dynamicBindingPathList": [{"key": "primaryColumns.step.computedValue"}, {"key": "primaryColumns.task.computedValue"}, {"key": "primaryColumns.status.computedValue"}, {"key": "primaryColumns.action.computedValue"}, {"key": "primaryColumns.action.buttonColor"}, {"key": "primaryColumns.action.borderRadius"}, {"key": "primaryColumns.action.boxShadow"}, {"key": "accentColor"}, {"key": "borderRadius"}, {"key": "boxShadow"}], "needsHeightForContent": true, "leftColumn": 0, "delimiter": ",", "defaultSelectedRowIndex": 0, "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisibleFilters": true, "isVisible": true, "enableClientSideSearch": true, "version": 1, "totalRecordsCount": 0, "isLoading": false, "childStylesheet": {"button": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "menuButton": {"menuColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "iconButton": {"buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none"}, "editActions": {"saveButtonColor": "{{appsmith.theme.colors.primaryColor}}", "saveBorderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "discardButtonColor": "{{appsmith.theme.colors.primaryColor}}", "discardBorderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}"}}, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "columnUpdatedAt": 1681404693628, "originalBottomRow": 151, "defaultSelectedRowIndices": [0], "mobileBottomRow": 151, "widgetName": "Table1", "defaultPageSize": 0, "columnOrder": ["step", "task", "status", "action"], "dynamicPropertyPathList": [{"key": "tableData"}], "displayName": "Table", "bottomRow": 151, "columnWidthMap": {"task": 245, "step": 70, "status": 85}, "parentRowSpace": 10, "hideCard": false, "mobileRightColumn": 25, "parentColumnSpace": 10.03125, "borderWidth": "1", "primaryColumns": {"step": {"index": 0, "width": 150, "id": "step", "originalId": "step", "alias": "step", "allowSameOptionsInNewRow": true, "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isCellVisible": true, "isCellEditable": false, "isDerived": false, "label": "step", "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"step\"]))}}", "validation": {}, "sticky": "", "labelColor": "#FFFFFF"}, "task": {"index": 1, "width": 150, "id": "task", "originalId": "task", "alias": "task", "allowSameOptionsInNewRow": true, "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isCellVisible": true, "isCellEditable": false, "isDerived": false, "label": "task", "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"task\"]))}}", "validation": {}, "sticky": "", "labelColor": "#FFFFFF"}, "status": {"index": 2, "width": 150, "id": "status", "originalId": "status", "alias": "status", "allowSameOptionsInNewRow": true, "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "text", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isCellVisible": true, "isCellEditable": false, "isDerived": false, "label": "status", "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"status\"]))}}", "validation": {}, "sticky": "", "labelColor": "#FFFFFF"}, "action": {"index": 3, "width": 150, "id": "action", "originalId": "action", "alias": "action", "allowSameOptionsInNewRow": true, "horizontalAlignment": "LEFT", "verticalAlignment": "CENTER", "columnType": "button", "textSize": "0.875rem", "enableFilter": true, "enableSort": true, "isVisible": true, "isCellVisible": true, "isCellEditable": false, "isDisabled": false, "isDerived": false, "label": "action", "onClick": "{{currentRow.step === '#1' ? showAlert('Done', 'success') : currentRow.step === '#2' ? navigateTo('https://docs.appsmith.com/core-concepts/connecting-to-data-sources/querying-a-database',undefined,'NEW_WINDOW') : navigateTo('https://docs.appsmith.com/core-concepts/displaying-data-read/display-data-tables',undefined,'NEW_WINDOW')}}", "computedValue": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( currentRow[\"action\"]))}}", "validation": {}, "sticky": "", "labelColor": "#FFFFFF", "buttonColor": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( appsmith.theme.colors.primaryColor))}}", "borderRadius": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( appsmith.theme.borderRadius.appBorderRadius))}}", "boxShadow": "{{Table1.processedTableData.map((currentRow, currentIndex) => ( 'none'))}}"}}, "key": "a3cbp6h4ou", "canFreezeColumn": true, "isDeprecated": false, "rightColumn": 25, "textSize": "0.875rem", "widgetId": "fnmc1e2pk5", "minWidth": 450, "tableData": [{"step": "#1", "task": "Drop a table", "status": "✅", "action": ""}, {"step": "#2", "task": "Create a query fetch_users with the Mock DB", "status": "--", "action": ""}, {"step": "#3", "task": "Bind the query using => fetch_users.data", "status": "--", "action": ""}], "label": "Data", "searchKey": "", "parentId": "0", "renderMode": "CANVAS", "mobileTopRow": 123, "horizontalAlignment": "LEFT", "isVisibleSearch": true, "responsiveBehavior": "fill", "originalTopRow": 123, "mobileLeftColumn": 0, "isVisiblePagination": true, "verticalAlignment": "CENTER"}, {"mobileBottomRow": 157, "widgetName": "Text3", "displayName": "Text", "iconSVG": "/static/media/icon.97c59b523e6f70ba6f40a10fc2c7c5b5.svg", "searchTags": ["typography", "paragraph", "label"], "topRow": 158, "bottomRow": 162, "parentRowSpace": 10, "type": "TEXT_WIDGET", "hideCard": false, "mobileRightColumn": 20, "animateLoading": true, "overflow": "NONE", "fontFamily": "{{appsmith.theme.fontFamily.appFont}}", "parentColumnSpace": 10.03125, "leftColumn": 4, "dynamicBindingPathList": [{"key": "truncateButtonColor"}, {"key": "fontFamily"}, {"key": "borderRadius"}], "shouldTruncate": false, "truncateButtonColor": "{{appsmith.theme.colors.primaryColor}}", "text": "Label", "key": "y05kp45w45", "isDeprecated": false, "rightColumn": 20, "textAlign": "LEFT", "dynamicHeight": "AUTO_HEIGHT", "widgetId": "h3dbin57yv", "minWidth": 450, "isVisible": true, "fontStyle": "BOLD", "textColor": "#231F20", "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 153, "responsiveBehavior": "fill", "originalTopRow": 158, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 4, "maxDynamicHeight": 9000, "originalBottomRow": 162, "fontSize": "1rem", "minDynamicHeight": 4}, {"boxShadow": "none", "iconSVG": "/static/media/icon.bd99caba5853ad71e4b3d8daffacb3a2.svg", "labelText": "Label", "topRow": 162, "labelWidth": 5, "type": "SINGLE_SELECT_TREE_WIDGET", "defaultOptionValue": "BLUE", "animateLoading": true, "leftColumn": 1, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "options": [{"label": "Blue", "value": "BLUE", "children": [{"label": "Dark Blue", "value": "DARK BLUE"}, {"label": "Light Blue", "value": "LIGHT BLUE"}]}, {"label": "Green", "value": "GREEN"}, {"label": "Red", "value": "RED"}], "placeholderText": "Select option", "isDisabled": false, "isRequired": false, "dynamicHeight": "FIXED", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "version": 1, "expandAll": false, "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "originalBottomRow": 169, "mobileBottomRow": 166, "widgetName": "TreeSelect1", "displayName": "TreeSelect", "searchTags": ["dropdown"], "bottomRow": 169, "parentRowSpace": 10, "hideCard": false, "mobileRightColumn": 21, "parentColumnSpace": 10.03125, "labelPosition": "Top", "key": "k5l967u58j", "labelTextSize": "0.875rem", "isDeprecated": false, "rightColumn": 21, "widgetId": "wht5ecmxqi", "minWidth": 450, "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "mobileTopRow": 159, "responsiveBehavior": "fill", "originalTopRow": 162, "mobileLeftColumn": 1, "maxDynamicHeight": 9000, "allowClear": false, "minDynamicHeight": 4}, {"boxShadow": "none", "iconSVG": "/static/media/icon.bd99caba5853ad71e4b3d8daffacb3a2.svg", "labelText": "Label", "topRow": 176, "labelWidth": 5, "type": "SELECT_WIDGET", "serverSideFiltering": false, "defaultOptionValue": "GREEN", "animateLoading": true, "leftColumn": 0, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "options": [{"label": "Blue", "value": "BLUE"}, {"label": "Green", "value": "GREEN"}, {"label": "Red", "value": "RED"}], "placeholderText": "Select option", "isDisabled": false, "isRequired": false, "dynamicHeight": "FIXED", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "version": 1, "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "originalBottomRow": 183, "mobileBottomRow": 175, "widgetName": "Select1", "isFilterable": true, "displayName": "Select", "searchTags": ["dropdown"], "bottomRow": 183, "parentRowSpace": 10, "hideCard": false, "mobileRightColumn": 20, "parentColumnSpace": 10.03125, "labelPosition": "Top", "key": "b4rxm5sj7b", "labelTextSize": "0.875rem", "isDeprecated": false, "rightColumn": 20, "widgetId": "aqr4irzrrv", "minWidth": 450, "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "mobileTopRow": 168, "responsiveBehavior": "fill", "originalTopRow": 176, "mobileLeftColumn": 0, "maxDynamicHeight": 9000, "minDynamicHeight": 4}, {"mobileBottomRow": 180, "widgetName": "Progress1", "progressType": "linear", "isCanvas": false, "displayName": "Progress", "iconSVG": "/static/media/icon.9b0d7b96a0223e8120bf6f14aca4154a.svg", "searchTags": ["percent"], "topRow": 183, "bottomRow": 187, "parentRowSpace": 10, "type": "PROGRESS_WIDGET", "isIndeterminate": false, "hideCard": false, "fillColor": "{{appsmith.theme.colors.primaryColor}}", "mobileRightColumn": 12, "parentColumnSpace": 10.03125, "leftColumn": 0, "dynamicBindingPathList": [{"key": "fillColor"}, {"key": "borderRadius"}], "key": "vf31g9mqgd", "showResult": false, "isDeprecated": false, "rightColumn": 12, "counterClosewise": false, "widgetId": "njusl29yfq", "isVisible": true, "steps": 1, "version": 1, "parentId": "0", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 176, "responsiveBehavior": "fill", "originalTopRow": 183, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "mobileLeftColumn": 0, "originalBottomRow": 187, "progress": 50}, {"boxShadow": "none", "mobileBottomRow": 174, "widgetName": "RadioGroup1", "displayName": "Radio Group", "iconSVG": "/static/media/icon.ba2b2ee006e51a5c681d7964d7777481.svg", "searchTags": ["choice"], "topRow": 169, "bottomRow": 176, "parentRowSpace": 10, "labelWidth": 5, "type": "RADIO_GROUP_WIDGET", "hideCard": false, "mobileRightColumn": 20, "defaultOptionValue": "Y", "animateLoading": true, "parentColumnSpace": 10.03125, "leftColumn": 0, "dynamicBindingPathList": [{"key": "accentColor"}], "labelPosition": "Top", "options": [{"label": "Yes", "value": "Y"}, {"label": "No", "value": "N"}], "isDisabled": false, "key": "wmcaxth6a9", "labelTextSize": "0.875rem", "isRequired": false, "isDeprecated": false, "rightColumn": 20, "dynamicHeight": "AUTO_HEIGHT", "widgetId": "44ygbjj04r", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "label": "Label", "version": 1, "parentId": "0", "labelAlignment": "left", "renderMode": "CANVAS", "isLoading": false, "mobileTopRow": 168, "originalTopRow": 169, "mobileLeftColumn": 0, "maxDynamicHeight": 9000, "originalBottomRow": 176, "isInline": true, "alignment": "left", "minDynamicHeight": 4}, {"boxShadow": "none", "iconSVG": "/static/media/icon.108789d7165de30306435ab3c24e6cad.svg", "topRow": 151, "labelWidth": 5, "type": "PHONE_INPUT_WIDGET", "animateLoading": true, "resetOnSubmit": true, "leftColumn": 2, "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "labelStyle": "", "isDisabled": false, "isRequired": false, "dynamicHeight": "FIXED", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isVisible": true, "version": 1, "isLoading": false, "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "originalBottomRow": 158, "mobileBottomRow": 158, "widgetName": "PhoneInput1", "displayName": "Phone Input", "searchTags": ["call"], "bottomRow": 158, "parentRowSpace": 10, "defaultDialCode": "+1", "autoFocus": false, "hideCard": false, "mobileRightColumn": 22, "parentColumnSpace": 10.03125, "labelPosition": "Top", "key": "oyvsfwa64l", "labelTextSize": "0.875rem", "isDeprecated": false, "rightColumn": 22, "widgetId": "njnmrr1p3a", "allowDialCodeChange": false, "minWidth": 450, "label": "Label", "parentId": "0", "labelAlignment": "left", "allowFormatting": true, "renderMode": "CANVAS", "mobileTopRow": 151, "responsiveBehavior": "fill", "originalTopRow": 151, "mobileLeftColumn": 2, "maxDynamicHeight": 9000, "iconAlign": "left", "defaultText": "", "minDynamicHeight": 4}, {"isVisible": true, "iconColor": "white", "isDisabled": false, "widgetName": "AudioRecorder1", "version": 1, "animateLoading": true, "responsiveBehavior": "fill", "minWidth": 450, "searchTags": ["sound recorder", "voice recorder"], "type": "AUDIO_RECORDER_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Audio Recorder", "key": "dqdbpm8bby", "iconSVG": "/static/media/icon.3cb03cd8ed8464c5725a5d89a8fa563f.svg", "widgetId": "pmchgp8yir", "renderMode": "CANVAS", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 10.03125, "parentRowSpace": 10, "leftColumn": 0, "rightColumn": 11, "topRow": 188, "bottomRow": 195, "mobileLeftColumn": 0, "mobileRightColumn": 11, "mobileTopRow": 188, "mobileBottomRow": 195, "parentId": "0", "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "originalTopRow": 188, "originalBottomRow": 195}, {"isVisible": true, "options": [{"label": "xs", "value": "xs"}, {"label": "sm", "value": "sm"}, {"label": "md", "value": "md"}, {"label": "lg", "value": "lg"}, {"label": "xl", "value": "xl"}], "defaultOptionValue": "md", "isDisabled": false, "showMarksLabel": true, "widgetName": "CategorySlider1", "shouldScroll": false, "shouldTruncate": false, "version": 1, "animateLoading": true, "labelText": "Size", "labelPosition": "Top", "labelAlignment": "left", "labelWidth": 5, "labelTextSize": "0.875rem", "sliderSize": "m", "responsiveBehavior": "fill", "searchTags": ["range"], "type": "CATEGORY_SLIDER_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Category Slider", "key": "3wrvvh1c3l", "iconSVG": "/static/media/icon.cbd0db7a0bd317a6e4cbbd72417f8dee.svg", "widgetId": "x9oi5temu5", "renderMode": "CANVAS", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "isLoading": false, "parentColumnSpace": 10.03125, "parentRowSpace": 10, "leftColumn": 0, "rightColumn": 13, "topRow": 202, "bottomRow": 210, "mobileLeftColumn": 0, "mobileRightColumn": 13, "mobileTopRow": 202, "mobileBottomRow": 210, "parentId": "0", "dynamicBindingPathList": [{"key": "accentColor"}], "originalTopRow": 202, "originalBottomRow": 210}, {"isVisible": true, "label": "Scan a QR/Barcode", "widgetName": "CodeScanner1", "isDefaultClickDisabled": true, "scannerLayout": "ALWAYS_ON", "version": 1, "isRequired": false, "isDisabled": false, "animateLoading": true, "placement": "CENTER", "responsiveBehavior": "fill", "searchTags": ["barcode scanner", "qr scanner", "code detector", "barcode reader"], "type": "CODE_SCANNER_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Code Scanner", "key": "sjlcv43n5e", "iconSVG": "/static/media/icon.c4a2da6d32c0f212d031fca147e4f7cb.svg", "widgetId": "oooxe4e3py", "renderMode": "CANVAS", "buttonColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 10.03125, "parentRowSpace": 10, "leftColumn": 0, "rightColumn": 9, "topRow": 211, "bottomRow": 244, "mobileLeftColumn": 0, "mobileRightColumn": 9, "mobileTopRow": 211, "mobileBottomRow": 244, "parentId": "0", "dynamicBindingPathList": [{"key": "buttonColor"}, {"key": "borderRadius"}], "originalTopRow": 211, "originalBottomRow": 244}, {"isVisible": true, "label": "Label", "labelPosition": "Top", "labelAlignment": "left", "labelTextSize": "0.875rem", "labelWidth": 5, "widgetName": "CurrencyInput1", "version": 1, "defaultText": "", "iconAlign": "left", "autoFocus": false, "labelStyle": "", "resetOnSubmit": true, "isRequired": false, "isDisabled": false, "animateLoading": true, "responsiveBehavior": "fill", "minWidth": 450, "allowCurrencyChange": false, "defaultCurrencyCode": "USD", "decimals": 0, "showStepArrows": false, "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "FIXED", "searchTags": ["amount", "total"], "type": "CURRENCY_INPUT_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Currency Input", "key": "5mg580qdsi", "iconSVG": "/static/media/icon.f312efcb48ce4dafb08c20291635b30b.svg", "widgetId": "9xdnjn3qgb", "renderMode": "CANVAS", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "borderRadius": "{{appsmith.theme.borderRadius.appBorderRadius}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 10.03125, "parentRowSpace": 10, "leftColumn": 0, "rightColumn": 12, "topRow": 246, "bottomRow": 253, "mobileLeftColumn": 0, "mobileRightColumn": 12, "mobileTopRow": 246, "mobileBottomRow": 253, "parentId": "0", "dynamicBindingPathList": [{"key": "accentColor"}, {"key": "borderRadius"}], "originalTopRow": 246, "originalBottomRow": 253}, {"isVisible": true, "widgetName": "Divider1", "orientation": "horizontal", "capType": "nc", "capSide": 0, "strokeStyle": "solid", "dividerColor": "#858282", "thickness": 2, "version": 1, "animateLoading": true, "responsiveBehavior": "fill", "minWidth": 450, "searchTags": ["line"], "type": "DIVIDER_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Divider", "key": "twm3yk5168", "iconSVG": "/static/media/icon.cbe8f608ca868e1eb44607e5fbd4a9e5.svg", "widgetId": "k5upd9vy58", "renderMode": "CANVAS", "isLoading": false, "parentColumnSpace": 10.03125, "parentRowSpace": 10, "leftColumn": 1, "rightColumn": 21, "topRow": 255, "bottomRow": 259, "mobileLeftColumn": 1, "mobileRightColumn": 21, "mobileTopRow": 255, "mobileBottomRow": 259, "parentId": "0", "dynamicBindingPathList": [], "originalTopRow": 255, "originalBottomRow": 259}, {"isVisible": true, "label": "Label", "defaultSwitchState": true, "widgetName": "Switch1", "alignWidget": "LEFT", "labelPosition": "Left", "version": 1, "isDisabled": false, "animateLoading": true, "responsiveBehavior": "fill", "minDynamicHeight": 4, "maxDynamicHeight": 9000, "dynamicHeight": "AUTO_HEIGHT", "searchTags": ["boolean"], "type": "SWITCH_WIDGET", "hideCard": false, "isDeprecated": false, "displayName": "Switch", "key": "8e3s4utmwy", "iconSVG": "/static/media/icon.a3115bc1c224776de2846985c8819f99.svg", "widgetId": "qvodqx5f71", "renderMode": "CANVAS", "accentColor": "{{appsmith.theme.colors.primaryColor}}", "boxShadow": "none", "isLoading": false, "parentColumnSpace": 10.03125, "parentRowSpace": 10, "leftColumn": 1, "rightColumn": 13, "topRow": 261, "bottomRow": 265, "mobileLeftColumn": 1, "mobileRightColumn": 13, "mobileTopRow": 261, "mobileBottomRow": 265, "parentId": "0", "dynamicBindingPathList": [{"key": "accentColor"}], "originalTopRow": 261, "originalBottomRow": 265}], "positioning": "fixed"}}