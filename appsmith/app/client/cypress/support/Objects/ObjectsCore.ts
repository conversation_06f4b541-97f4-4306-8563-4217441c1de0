import { WIDGET, WIDGETSKIT } from "../../locators/WidgetLocators";
import { ObjectsRegistry } from "../Objects/Registry";
import { EntityItems } from "../Pages/AssertHelper";

export const agHelper = ObjectsRegistry.AggregateHelper;
export const assertHelper = ObjectsRegistry.AssertHelper;
export const locators = ObjectsRegistry.CommonLocators;
export const entityExplorer = ObjectsRegistry.EntityExplorer;
export const jsEditor = ObjectsRegistry.JSEditor;
export const propPane = ObjectsRegistry.PropertyPane;
export const deployMode = ObjectsRegistry.DeployMode;
export const appSettings = ObjectsRegistry.AppSettings;
export const generalSettings = ObjectsRegistry.GeneralSettings;
export const pageSettings = ObjectsRegistry.PageSettings;
export const embedSettings = ObjectsRegistry.EmbedSettings;
export const homePage = ObjectsRegistry.HomePage;
export const theme = ObjectsRegistry.ThemeSettings;
export const gitSync = ObjectsRegistry.GitSync;
export const apiPage = ObjectsRegistry.ApiPage;
export const adminSettings = ObjectsRegistry.AdminSettings;
export const dataSources = ObjectsRegistry.DataSources;
export const inviteModal = ObjectsRegistry.InviteModal;
export const table = ObjectsRegistry.Table;
export const debuggerHelper = ObjectsRegistry.DebuggerHelper;
export const templates = ObjectsRegistry.Templates;
export const peekOverlay = ObjectsRegistry.PeekOverlay;
export const installer = ObjectsRegistry.LibraryInstaller;
export const onboarding = ObjectsRegistry.Onboarding;
export const autoLayout = ObjectsRegistry.AutoLayout;
export const draggableWidgets = WIDGET;
export const fakerHelper = ObjectsRegistry.FakerHelper;
export const dataManager = ObjectsRegistry.DataManager;
export const entityItems = EntityItems;
export const tabs = ObjectsRegistry.Tabs;
export const gsheetHelper = ObjectsRegistry.GSheetHelper;
export const widgetLocators = WIDGETSKIT;
export const communityTemplates = ObjectsRegistry.CommunityTemplates;
export const anvilLayout = ObjectsRegistry.AnvilLayout;
export const wdsWidgets = ObjectsRegistry.WDSWidgets;
export const partialImportExport = ObjectsRegistry.PartialImportExport;
export const anvilSnapshot = ObjectsRegistry.AnvilSnapshot;
