export default {
  curlImportBtn: ".t--importBtn",
  createBlankApiCard: ".t--createBlankApiCard",
  eachProviderCard: ".t--eachProviderCard",
  nameOfApi: ".t--nameOfApi",
  addToPageBtn: ".t--addToPageBtn",
  ApiActionMenu: "[data-testid=\"t--more-action-trigger\"]",
  ApiDeleteBtn: ".t--apiFormDeleteBtn",
  ApiRunBtn: "[data-testid=\"t--run-action\"]",
  addToPageBtnsId: ".t--addToPageButtons",
  ApiHomePage: ".t--apiHomePage",
  formActionButtons: ".t--formActionButtons",
  dataSourceField: ".t--dataSourceField",
  responseBody: ".CodeMirror-code  span.cm-string.cm-property",
  ApiVerb: ".t--apiFormHttpMethod div",
  apiPaginationNextText: ".t--apiFormPaginationNext",
  apiPaginationPrevText: ".t--apiFormPaginationPrev",
  apiPaginationPrevTest: ".t--apiFormPaginationPrevTest",
  apiPaginationNextTest: ".t--apiFormPaginationNextTest",
  apiPaginationTab:
    ".t--apiFormPaginationType label:contains('Paginate with response URL') input",
  apiTab: ".react-tabs__tab-list li",
  bodyType: ".t--apiFormPostBodyType",
  bodyTypeSelected: "[data-testid=\"t--api-body-tab-switch\"] .rc-select-selection-item",
  bodyTab: "Body",
  headersTab: "Header",
  httpDropDownOptions: ".rc-select-item",
  codeEditorWrapper: ".t--code-editor-wrapper",
  apiSearchHint: ".datasource-hint",
  slashCommandButton: ".commands-button",
  apiResponseObject: ".object-key",
  apiResponseTabsList: ".ads-v2-tabs__list",
};
