{"datasourcesCard": "t--datasource", "host": "input[name$='.datasourceConfiguration.endpoints[0].host']", "port": "input[name$='.datasourceConfiguration.endpoints[0].port']", "databaseName": "input[name$='.datasourceConfiguration.authentication.databaseName']", "username": "input[name$='.datasourceConfiguration.authentication.username']", "password": "input[name$='.datasourceConfiguration.authentication.password']", "headers": "input[placeholder='Authorization Header']", "authenticationAuthtype": "[data-testid$=.datasourceConfiguration\\.authentication\\.authType]", "url": "input[name='url']", "MongoDB": ".t--plugin-name:contains('MongoDB')", "RESTAPI": ".t--plugin-name:contains('REST API')", "PostgreSQL": ".t--plugin-name:contains('PostgreSQL')", "SMTP": ".t--plugin-name:contains('SMTP')", "MySQL": ".t--plugin-name:contains('MySQL')", "GoogleSheets": ".t--plugin-name:contains('Google Sheets')", "sectionAuthentication": "[data-testid=section-Authentication] .t--collapse-section-container", "activeDatasourceList": ".t--active-datasource-list", "datasourceCard": ".t--datasource", "datasourceCardMenu": ".t--datasource-menu-option", "datasourceCardGeneratePageBtn": ".t--generate-template, .t--datasource-generate-page", "datasourceMenuOptionEdit": "t--datasource-option-edit", "datasourceMenuOptionDelete": "t--datasource-option-delete", "editDatasource": ".t--edit-datasource", "datasourceTitle": ".t--edit-datasource-name .bp3-editable-text-content", "datasourceTitleLocator": ".t--edit-datasource-name", "datasourceTitleInputLocator": ".t--edit-datasource-name input", "defaultDatabaseName": "input[name$='.datasourceConfiguration.connection.defaultDatabaseName']", "datasourceConfigurationProperty": "input[name$='.datasourceConfiguration.properties[0]']", "googleSheets": ".t--plugin-name:contains('Google Sheets')", "selConnectionType": "[data-testid$='.datasourceConfiguration.connection.type']", "scope": "[data-testid='authentication.scopeString']", "Mysql": ".t--plugin-name:contains('Mysql')", "ElasticSearch": ".t--plugin-name:contains('Elasticsearch')", "DynamoDB": ".t--plugin-name:contains('DynamoDB')", "Redis": ".t--plugin-name:contains('Red<PERSON>')", "MsSQL": ".t--plugin-name:contains('Microsoft SQL Server')", "ArangoDB": ".t--plugin-name:contains('ArangoDB')", "Firestore": ".t--plugin-name:contains('Firestore')", "Redshift": ".t--plugin-name:contains('Redshift')", "AmazonS3": ".t--plugin-name:contains('S3')", "authType": "[data-testid=authType]", "OAuth2": ".rc-select-item-option-content:contains('OAuth 2.0')", "accessTokenUrl": "[data-testid='authentication.accessTokenUrl'] input", "clienID": "[data-testid='authentication.clientId'] input", "clientSecret": "[data-testid='authentication.clientSecret'] input", "datasourceConfigUrl": "[data-testid$='.datasourceConfiguration.url'] input", "projectID": "[data-testid$='.datasourceConfiguration.authentication.username'] input", "serviceAccCredential": "[data-testid$='.datasourceConfiguration.authentication.password'] input", "grantType": "[data-testid='authentication.grantType']", "authorizationURL": "[data-testid='authentication.authorizationUrl'] input", "authorizationCode": ".rc-select-item-option-content:contains('Authorization Code')", "clientCredentials": ".rc-select-item-option-content:contains('Client Credentials')", "clientAuthentication": "[data-testid='authentication.isAuthorizationHeader']", "sendClientCredentialsInBody": ".rc-select-item-option-content:contains('Send client credentials in body')", "scopeString": "[data-testid$='.datasourceConfiguration.authentication.scopeString']", "GS_readFiles": "[data-testid='t--dropdown-option-Read Files']", "GS_readAndEditFiles": "[data-testid='t--dropdown-option-Read, Edit and Create Files']", "GS_readEditCreateAndDeleteFiles": "[data-testid='t--dropdown-option-Read, Edit, Create and Delete Files']", "saveAndAuthorize": "button:contains('Save and Authorize')", "basic": "//div[contains(@class,'option') and text()='Basic']", "basicUsername": "input[name='authentication.username']", "basicPassword": "input[name='authentication.password']", "mockUserDatabase": "div[id='mock-database'] span:contains('Users')", "mockUserDatasources": ".t--plugin-name:contains('Users')", "mongoUriDropdown": "//p[text()='Use mongo connection string URI']/following-sibling::div", "mongoUriYes": "//div[text()='Yes']", "mongoUriInput": "//p[text()='Connection string URI']/following-sibling::div//input", "advancedSettings": "[data-testid='section-Advanced Settings'] .t--collapse-section-container", "useSelfSignedCert": "[data-testid='connection.ssl.authTypeControl']", "useCertInAuth": "[data-testid='authentication.useSelfSignedCert']", "certificateDetails": "[data-testid='section-Certificate Details'] .t--collapse-section-container", "saveBtn": ".t--save-datasource", "gSheetsOperationDropdown": "[data-testid='actionConfiguration.formData.command.data']", "gSheetsEntityDropdown": "[data-testid='actionConfiguration.formData.entityType.data']", "gSheetsInsertOneOption": ".rc-select-item-option-content:contains('Insert One')", "gSheetsSheetRowsOption": ".rc-select-item-option-content:contains('Sheet Row(s)')", "gSheetsCodeMirrorPlaceholder": ".CodeMirror-placeholder", "connectionSettingsSection": "[data-testid='section-Connection'] .t--collapse-section-container", "authenticationSettingsSection": "[data-testid='section-Authentication'] .t--collapse-section-container", "sslSettingsSection": "[data-testid='section-SSL (optional)'] .t--collapse-section-container"}