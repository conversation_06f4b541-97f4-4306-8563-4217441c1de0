{"settings": ".t--edit-column-btn", "propertyName": ".t--code-editor-wrapper", "jsformInput": ".t--jsonformfield-name input", "jsformDOB": ".t--json<PERSON>field-date_of_birth input", "jsformEmpID": ".t--jsonformfield-employee_id input", "msg": ".bp3-popover-content", "submit": "button:contains('Submit')", "mandatoryAsterisk": ".t--jsonformfield-name div :contains('*')", "mandatoryFieldControl": ".t--property-control-required .ads-v2-switch", "keyInput": ".t--jsonformfield-Key input", "emailField": "//input[@type='email']", "switchStatus": ".t--switch-widget-active", "calendarPopup": ".DayPicker-Months", "datepickerContainer": "//div[@data-testid='datepicker-container']"}