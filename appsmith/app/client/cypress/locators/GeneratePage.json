{"selectDatasourceDropdown": "[data-testid=t--datasource-dropdown]", "datasourceDropdownOption": "[data-testid=t--datasource-dropdown-option]", "selectTableDropdown": "[data-testid=t--table-dropdown]", "dropdownOption": ".rc-select-item-option-content", "selectSearchColumnDropdown": "[data-testid=t--searchColumn-dropdown]", "generatePageFormSubmitBtn": "[data-testid=t--generate-page-form-submit]", "selectRowinTable": "//div[text()='CRUD User2']/ancestor::div[contains(@class,'tr')]", "currentStatusField": "//div[@type='FORM_WIDGET']//span[text()='Status']//ancestor::div[contains(@class,'t--widget-textwidget')]/following-sibling::div[contains(@class, 't--widget-inputwidgetv2')][1]//input", "updateBtn": "span:contains('Update')", "addRowIcon": "//span[@icon='add']/ancestor::div[1]", "idField": ".t--jsonformfield-id", "nameField": ".t--j<PERSON><PERSON>field-name", "statusField": ".t--jsonformfield-status", "genderField": ".t--j<PERSON><PERSON>field-gender", "emailField": ".t--jsonformfield-email", "submitBtn": "span:contains('Submit')", "sortByDropdown": "span[name='dropdown']", "ascending": "//div[text()='Ascending']", "descending": "//div[text()='Descending']", "currentNameField": "//div[@type='FORM_WIDGET']//span[text()='Name']//ancestor::div[contains(@class,'t--widget-textwidget')]/following-sibling::div[contains(@class, 't--widget-inputwidgetv2')][1]//input", "deleteofSelectedRow": "//div[@class='tr selected-row']//span[text()='Delete']", "confirmBtn": "span:contains('Confirm')", "deleteMenuItem": "//div[text()='Delete']/parent::a[contains(@class, 'single-select')]", "uploadFilesS3": "div.uppy-Dashboard-AddFiles input", "uploadBtn": "button.uppy-StatusBar-actionBtn--upload", "selectedRow": ".tr.selected-row", "searchinTable": "//input[@placeholder='Search...']"}