{"editIcon": ".t--widget-propertypane-toggle", "helpIcon": ".t--widget-help-control", "editPropCrossButton": ".t--property-pane-close-btn", "editPropBackButton": "[data-testid='t--property-pane-back-btn']", "deleteWidgetIcon": ".t--widget-delete-control", "dropdownSelectButton": ".t--open-dropdown-Select-Action", "crossbutton": ".t--property-pane-close-btn", "dropdownAction": ".t--open-dropdown-Select-Action", "datatypedropdown": ".t--property-control-datatype button", "Alerttypedropdown": ".t--open-dropdown-Select-type", "dropdownmenu": ".rc-select-item-option-content", "containerInnerText": ".t--draggable-containerwidget span.t--widget-name", "optionchangetextDropdown": " .t--property-control-onoptionchange .CodeMirror textarea", "optionchangetextDatePicker": " .t--property-control-ondateselected .CodeMirror-code", "optionchangetextCheckbox": ".t--property-control-oncheckchange .CodeMirror-code", "optionchangetextSwitch": ".t--property-control-onchange .CodeMirror textarea", "optionchangetextInput": ".t--property-control-ontextchanged .CodeMirror-code", "optionchangeRadioselect": ".t--property-control-onselectionchange .CodeMirror textarea", "optionalignment": ".t--property-control-alignment input", "optionposition": ".t--property-control-position span:contains('Right')", "optionpositionL": ".t--property-control-position span:contains('Left')", "onMarkerclick": ".t--property-control-onmarkerclick .CodeMirror-code", "success": "div[type='success'] span", "mapOptionChange": ".t--property-control-onmarkerclick .CodeMirror-code", "dropdownbuttonclick": ".bp3-button", "menuSelection": ".bp3-button > .bp3-button-text", "scrollView": ".t--property-control-scrollcontents input", "InputforText": ".t--property-control-text .CodeMirror-code", "TextInside": ".bp3-ui-text span", "homeIcon": ".t--appsmith-logo", "typeWidgetName": ".bp3-editable-text-editing>input", "requiredCheckbox": ".t--property-control-required input[type='checkbox']", "visibleCheckbox": ".t--property-control-visible input[type='checkbox']", "allowSelectAllCheckbox": ".t--property-control-allowselectall input[type='checkbox']", "selectAllOptions": ".t--property-control-selectalloptions input[type='checkbox']", "disableCheckbox": ".t--property-control-disabled input[type='checkbox']", "hideToolbarCheckbox": ".t--property-control-hidetoolbar input[type='checkbox']", "labelTextStyle": ".bp3-ui-text span", "bodyTextStyle": ".bp3-ui-text span", "headingTextStyle": ".bp3-ui-text span", "propertyPaneTitle": ".t--property-pane-title", "dropDownIcon": ".t--property-control-fontsize input", "onDateSelectedField": ".t--property-control-ondateselected", "TableRow": ".t--draggable-tablewidget .tbody", "TableV2Row": ".t--draggable-tablewidgetv2 .tbody", "TableV2Head": ".t--draggable-tablewidgetv2 .thead", "Disablejs": ".t--property-control-disabled", "spellCheck": ".t--property-control-spellcheck", "requiredjs": ".t--property-control-required", "allowScroll": ".t--property-control-allowscroll input", "tableInner": ".t--draggable-tablewidget span.t--widget-name", "tableV2Inner": ".t--draggable-tablewidgetv2 span.t--widget-name", "buttonInner": ".t--draggable-buttonwidget span.t--widget-name", "inputWidgetInner": ".t--draggable-inputwidgetv2 span.t--widget-name", "pdfSupport": ".t--property-control-pdfexport input", "ExcelSupport": ".t--property-control-excelexport input", "dropDownBtn": "input", "disabledField": " .bp3-disabled", "disabledBtn": " button[disabled='disabled']", "rteToolbar": " .tox-toolbar__primary", "inputField": " .bp3-input", "csvSupport": ".t--property-control-csvexport input", "backToEditor": ".t--back-to-editor", "enableSearchLocCheckbox": ".t--property-control-enablesearchlocation input", "enablePickLocCheckbox": ".t--property-control-enablepicklocation input", "enableCreateMarkerCheckbox": ".t--property-control-createnewmarker input", "widgetNameTag": "span.t--widget-name", "serverSidePaginationCheckbox": ".t--property-control-serversidepagination input", "rightArrowBtn": "span[icon='chevron-right']", "toastMsg": "div.Toastify__toast", "callApi": ".t--property-control-onpagechange .t--open-dropdown-Select-Action", "singleSelectMenuItem": ".bp3-menu-item.single-select div", "singleSelectWidgetMenuItem": ".menu-item-link", "singleSelectWidgetButtonControl": ".bp3-button.select-button", "singleSelectActiveMenuItem": ".menu-item-active div", "selectInputSearch": ".select-popover-wrapper .bp3-input", "multiSelectMenuItem": "rc-select-item.rc-select-item-option div", "selectMenuItem": ".bp3-menu li>a>div", "evaluatedTypeTitle": ".t--CodeEditor-evaluatedValue > p:first-of-type", "evaluatedType": ".t--CodeEditor-evaluatedValue > div:first-of-type pre", "evaluatedCurrentValue": "div:last-of-type .t--CodeEditor-evaluatedValue > div:last-of-type pre", "entityExplorersearch": "#entity-explorer-search", "saveStatusContainer": ".t--save-status-container", "statusSaving": ".t--save-status-is-saving", "saveStatusError": ".t--save-status-error", "selectWidgetVirtualList": ".menu-virtual-list div", "tableNextPage": ".t--table-widget-next-page", "tablePrevPage": ".t--table-widget-prev-page", "toastmsg": ".Toastify__toast-body span", "copyWidget": "[data-testid='t--copy-widget']", "deleteWidget": "[data-testid='t--delete-widget']", "dropTarget": ".t--drop-target", "toastAction": "div.Toastify__toast", "toastBody": ".Toastify__toast-body", "videoInner": ".t--draggable-videowidget span.t--widget-name", "audioInner": ".t--draggable-audiowidget span.t--widget-name", "onPlay": ".t--property-control-onplay .t--open-dropdown-Select-Action", "chooseAction": ".single-select", "chooseMsgType": ".t--open-dropdown-Select-type", "onPause": ".t--property-control-onpause .t--open-dropdown-Select-Action", "chooseWidget": ".t--open-dropdown-Select-widget", "onClick": ".t--property-control-onclick .t--open-dropdown-Select-Action", "changeZoomlevel": ".t--property-control-maxzoomlevel input", "selectedZoomlevel": ".t--property-control-maxzoomlevel .rc-select-selection-item span", "imgWidget": "div[data-testid='styledImage']", "editColTitle": ".t--property-pane-title", "editColText": ".t--property-pane-title span", "changeColType": ".t--property-control-columntype input", "changeSortBy": ".t--property-control-sortby input", "selectedColType": ".t--property-control-columntype button span", "collapsesection": ".t--property-pane-section-collapse-general .bp3-icon", "selectTab": ".t--tab-Tab", "layoutControls": ".t--layout-control-wrapper > .ads-v2-segmented-control div", "layoutPopover": ".bp3-popover.bp3-minimal.layout-control", "canvas": ".t--canvas-artboard", "deflautSelectedRow": ".t--property-control-defaultselectedrow textarea", "defaultSearchText": ".t--property-control-defaultsearchtext textarea", "entityItem": ".t--entity-item", "entityCollapseToggle": ".t--entity-collapse-toggle", "entityContextMenu": ".t--context-menu", "entityContextMenuContent": ".t--entity-context-menu", "filePickerButton": ".t--widget-filepickerwidget", "filePickerInput": ".uppy-Dash<PERSON>-input", "filePickerUploadButton": ".uppy-StatusBar-actionBtn--upload", "filePickerRemoveButton": ".uppy-Dashboard-Item-action--remove", "AddMoreFiles": ".uppy-DashboardContent-addMoreCaption", "filePickerOnFilesSelected": ".t--property-control-onfilesselected", "dataType": ".t--property-control-datatype", "recaptchaVersion": ".t--property-control-googlerecaptchaversion", "recaptchaVersionText": ".t--property-control-googlerecaptchaversion span.rc-select-selection-item span", "filePickerDataFormat": ".t--property-control-dataformat", "helperText": ".t--property-control-helperText", "jsonFormFieldType": ".t--property-control-fieldtype", "jsonFormAddNewCustomFieldBtn": ".t--property-control-fieldconfiguration .t--add-column-btn", "evaluateMsg": ".t--evaluatedPopup-error", "globalSearchModal": "[data-testid='t--global-search-modal']", "globalSearchInput": ".t--global-search-input", "globalSearchTrigger": ".t--global-search-modal-trigger", "globalSearchClearInput": ".t--global-clear-input", "containerWidget": ".t--widget-containerwidget", "paginationButton": ".rc-pagination-item", "switchWidgetActive": ".t--switch-widget-active", "switchWidgetInActive": ".t--switch-widget-inactive", "switchWidgetLoading": ".t--switch-widget-loading", "tablePageSizeChangeAction": ".t--property-control-onpagesizechange .t--open-dropdown-Select-Action", "listWidgetNameTag": ".bp3-editable-text-content", "currencyType": ".t--property-control-currency  input", "decimalType": ".t--property-control-decimals  input", "allowCurrencyChange": ".t--property-control-allowcurrencychange input[type='checkbox']", "inputCurrencyChangeType": ".t--input-currency-change", "inputCountryCodeChangeType": ".t--input-country-code-change", "viewerPage": "[data-testid='t--app-viewer-page']", "dropDownOptSelected": "//span[@type='p1']", "generalChevran": ".t--property-pane-section-collapse-general .t--chevron-icon", "generalSection": ".t--property-pane-section-general", "selectedIcon": ".t--property-control-icon .icon-select-container .bp3-button .bp3-button-text", "isSortable": ".t--property-control-columnsorting input[type='checkbox']", "isSortable_tablev1": ".t--property-control-sortable input[type='checkbox']", "labelSection": ".cm-keyword", "lintWarning": ".CodeMirror-lint-mark-warning", "lintErrorMsg": ".CodeMirror-lint-message-error", "lintWarningMsg": ".CodeMirror-lint-message-warning", "labelSectionTxt": ".CodeMirror-code .cm-variable", "lintError": ".CodeMirror-lint-mark-error", "debugger": ".t--debugger-count", "errorTab": "[data-testid=t--tab-ERROR_TAB]", "logsTab": "[data-testid=t--tab-LOGS_TAB]", "debugErrorMsg": "[data-testid=t--debugger-log-message]", "tableButtonVariant": ".t--property-control-buttonvariant", "debuggerLabel": "span.debugger-label", "debuggerToggle": "[data-testid=t--debugger-toggle]", "debuggerDownStreamErrMsg": "[data-testid=t--debugger-downStreamErrorMsg]", "debuggerDownStreamErrCode": "[data-testid=t--debugger-downStreamErrorCode]", "debuggerDescription": ".debugger-description", "cyclicDependencyError": "//div[@class='Toastify']//span[contains(text(),'Cyclic dependency found while evaluating')]", "openDocumentationfromErrorTab": "//span[text()='Open documentation']", "selectInner": ".t--draggable-selectwidget span.t--widget-name, Select1", "toastifyError": "//div[@class='Toastify']//span]", "selectButton": ".select-button", "selectwidget": ".t--draggable-selectwidget", "textWidget": ".t--draggable-textwidget", "filepickerv2": ".t--draggable-filepickerwidgetv2", "dashboardItemName": ".uppy-Dashboard-Item-name", "mapChartShowLabels": ".t--property-control-showlabels input", "changeThemeBtn": ".t--change-theme-btn", "editThemeBtn": ".t--edit-theme-btn", "themeCard": ".t--theme-card", "saveThemeBtn": ".t--save-theme-btn", "selectThemeBackBtn": ".t--theme-select-back-btn", "themeAppBorderRadiusBtn": ".t--theme-appBorderRadius", "themeAppBoxShadowBtn": ".t--theme-appBoxShadow", "lazyCodeEditorFallback": ".t--lazyCodeEditor-fallback", "lazyCodeEditorRendered": ".t--lazyCodeEditor-editor", "textWidgetContainer": ".t--text-widget-container", "propertyStyle": "button[role='tab'] span:contains('Style')", "propertyContent": "button[role='tab'] span:contains('Content')", "cancelActionExecution": ".t--cancel-action-button", "menuButtonMenuItemsSource": ".t--property-control-menuitemssource", "menuButtonSourceData": ".t--property-control-sourcedata", "menuButtonConfigureArrayItems": ".t--property-control-configuremenuitems button", "codeScannerScannerLayout": ".t--property-control-scannerlayout", "codeScannerVideo": ".code-scanner-camera-container video", "codeScannerDisabledSVGIcon": ".code-scanner-camera-container div[disabled] svg", "layoutHeightDropdown": ".t--property-control-height .remixicon-icon", "fixed": "Fixed", "autoHeight": "Auto Height", "autoHeightWithLimits": "Auto Height with limits", "heightDropdown": "[data-guided-tour-iid='dynamicHeight']", "minHeight": "minheight\\(inrows\\)", "maxHeight": "maxheight\\(inrows\\)", "overlayMin": "[data-testid='t--auto-height-overlay-min']", "overlayMax": "[data-testid='t--auto-height-overlay-max']", "addOption": ".t--property-control-options-add", "showTabsControl": ".t--property-control-showtabs input", "checkboxIndicator": ".t--draggable-checkboxwidget input", "generalSectionHeight": ".t--property-pane-section-general .t--property-control-label:contains('Height')", "listPaginateNextButton": ".t--widget-listwidgetv2 .rc-pagination-next .rc-pagination-item-link", "listPaginateActivePage": ".t--widget-listwidgetv2 .rc-pagination-item-active", "listPaginatePrevButton": ".t--widget-listwidgetv2 .rc-pagination-prev .rc-pagination-item-link", "listPaginateNextButtonDisabled": ".t--widget-listwidgetv2 .rc-pagination-disabled .rc-pagination-item-link", "PropertyPaneSearchInput": ".t--property-pane-search-input-wrapper input", "BorderRadius0px": ".t--property-control-borderradius .ads-v2-segmented-control-value-0px", "EnableFormatting": ".t--property-control-enableformatting input[type='checkbox']", "autoConvert": "#t--layout-conversion-cta", "convert": "button:contains('Convert layout')", "refreshApp": "button:contains('Refresh the app')", "autoConversionDialog": "span:contains('Use snapshot')", "useSnapshot": "button:contains('Use snapshot')", "useSnapshotBanner": "span:contains('Use snapshot')", "discardSnapshot": "span:contains('Discard snapshot')", "desktopOption": "//span[@data-value = 'DESKTOP']", "mobileOption": "//span[@data-value = 'MOBILE']", "convertanyways": "button:contains('Convert anyways')", "discard": "button:contains('Discard')", "heightProperty": ".rc-select-single input", "heightPropertyOption": ".rc-select-item-option-content span", "filePickerMaxNoOfFiles": ".t--property-control-maxno\\.offiles .CodeMirror-code", "allowclearingValueInput": ".t--property-control-allowclearingvalue input[type='checkbox']", "allowcountrycodechangeInput": ".t--property-control-allowcountrycodechange input[type='checkbox']", "allowsearchingInput": ".t--property-control-allowsearching input", "allowsearchingInputTypeCheckbox": ".t--property-control-allowsearching input[type='checkbox']", "clientSideSearch": ".t--property-control-clientsidesearch input[type='checkbox']", "enableClientSideSearch": ".t--property-control-enableclientsidesearch input[type='checkbox']", "fixedFooterInput": ".t--property-control-fixedfooter input", "tostifyIcon": ".Toastify--animate-icon > span", "downloadFileType": "button[class*='t--open-dropdown-Select-file-type'] > span:first-of-type", "listToggle": "[data-testid='t--list-toggle']", "showBindingsMenu": "//*[@id='entity-properties-container']"}