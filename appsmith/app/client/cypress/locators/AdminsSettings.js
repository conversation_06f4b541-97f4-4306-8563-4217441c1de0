export default {
  Tabs: ".t--settings-category-list",
  profileTab: ".t--settings-category-profile",
  generalTab: ".t--settings-category-general",
  authenticationTab: ".t--settings-category-authentication",
  emailTab: ".t--settings-category-email",
  userSettingsTab: ".t--settings-category-user-settings",
  instanceSettingsTab: ".t--settings-category-instance-settings",
  configurationTab: ".t--settings-category-configuration",
  googleButton: ".t--settings-sub-category-google-auth",
  githubButton: ".t--settings-sub-category-github-auth",
  formloginButton: ".t--settings-sub-category-form-login",
  readMoreLink: ".t--read-more-link",
  subTextLink: ".t--sub-text-link",
  versionTab: ".t--settings-category-version",
  backButton: ".t--admin-settings-back-button",
  saveButton: ".t--admin-settings-save-button",
  resetButton: ".t--admin-settings-reset-button",
  textInput: ".t--admin-settings-text-input input",
  instanceName: ".t--admin-settings-instanceName input",
  adminEmails: ".t--admin-settings-APPSMITH_ADMIN_EMAILS input",
  fromAddress: ".t--admin-settings-APPSMITH_MAIL_FROM input",
  restartNotice: ".t--admin-settings-restart-notice",
  appsmithStarting: "p:contains('Appsmith is starting')",
  appsmithLogo: ".t--appsmith-logo",
  appsmithHeader: "[data-testid='t--appsmith-page-header']",
  loginWithGoogle: "[data-testid='login-with-Google']",
  loginWithGithub: "[data-testid='login-with-Github']",
  disconnectBtn: "[data-testid='disconnect-service-button']",
  formSignupDisabled: "[data-testid='isSignupDisabled']",
  formLoginEnabled: "[data-testid='isFormLoginEnabled']",
  embedSettings: ".t--admin-settings-APPSMITH_ALLOWED_FRAME_ANCESTORS",
  upgrade: "[data-testid='t--button-upgrade']",
  accessControl: ".t--settings-category-access-control",
  auditLogs: ".t--settings-category-audit-logs",
  provisioning: ".t--settings-category-provisioning",
  upgrageLeftPane: "[data-testid='t--enterprise-settings-category-item-be']",
  branding: ".t--settings-category-branding",
  brandingSubmitButton: ".t--settings-branding-submit-button",
  businessTag: "[data-testid='t--business-tag']",
  enterpriseTag: "[data-testid='t--enterprise-tag']",
  enableEmailVerificationInput: "[data-testid='emailVerificationEnabled']",
  adminSettingsCallout: "[data-testid='admin-settings-group-link']",
  hideWatermarkWrapper: ".t--admin-settings-hideWatermark",
  hideWatermarkInput: "[data-testid='hideWatermark']",
  showRolesAndGroupsWrapper: ".t--admin-settings-showRolesAndGroups",
  showRolesAndGroupsInput: "[data-testid='showRolesAndGroups']",
  singleSessionPerUserWrapper: ".t--admin-settings-singleSessionPerUserEnabled",
  singleSessionPerUserInput: "[data-testid='singleSessionPerUserEnabled']",
  sessionTimeoutWrapper: ".t--admin-settings-userSessionTimeoutInMinutes",
  sessionTimeoutInput: "[name='userSessionTimeoutInMinutes']",
  adminEmailsData: ".t--admin-settings-APPSMITH_ADMIN_EMAILS span > span",
  LeftPaneBrandingLink: ".t--settings-category-branding",
  AdminSettingsColorInput: ".t--settings-brand-color-input input[type=text]",
  AdmingSettingsLogoInput: ".t--settings-brand-logo-input input[type=file]",
  AdmingSettingsLogoInputImage: ".t--settings-brand-logo-input img",
  AdmingSettingsFaviconInput:
    ".t--settings-brand-favicon-input input[type=file]",
  AdmingSettingsFaviconInputImage: ".t--settings-brand-favicon-input img",
  BrandingBg: ".t--branding-bg",
  BrandingLogo: ".t--branding-logo",
  BrandingFavicon: "img.t--branding-favicon",
  BrandingFaviconHead: "link.t--branding-favicon",
  dashboardAppTab: ".t--apps-tab",
  createNewAppButton: ".t--new-button",
  loginContainer: ".t--login-container",
  signupLink: ".t--signup-link",
  authContainer: ".t--auth-container",
  submitButton: "button[type='submit']",
  appsmithLogo: ".t--appsmith-logo",
  appsmithLogoImg: ".t--appsmith-logo img",
  AdminSettingsColorInputShades: ".t--color-input-shades",
  businessTag: ".business-tag",
  upgradeBanner: ".upgrade-banner",
  upgradeButton: "[data-testid='t--button-upgrade']",
  smtpAppsmithMailHostInput: "[name='APPSMITH_MAIL_HOST']",
  smtpAppsmithMailPortInput: "[name='APPSMITH_MAIL_PORT']",
  smtpAppsmithMailFromInput: "[name='APPSMITH_MAIL_FROM']",
  smtpAppsmithMailReplyToInput: "[name='APPSMITH_REPLY_TO']",
  smtpAppsmithMailUserNameInput: "[name='APPSMITH_MAIL_USERNAME']",
  smtpAppsmithMailPasswordInput: "[name='APPSMITH_MAIL_PASSWORD']",
  smtpAppsmithMailTestButton: "[data-testid='admin-settings-button']",
  addEmailGhostInput:
    "[data-testid='admin-settings-tag-input'] .bp3-input-ghost",
};
