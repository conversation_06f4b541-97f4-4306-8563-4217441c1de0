{"canvas": "#canvas-selection-0", "border": ".t--theme-appBorderRadius", "popover": ".rc-tooltip-inner", "shadow": ".t--theme-appBoxShadow", "color": ".t--property-pane-sidebar .bp3-popover-target .cursor-pointer", "inputColor": ".t--colorpicker-v2-popover [data-testid='t--color-picker-input']", "colorPicker": "[data-testid='color-picker']", "greenColor": "[style='background-color: rgb(21, 128, 61);']", "fontsSelected": ".leading-normal", "currentTheme": ".cursor-pointer:contains('Applied theme')", "purpleColor": "[style='background-color: rgb(107,114,128);']", "featuredThemeSection": "[data-testid='t--featured-themes']", "fontOption": ".rc-virtual-list .rc-select-item-option"}