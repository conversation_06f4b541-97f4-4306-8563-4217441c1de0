{"buttonWidget": ".t--widget-buttonwidget", "iconWidgetBtn": ".t--widget-iconbuttonwidget button", "textWidget": ".t--widget-textwidget", "richTextEditorWidget": ".t--widget-richtexteditorwidget", "datepickerWidget": ".t--widget-datepic<PERSON>widget", "backToEditor": ".t--back-to-editor", "inputWidget": ".t--widget-inputwidgetv2", "iconWidget": ".t--widget-iconwidget", "checkboxWidget": ".t--widget-checkboxwidget", "switchwidget": ".t--widget-switchwidget", "radioWidget": ".t--widget-radiogroupwidget", "checkboxGroupWidget": ".t--widget-checkboxgroupwidget", "switchGroupWidget": ".t--widget-switchgroupwidget", "formWidget": ".t--widget-formwidget", "imageWidget": ".t--widget-imagewidget", "selectwidget": ".t--widget-selectwidget", "multiselectwidgetv2": ".t--widget-multiselectwidgetv2", "multiselecttreewidget": ".t--widget-multiselecttreewidget", "singleselecttreewidget": ".t--widget-singleselecttreewidget", "tabWidget": ".t--widget-tabswidget", "jsonFormWidget": ".t--widget-j<PERSON><PERSON><PERSON><PERSON>t", "chartWidget": ".t--widget-chartwidget", "tableWidget": ".t--widget-tablewidget", "chartCanvasVal": ".t--widget-chartwidget svg rect", "mapWidget": ".t--widget-mapwidget", "mapChartWidget": ".t--widget-mapchartwidget", "mapChartWidgetContainer": "[data-testid='t--map-chart-container']", "tableLength": ".t--widget-tablewidget .tbody", "tableV2Length": ".t--widget-tablewidgetv2 .tbody", "mapSearch": ".t--widget-mapwidget input", "pickMyLocation": ".t--widget-mapwidget div[title='Pick My Location']", "rectChart": ".t--widget-chartwidget svg rect", "chartLab": ".t--widget-chartwidget svg g text", "searchInput": ".t--search-input", "downloadBtn": ".t--table-download-btn", "filterBtn": ".t--table-filter-toggle-btn", "applyFiltersBtn": ".t--apply-filter-btn", "attributeDropdown": ".t--table-filter-columns-dropdown", "attributeValue": ".t--dropdown-option", "conditionDropdown": ".t--table-filter-conditions-dropdown", "inputValue": ".t--table-filter-value-input", "tableFilterInputValue": ".t--table-filter-value-input input", "canvas": ".canvas", "removeFilter": ".t--table-filter-remove-btn", "rowHeight": ".t--property-control-rowheight .bp3-popover-target", "rowHeightOpt": ".t--table-compact-mode-option", "visibilityMode": ".t--table-column-visibility-toggle-btn", "visibilityOpt": ".option-title", "containerWidget": ".t--widget-containerwidget", "pageInfo": ".bp3-heading", "inputGrp": ".bp3-input-group input", "datePickerNew": ".t--widget-datepickerwidget2", "tab": ".t--tab-Tab", "downloadOption": ".t--table-download-data-option", "addFilter": ".t--add-filter-btn", "operatorsDropdown": ".t--table-filter-operators-dropdown", "attributesDropdown": ".t--table-filter-columns-dropdown", "codescannerwidget": ".t--widget-codescannerwidget"}