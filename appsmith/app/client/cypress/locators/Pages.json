{"commonWidgets": ".t--page-sidebar-CommonWidgets", "formWidgets": ".t--page-sidebar-FormWidgets", "viewWidgets": ".t--page-sidebar-ViewWidgets", "widgetsEditor": ".t--nav-link-widgets-editor", "Menuaction": ".bp3-overlay-open>.bp3-transition-container", "Delete": ":nth-child(2) > .bp3-menu-item", "apiEditorIcon": ".t--nav-link-api-editor", "integrationActiveTab": "button[id$='ACTIVE']", "popover": "//div[contains(@class,'t--entity page')]//*[last()]//*[local-name()='g' and @id='Icon/Outline/more-vertical']", "editName": "span:contains('Rename')", "clonePage": "span:contains('Clone')", "deletePage": "span:contains('Delete')", "deletePageConfirm": "span:contains('Are you sure?')", "hidePage": "span:contains('Hide')", "showPage": "span:contains('Show')"}