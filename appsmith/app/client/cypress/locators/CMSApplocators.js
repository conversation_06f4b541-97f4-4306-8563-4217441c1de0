export default {
  selectGet: ".rc-select-item-option-content:contains('GET')",
  selectPost: ".rc-select-item-option-content:contains('POST')",
  selectDelete: "//span[text()='DELETE']",
  pagebutton: "//div[text()='Page1']",
  submitButton: "span:contains('Submit New Proposal')",
  mailButton: "span:contains('Mail')",
  sendMailText: "//span[text()='Send Mail']",
  inputMail: "//input[@value='<EMAIL>']",
  subjectField: "(//div[@class='bp3-input-group']//input)[6]",
  contentField: ".t--widget-inputwidgetv2",
  confirmButton: "span:contains('Confirm')",
  closeButton: "span:contains('Close')",
  datasourcesbutton: "//div[text()='Datasources']",
  postApi: "[data-testid='t--ide-tab-send_mail']",
  deleteButton: "//span[text()='Delete Proposal']",
  deleteTaskText: "//span[text()='Delete this task']",
};
