{"queryEditorIcon": ".t--nav-link-query-editor", "templateMenu": ".t--template-menu", "runQuery": "//span[text()='Run']/ancestor::button", "saveQuery": ".t--save-query", "deleteQuery": ".t--delete-query", "queryMoreAction": "[data-testid=t--more-action-trigger]", "deleteUsingContext": ".t--apiFormDeleteBtn", "addDatasource": ".t--add-datasource", "editDatasourceButton": ".t--edit-datasource", "switch": ".t--form-control-SWITCH input", "queryResponse": "(//div[@class='table']//div[@class='tr'])[2]//div[@class='td as-mask']", "querySelect": "//div[contains(@class, 't--template-menu')]//div[text()='Select']", "queryCreate": "//div[contains(@class, 't--template-menu')]//div[text()='Create']", "queryUpdate": "//div[contains(@class, 't--template-menu')]//div[text()='Update']", "queryDelete": "//div[contains(@class, 't--template-menu')]//div[text()='Delete']", "queryFromEmail": ".CodeMirror-lines", "codeTextArea": "//div[@class='CodeMirror-code']//span/span", "searchFilefield": "//input[@placeholder='Search File Prefix']", "copyURLicon": "//button/span[@icon='link']", "snipeableTable": "//input[@type='search']", "suggestedWidgetChart": ".t--suggested-widget-CHART_WIDGET", "queryTimeout": "//input[@name='actionConfiguration.timeoutInMillisecond']"}