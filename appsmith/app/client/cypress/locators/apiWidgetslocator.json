{"resourceUrl": ".t--dataSourceField", "searchInputPlaceholder": "//div[contains(@class, 't--dataSourceField')]//div//input", "searchApi": ".t--sidebar input[type=text]", "createapi": ".t--createBlankApiCard", "createAuthApiDatasource": ".t--createAuthApiDatasource", "popover": "button[data-testid='t--entity-context-menu-trigger']", "moveTo": ".single-select >div:contains('Move to')", "copyTo": ".single-select >div:contains('Copy to page')", "home": ".single-select >div:contains('Page1')", "delete": ".t--entity-context-menu > div.ads-v2-menu__menu-item.error-menuitem", "deleteConfirm": ".single-select >div:contains('Are you sure?')", "path": ".t--path >div textarea", "autoSuggest": "//div[contains(@id,'react-select')]", "headerKey": ".t--actionConfiguration\\.headers\\[0\\]\\.key\\.0", "headerValue": ".t--actionConfiguration\\.headers\\[0\\]\\.value\\.0", "autoGeneratedHeaderKey": ".t--autoGeneratedHeader-key-0", "autoGeneratedHeaderValue": ".t--autoGeneratedHeader-value-0", "queryKey": ".t--actionConfiguration\\.queryParameters\\[0\\]\\.key\\.0", "queryValue": ".t--actionConfiguration\\.queryParameters\\[0\\]\\.value\\.0", "formEncoded": ".t--actionConfiguration\\.bodyFormData\\[0\\]\\.key\\.0", "responseText": ".CodeMirror-line > [role='presentation']", "createApiOnSideBar": "button:contains('Create new API')", "saveButton": "button:contains('Save')", "addHeader": ".t--addApiHeader svg", "apidocumentaionLink": ".t--apiDocumentationLink", "postbody": "(//div[contains(@class,'CodeMirror-wrap')]//textarea)[2]", "paginationTab": "//span[text()='Pagination']", "apiInputTab": "li:contains('API Input')", "paginationOption": ".t--apiFormPaginationType div>input", "paginationWithTable": ".t--apiFormPaginationType label:contains('Paginate with Table Page No') input", "paginationWithUrl": ".t--apiFormPaginationType label:contains('Paginate with response URL') input", "panigationNextUrl": ".t--apiFormPaginationNext div>textarea", "panigationPrevUrl": ".t--apiFormPaginationPrev div>textarea", "TestNextUrl": ".t--apiFormPaginationNextTest", "TestPreUrl": ".t--apiFormPaginationPrevTest", "EditApiName": "img[alt='Edit pen']", "Request": "//li//span[text()='Request']", "RequestURL": "(//span[@class='bp3-tree-node-label']/span)[1]", "RequestMethod": "(//span[@class='bp3-tree-node-label']/span)[2]", "content-Type": "(//span[@class='bp3-tree-node-label']/span)[3]", "requestBody": "(//div[contains(@class,'bp3-collapse-body')]//textarea)[1]", "showrequest": "span:contains('Show Request')", "Responsetab": "//li//span[text()='Response Body']", "deleteAPI": ".t--apiFormDeleteBtn", "editName": ".single-select >div:contains('Rename')", "page": ".single-select >div", "propertyList": ".binding", "actionlist": ".action div div", "settings": "[data-testid='t--action-settings-trigger']", "headers": "span:contains('Headers')", "renameEntity": "div[role='menuitem']:contains('Rename')", "paramsTab": "//span[text()='Params']", "paramKey": ".t--actionConfiguration\\.queryParameters\\[0\\]\\.key\\.0", "paramValue": ".t--actionConfiguration\\.queryParameters\\[0\\]\\.value\\.0", "multipartTypeDropdown": "button:contains('Type')", "confirmBeforeExecute": "[name=confirmBeforeExecute]", "runQueryButton": "[data-testid=\"t--run-action\"]"}