{"imageWidget": ".t--draggable-imagewidget", "chartWidget": ".t--draggable-chartwidget", "chartType": ".t--property-control-charttype", "chartTypeText": ".t--property-control-charttype", "mapType": ".t--property-control-maptype .rc-select-selection-item", "destin": ".appsmith_widget_01ewdomru7", "mapWidget": ".t--draggable-mapwidget", "dropdownSelectButton": ".t--open-dropdown-Select", "dismissbutton": ".<PERSON><PERSON><PERSON><PERSON>", "search": "input[placeholder='Enter location']", "enablesearch": ".t--property-control-enablesearchlocation input", "enablepicklocation": ".t--property-control-enablepicklocation input", "enablecreatemarker": ".t--property-control-createnewmarker input", "zoom": ".t--property-control-zoomable input", "searchloc": "input[placeholder='Enter location to search']", "imagecontainer": ".t--draggable-imagewidget span.t--widget-name", "imageinner": ".t--draggable-imagewidget img", "chartInnerText": ".t--property-control-title", "inputChartValue": ".t--property-control-chartseries .CodeMirror textarea", "chartButton": ".t--property-control-chartseries button", "rectangleChart": ".t--draggable-chartwidget g", "fusionRectangleChart": ".t--draggable-chartwidget g rect", "xlabel": ".t--property-control-x-axislabel .CodeMirror-code", "ylabel": ".t--property-control-y-axislabel .CodeMirror-code", "mapInner": ".t--draggable-mapwidget span.t--widget-name", "mapInput": ".t--property-control-defaultmarkers .CodeMirror-code", "mapinitialloc": ".t--property-control-initiallocation input", "mapSearch": ".t--draggable-mapwidget input", "createMarker": ".t--property-control-createnewmarker [type='checkbox']", "zoomLevel": ".t--property-control-zoomlevel  svg", "sourceImage": ".t--property-control-image .CodeMirror-code", "defaultImage": ".t--property-control-defaultimage .CodeMirror textarea", "Chartlabel": ".t--draggable-chartwidget g text", "FusionChartlabel": ".t--draggable-chartwidget g:nth-child(5) text", "pickMyLocation": ".t--draggable-mapwidget div[title='Pick My Location']", "mapChartEntityLabels": ".t--draggable-mapchartwidget text", "listWidget": ".t--draggable-listwidget"}