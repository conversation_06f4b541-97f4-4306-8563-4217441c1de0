export default {
  gitRepoInput: ".t--git-repo-input input",
  generateDeployKeyBtn: "[data-testid=t--generate-deploy-ssh-key-button]",
  connectSubmitBtn: ".t--connect-submit-btn",
  createNewBranchButton: ".t--create-new-branch-button",
  createNewBranchSubmitbutton: ".t--create-new-branch-submit-button",
  branchButton: "[data-testid='t--branch-button-currentBranch']",
  commitCommentInput: ".t--commit-comment-input textarea",
  commitButton: ".t--commit-button",
  pullButton: ".t--pull-button",
  openRepoButton: ".t--open-repo-button",
  gitConfigNameInput: ".t--git-config-name-input input",
  gitConfigEmailInput: ".t--git-config-email-input input",
  branchSearchInput:
    ".t--branch-search-input .ads-v2-input__input-section-input",
  branchListItem: "[data-testid=t--branch-list-item]",
  closeGitSyncModal: ".ads-v2-modal__content-header-close-button",
  closeRevokeModal: ".t--close-disconnect-modal",
  gitSyncModal: "[data-testid='t--git-sync-modal']",
  bottomBarCommitButton: ".t--bottom-bar-commit",
  bottomBarMergeButton: ".t--bottom-bar-merge",
  bottomBarPullButton: ".t--bottom-bar-pull",
  mergeCTA: "[data-testid=t--git-merge-button]",
  loaderQuickGitAction: ".t--loader-quick-git-action",
  connetStatusbar: ".t--connect-statusbar",
  useGlobalGitConfig: "[data-testid=t--use-global-config-checkbox]",
  connectGitBottomBar: ".t--connect-git-bottom-bar",
  disabledConnectGitBottomBar: ".t--disabled-connect-git-bottom-bar",
  syncBranches: ".t--sync-branches",
  closeBranchList: ".t--close-branch-list",
  learnMoreDeployKey: "//span[text()='Learn more']",
  learnMoreSshUrl: ".t--learn-more-ssh-url",
  readDocument: ".t--read-document",
  gitConnectErrorLearnMore:
    ".t--git-connection-error .t--notification-banner-learn-more",
  deployPreview: ".t--git-deploy-preview",
  mergeButton: "[data-testid=t--git-merge-button]",
  disconnectIcon: ".t--git-disconnect-icon",
  disconnectGitModal: "[data-testid='t--disconnect-git-modal']",
  disconnectButton: ".t--git-revoke-button",
  closeDisconnectModal: ".ads-v2-modal__content-header-close-button",
  disconnectAppNameInput: ".t--git-app-name-input",
  repoLimitExceededErrorModal: ".t--git-repo-limited-modal",
  gitModalLink: ".ads-v2-link",
  connectedApplication: ".t--connected-app-wrapper",
  diconnectLink: ".t--disconnect-link",
  disconnectLearnMoreLink: ".t--disconnect-learn-more",
  learnMoreOnRepoLimitModal: ".t--learn-more-repo-limit-modal",
  gitSyncModalDeployTab: "[data-testid=t--tab-DEPLOY]",
  gitPullCount: ".t--bottom-bar-commit .count",
  gitConnectionContainer: "[data-testid=t--git-connection-container]",
  gitRemoteURLContainer: "[data-testid=t--remote-url-container]",
  discardChanges: ".t--discard-button",
  gitBranchContextMenu: ".git-branch-more-menu",
  gitBranchDelete: ".t--branch-more-menu-delete",
  SSHKeycontextmenu: ".git-sync-modal .ads-v2-button__content",
  regenerateSSHKeyECDSA: ".t--regenerate-sshkey-ECDSA",
  regenerateSSHKeyRSA: ".t--regenerate-sshkey-RSA",
  confirmButton: "//span[text()='Yes']",
  errorCallout: ".t--git-connection-error span",
  mergeConflicts:
    "//span[contains(text(), 'There are uncommitted changes present in your local branch master. Please commit them first and try again')]",
};
