{"NoApiMsg": "p:contains('No APIs yet.')", "NoQueryMsg": "p:contains('No DB Queries yet.')", "NoWidgetsMsg": "p:contains('icon above to add widgets')", "createQueryMenu": ".file-ops", "entityExplorer": ".t--entity-explorer", "popover": "//div[contains(@class,'t--entity page')]//*[local-name()='g' and @id='Icon/Outline/more-vertical']", "datsourceEntityPopover": "//div[contains(@class,'t--entity datasource')]//*[local-name()='g' and @id='Icon/Outline/more-vertical']", "editName": ".single-select >div:contains('Rename')", "deletePage": ".single-select >div:contains('Delete')", "refreshStructure": ".single-select >div:contains('Refresh')", "datasourceStructure": ".t--entity.datasourceStructure", "datasourceColumn": ".t--datasource-column", "collapse": ".t--entity-collapse-toggle", "property": ".language-appsmith-binding", "editNameField": ".editing input", "editEntityField": ".bp3-editable-text-input", "closeWidgets": ".t--close-widgets-sidebar", "dropHere": "#div-dragarena-0", "editEntity": ".t--entity-name input", "activeTab": "span:contains('Active')", "blankAPI": "span:contains('New blank API')"}