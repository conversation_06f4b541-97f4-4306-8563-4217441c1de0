{"addNewtable": "//span[text()='New Table']", "inputValues": "//div[@class='bp3-input-group']", "addTablename": "(//div[@class='bp3-input-group']//input)[2]", "addColumn": "//span[text()='Add Column']/parent::button/parent::div", "addColumnName": "div[data-testid='input-container']", "textField": "//span[text()='Text']", "selectDatatype": "//div[text()='Varchar']", "submitButton": "//span[text()='Submit']", "closeButton": "//span[text()='Close']", "viewButton": "//span[text()='View']", "dropdownChevronDown": ".t--draggable-dropdownwidget", "selectInformationSchema": "//div[text()='information_schema']", "deleteButton": "//span[text()='Delete']", "confirmButton": "//span[text()='Confirm']", "columnNamefield": "//span[text()='Column name']", "datatypefield": "//span[text()='Data Type']", "bookname": "(//div[@class='bp3-input-group'])[5]", "bookgenre": "(//div[@class='bp3-input-group'])[6]", "bookprice": "(//div[@class='bp3-input-group'])[7]", "bookquantity": "(//div[@class='bp3-input-group'])[8]", "addtoCart": "//span[@class='bp3-button-text' and text()='Submit']", "deletefromCart": "//span[@class='bp3-button-text' and text()='Delete']", "editbookquantity": "(//div[@class='bp3-input-group'])[4]", "addButton": "//span[text()='Add']/parent::button", "editButton": "//span[text()='Edit']/parent::button", "loadButton": ".bp3-button.bp3-loading"}