{"checkboxWidget": ".t--draggable-checkboxwidget", "selectwidget": ".t--draggable-selectwidget", "dropdownWidget": ".t--draggable-selectwidget", "menuButtonWidget": ".t--draggable-menubuttonwidget", "multiselectwidgetv2": ".t--draggable-multiselectwidgetv2", "multiselectWidgetv2search": ".multi-select-dropdown .bp3-input", "multiselecttreeWidget": ".t--draggable-multiselecttreewidget", "singleselecttreeWidget": ".t--draggable-singleselecttreewidget", "dropdownSelectionType": ".t--property-control-selectiontype .bp3-popover-target", "radioWidget": ".t--draggable-radiogroupwidget", "checkboxGroupWidget": ".t--draggable-checkboxgroupwidget", "selectAllCheckboxControl": ".bp3-control.bp3-checkbox.whitespace-nowrap", "checkboxGroupOptionInputs": ".bp3-control.bp3-checkbox.whitespace-nowrap ~ .bp3-control.bp3-checkbox", "switchGroupWidget": ".t--draggable-switchgroupwidget", "radioOnSelectionChangeDropdown": ".t--property-control-onselectionchange", "nextDayBtn": ".DayPicker-Day[aria-selected='true'] + div.DayPicker-Day", "datepickerWidget": ".t--draggable-datepic<PERSON>widget", "defaultDate": ".t--property-control-defaultdate input", "minDate": ".t--property-control-mindate input", "maxDate": ".t--property-control-maxdate input", "filepickerWidget": ".t--draggable-filepickerwidget", "formWidget": ".t--draggable-formwidget", "richTextEditorWidget": ".t--draggable-rich<PERSON>ed<PERSON><PERSON>dget", "richEditorOnTextChange": ".t--property-control-ontextchange", "optionvalue": ".t--property-control-defaultselectedvalue .kDwnRc", "defselected": ".CodeMirror textarea", "dropdowninner": ".bp3-button > .bp3-button-text", "Textinput": ".t--property-control-options .CodeMirror-code", "labelvalue": ".t--draggable-selectwidget label", "dropdownInput": ".bp3-tag-input-values", "selectButton": ".select-button", "multiSelect": ".rc-select-selector", "mulitiselectInput": ".rc-select-selection-search-input", "treeSelectInput": ".rc-tree-select-selection-search-input", "multiTreeSelect": ".rc-tree-select-selector", "treeSelect": ".rc-tree-select-selector", "treeSelectClearAll": ".rc-tree-select-clear", "treeSelectPlaceholder": ".rc-tree-select-single .rc-tree-select-selection-placeholder", "treeSelectFilterInput": ".tree-select-dropdown .bp3-input", "multiTreeSelectFilterInput": ".tree-multiselect-dropdown .bp3-input", "labelradio": ".t--draggable-radiogroupwidget label", "labelCheckboxGroup": ".t--draggable-checkboxgroupwidget label", "labelSwitchGroup": ".t--draggable-switchgroupwidget label", "deleteradiovalue": ".t--property-control-options svg.remixicon-icon ", "inputRadio": ".t--draggable-radiogroupwidget input", "defaultSelect": ".t--property-control-defaultselectedvalue .CodeMirror-code", "defaultSelectValues": ".t--property-control-defaultselectedvalues .CodeMirror-code", "formInner": ".t--draggable-formwidget span.t--widget-name", "radioInput": ".t--draggable-radiogroupwidget span.t--widget-name", "checkboxGroupInput": ".t--draggable-checkboxgroupwidget span.t--widget-name", "switchGroupInput": ".t--draggable-switchgroupwidget span.t--widget-name", "radioAddButton": ".t--property-control-options button.t--property-control-options-add", "formD": ".t--widget-formwidget div.style-container", "datepickerFooter": ".bp3-datepicker-footer span", "datepickerFooterPublish": ".bp3-datepicker-footer span", "disableJs": ".t--property-control-disabled input[type='checkbox']", "requiredJs": ".t--property-control-required input[type='checkbox']", "switchWidget": ".t--draggable-switchwidget", "toggleJsDefaultDate": ".t--property-control-defaultdate .t--js-toggle", "toggleJsMinDate": ".t--property-control-mindate .t--js-toggle", "toggleJsMaxDate": ".t--property-control-maxdate .t--js-toggle", "exploreWidget": "[class$=bp3-panel-stack-view] > div:nth-child(1) > div:nth-child(1) > span:nth-child(4)", "widgetRelatedDocument": "div.main > div:nth-child(1) > div:nth-child(1)", "dropdowonChevranDown": ".t--widget-selectwidget .bp3-icon-chevron-down", "dropdownDefaultButton": ".t--widget-selectwidget .bp3-button", "toggleVisible": ".t--property-control-visible .t--js-toggle", "inputToggleVisible": "div.t--property-control-visible div.CodeMirror-lines", "selectWidget": ".t--widget-selectwidget", "multiSelectWidget": ".t--widget-multiselectwidgetv2", "multiSelectWidgetSearch": ".multi-select-dropdown .bp3-input", "filterCheckbox": ".t--property-control-filterable input[type=checkbox]", "searchBoxDropdown": ".select-popover-wrapper div.bp3-input-group", "NavHomePage": "[data-testid='t--default-home-icon']", "apiCallToast": "div.Toastify__toast-body", "toggleOnOptionChange": ".t--property-control-onoptionchange .t--js-toggle", "toggleButtonVariant": ".t--property-control-buttonvariant .t--js-toggle", "minDateTextArea": ".t--property-control-mindate  .CodeMirror textarea", "minDateInput": ".t--property-control-mindate .ads-v2-input__input-section-input", "datePickerInput": ".t--widget-datepickerwidget2 .bp3-input", "dayPickerNextButton": ".DayPicker-NavButton--next", "dayPickerToday": ".ads-v2-datepicker__calender-today"}