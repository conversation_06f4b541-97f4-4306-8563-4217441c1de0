{"templatesTab": ".t--templates-tab", "templateForkButton": ".t--fork-template", "dialogForkButton": ".t--fork-template-button", "templateCard": "[data-testid='template-card']", "templateViewForkButton": "[data-testid='template-fork-button']", "startFromTemplateCard": "[data-testid=start-from-template]", "templateDialogBox": "[data-testid=t--templates-dialog-component]", "selectCheckbox": ".ads-v2-checkbox", "closeButton": "//button[@aria-label='Close']//span", "marketingDashboard": "//h1[text()='Marketing Dashboard']", "vehicleMaintenenceApp": "//h1[text()='Vehicle Maintenance App']", "applicationTrackerDashboard": "//h1[text()='Applicant Tracker-test']", "startFromTemplateOnboardingCard": "[data-testid=t--start-from-template]", "templateLoadState": "div.template-content:not(:empty)", "selectAllPages": "label:contains('Select all') > input[type='checkbox']"}