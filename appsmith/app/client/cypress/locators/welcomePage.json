{"firstName": ".t--welcome-form-first-name input", "lastName": ".t--welcome-form-last-name input", "email": ".t--welcome-form-email input", "password": ".t--welcome-form-password input", "verifyPassword": ".t--welcome-form-verify-password input", "proficiencyGroupButton": "[data-testid='t--user-proficiency'] button", "useCaseGroupButton": "[data-testid='t--user-use-case'] button", "continueButton": ".t--welcome-form-continue-button", "submitButton": ".t--welcome-form-submit-button", "dataCollection": ".t--welcome-form-datacollection", "newsLetter": ".t--welcome-form-newsletter", "createButton": ".t--welcome-form-create-button", "createSuperUser": "#super-user-form", "superUserForm": "[data-testid='super-user-form']"}