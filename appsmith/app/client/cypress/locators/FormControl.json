{"dropdownWrapper": ".rc-virtual-list .rc-select-item-option div", "dropdownOption": "[data-testid='t--dropdown-option-*']", "removeMultiSelectOptionWithIcon": ".t--remove-option-*", "commandDropdown": ".t--actionConfiguration\\.formData\\.command\\.data", "sortingColumn": ".t--actionConfiguration\\.formData\\.sortBy\\.data*\\.column", "sortingOrder": ".t--actionConfiguration\\.formData\\.sortBy\\.data*\\.order", "sortingAddField": "[data-testid=t--sorting-add-field]", "sortingDeleteField": "[data-testid=t--sorting-delete-*]", "sheetUrlDropdown": ".t--actionConfiguration\\.formData\\.sheetUrl\\.data", "sheetNameDropdown": ".t--actionConfiguration\\.formData\\.sheetName\\.data", "projectionDropdown": ".t--actionConfiguration\\.formData\\.projection\\.data", "paginationLimit": ".t--actionConfiguration\\.formData\\.pagination\\.data\\.limit", "paginationOffset": ".t--actionConfiguration\\.formData\\.pagination\\.data\\.offset", "tableHeaderIndex": ".t--actionConfiguration\\.formData\\.tableHeaderIndex\\.data", "JSModeSortingControl": ".t--actionConfiguration\\.formData\\.sortBy\\.data", "JSModeWhereClauseControl": ".t--actionConfiguration\\.formData\\.where\\.data", "whereClauseCondition": ".t--actionConfiguration\\.formData\\.where\\.data\\.condition", "nestedWhereClauseCondition": ".t--actionConfiguration\\.formData\\.where\\.data\\.children*\\.children+\\.children=\\.children?\\.condition", "nestedWhereClauseKey": ".t--actionConfiguration\\.formData\\.where\\.data\\.children*\\.children+\\.children=\\.children?\\.key", "nestedWhereClauseValue": ".t--actionConfiguration\\.formData\\.where\\.data\\.children*\\.children+\\.children=\\.children?\\.value", "whereClauseAddCondition": ".t--where-add-condition*", "whereClauseAddGroupCondition": ".t--where-add-group-condition*", "whereClauseDeleteField": "[data-testid=t--where-clause-delete-*]", "rowIndexInput": ".t--actionConfiguration\\.formData\\.rowIndex\\.data", "mongoFindQuery": ".t--actionConfiguration\\.formData\\.find\\.query\\.data", "mongoFindSort": ".t--actionConfiguration\\.formData\\.find\\.sort\\.data", "mongoFindLimit": ".t--actionConfiguration\\.formData\\.find\\.limit\\.data", "mongoFindSkip": ".t--actionConfiguration\\.formData\\.find\\.skip\\.data", "mongoFindProjection": ".t--actionConfiguration\\.formData\\.find\\.projection\\.data", "mongoCollection": ".t--actionConfiguration\\.formData\\.collection\\.data", "mongoCountQuery": ".t--actionConfiguration\\.formData\\.count\\.query\\.data", "mongoDistinctQuery": ".t--actionConfiguration\\.formData\\.distinct\\.query\\.data", "mongoDistinctKey": ".t--actionConfiguration\\.formData\\.distinct\\.key\\.data", "mongoAggregateArrayOfPipelines": ".t--actionConfiguration\\.formData\\.aggregate\\.arrayPipelines\\.data", "mongoInsertDocuments": ".t--actionConfiguration\\.formData\\.insert\\.documents\\.data", "mongoDeleteLimitDropdown": ".t--actionConfiguration\\.formData\\.delete\\.limit\\.data", "mongoDeleteDocumentsQuery": ".t--actionConfiguration\\.formData\\.delete\\.query\\.data", "mongoUpdateManyQuery": ".t--actionConfiguration\\.formData\\.updateMany\\.query\\.data", "mongoUpdateManyUpdate": ".t--actionConfiguration\\.formData\\.updateMany\\.update\\.data", "mongoUpdateManyLimitDropdown": ".t--actionConfiguration\\.formData\\.updateMany\\.limit\\.data", "s3BucketName": ".t--actionConfiguration\\.formData\\.bucket\\.data", "s3FilePath": ".t--actionConfiguration\\.formData\\.path\\.data", "s3CreateFileDataType": ".t--actionConfiguration\\.formData\\.create\\.dataType\\.data", "s3ListPrefix": ".t--actionConfiguration\\.formData\\.list\\.prefix\\.data", "s3ListSignedUrl": ".t--actionConfiguration\\.formData\\.list\\.signedUrl\\.data", "s3ListUnSignedUrl": ".t--actionConfiguration\\.formData\\.list\\.unSignedUrl\\.data", "s3ReadFileDataType": ".t--actionConfiguration\\.formData\\.read\\.dataType\\.data", "postgreSqlBody": ".t--actionConfiguration\\.body", "rawBody": ".t--actionConfiguration\\.formData\\.body\\.data"}