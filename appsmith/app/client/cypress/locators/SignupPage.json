{"username": "input[name='email']", "password": "input[name='password']", "submitBtn": "button[type='submit']", "proficiencyGroupButton": "[data-testid='t--user-proficiency'] button", "useCaseGroupButton": "[data-testid='t--user-use-case'] button", "dropdownOption": ".rc-select-item-option:first", "getStartedSubmit": ".t--get-started-button", "forgetPasswordLink": "[href='/user/forgotPassword']", "signupLink": ".t--signup-link"}