const commonlocators = require("../../../../../locators/commonlocators.json");
import * as _ from "../../../../../support/Objects/ObjectsCore";

describe(
  "Binding the list widget with text widget",
  { tags: ["@tag.Widget", "@tag.List", "@tag.Binding"] },
  function () {
    before(() => {
      _.agHelper.AddDsl("Listv2/simpleListVulnerability");
    });

    it("1. Validate that list widget doesn't execute code", function () {
      // First input
      cy.get(".t--widget-inputwidgetv2 input")
        .eq(0)
        .type("'+(function() { return 3; })()+'", {
          parseSpecialCharSequences: false,
        });
      cy.wait(1000);
      cy.get(".t--widget-buttonwidget").eq(0).click();
      cy.get(commonlocators.toastmsg).contains(
        "'+(function() { return 3; })()+'",
      );

      // First input
      cy.get(".t--widget-inputwidgetv2 input")
        .eq(0)
        .clear()
        .type("`+(function() { return 3; })()+`", {
          parseSpecialCharSequences: false,
        });
      cy.wait(1000);
      cy.get(".t--widget-buttonwidget").eq(0).click();
      cy.get(commonlocators.toastmsg).should(
        "contain",
        "`+(function() { return 3; })()+`",
      );

      // First input
      cy.get(".t--widget-inputwidgetv2 input")
        .eq(0)
        .clear()
        .type('"+(function() { return 3; })()+"', {
          parseSpecialCharSequences: false,
        });
      cy.wait(1000);
      cy.get(".t--widget-buttonwidget").eq(0).click();
      cy.get(commonlocators.toastmsg).should(
        "contain",
        '"+(function() { return 3; })()+"',
      );
    });
  },
);
